#!/usr/bin/env python3
"""
Test script to verify the new report structure and LLM logging functionality
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from shared.llm_logger import initialize_logger, get_logger
from shared.logged_azure_openai import LoggedAzureOpenAI
from shared.config import get_llm_config

def test_llm_logger():
    """Test LLM logging functionality"""
    print("🧪 Testing LLM Logger...")
    print("-" * 50)
    
    # Initialize logger
    logger = initialize_logger(
        target_url="https://example.com",
        vulnerability_type="SQLI"
    )
    
    # Simulate some interactions
    logger.log_interaction(
        model="gpt-4o",
        provider="azure_openai",
        prompt="Test prompt for SQL injection assessment",
        response="Test response with vulnerability findings",
        input_tokens=100,
        output_tokens=200,
        total_tokens=300,
        response_time=2.5,
        agent_name="Lead Agent",
        task_description="Initial reconnaissance"
    )
    
    logger.log_interaction(
        model="gpt-4o",
        provider="azure_openai",
        prompt="Follow-up prompt for detailed analysis",
        response="Detailed analysis response",
        input_tokens=150,
        output_tokens=250,
        total_tokens=400,
        response_time=3.2,
        agent_name="SQLI Agent",
        task_description="SQL injection testing"
    )
    
    # Finalize session
    logger.finalize_session()
    
    # Get summary
    summary = logger.get_session_summary()
    logs = logger.get_detailed_logs()
    
    print(f"✅ Session ID: {summary['session_id']}")
    print(f"✅ Total Interactions: {summary['total_interactions']}")
    print(f"✅ Total Tokens: {summary['total_tokens']}")
    print(f"✅ Total Cost: ${summary['total_cost']:.4f}")
    print(f"✅ Detailed Logs: {len(logs)} entries")
    
    return True

def test_report_structure():
    """Test the new report structure"""
    print("\n📄 Testing Report Structure...")
    print("-" * 50)
    
    # Initialize logger for testing
    logger = initialize_logger(
        target_url="https://testsite.com",
        vulnerability_type="XSS"
    )
    
    # Add some test interactions
    logger.log_interaction(
        model="gpt-4o",
        provider="azure_openai",
        prompt="Analyze this website for XSS vulnerabilities",
        response="Found potential XSS in search parameter",
        input_tokens=200,
        output_tokens=300,
        total_tokens=500,
        response_time=4.1,
        agent_name="XSS Agent"
    )
    
    logger.finalize_session()
    
    # Create mock report data
    mock_report = {
        "vulnerability_type": "XSS",
        "report": """
        XSS Vulnerability Assessment Report
        
        Target: https://testsite.com
        
        Findings:
        1. Reflected XSS in search parameter
        2. Stored XSS in comment field
        
        Payloads tested:
        - <script>alert('XSS')</script>
        - javascript:alert(1)
        
        Evidence:
        - GET /search?q=<script>alert('XSS')</script>
        - Response contained unescaped script tag
        """
    }
    
    # Import the save_report function
    from main import save_report
    
    # Test report generation
    test_output_file = "test_reports/test_report.json"
    save_report(mock_report, test_output_file)
    
    # Verify the report was created and has correct structure
    if Path(test_output_file).exists():
        with open(test_output_file, 'r') as f:
            report_data = json.load(f)
        
        # Check required fields
        required_fields = [
            "scan_metadata",
            "vulnerabilities", 
            "llm_analytics",
            "detailed_llm_logs",
            "evidence"
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in report_data:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
        
        # Check scan_metadata structure
        metadata = report_data["scan_metadata"]
        metadata_fields = ["scan_id", "timestamp", "target_url", "vulnerability_type"]
        for field in metadata_fields:
            if field not in metadata:
                print(f"❌ Missing metadata field: {field}")
                return False
        
        # Check llm_analytics structure
        analytics = report_data["llm_analytics"]
        analytics_fields = [
            "model", "provider", "total_interactions", 
            "total_input_tokens", "total_output_tokens", 
            "total_cost_usd", "cost_breakdown"
        ]
        for field in analytics_fields:
            if field not in analytics:
                print(f"❌ Missing analytics field: {field}")
                return False
        
        # Check cost breakdown
        cost_breakdown = analytics["cost_breakdown"]
        cost_fields = [
            "input_cost_per_1m_tokens", "output_cost_per_1m_tokens",
            "total_input_cost", "total_output_cost"
        ]
        for field in cost_fields:
            if field not in cost_breakdown:
                print(f"❌ Missing cost breakdown field: {field}")
                return False
        
        print("✅ Report structure validation passed!")
        print(f"✅ Report saved to: {test_output_file}")
        print(f"✅ Vulnerabilities found: {len(report_data['vulnerabilities'])}")
        print(f"✅ LLM interactions logged: {analytics['total_interactions']}")
        print(f"✅ Total cost: ${analytics['total_cost_usd']:.4f}")
        
        return True
    else:
        print("❌ Report file was not created")
        return False

def display_sample_report():
    """Display a sample of the new report structure"""
    print("\n📋 Sample Report Structure:")
    print("-" * 50)
    
    sample_structure = {
        "scan_metadata": {
            "scan_id": "session_1234567890",
            "timestamp": "2025-07-31T12:00:00",
            "target_url": "https://example.com",
            "vulnerability_type": "SQLI",
            "scan_duration": "0:05:23",
            "tool_version": "PentestAI v1.0"
        },
        "vulnerabilities": [
            {
                "type": "SQL Injection",
                "severity": "High",
                "description": "SQL Injection vulnerability detected",
                "evidence": {
                    "payloads": ["1' OR '1'='1", "1; DROP TABLE users--"],
                    "requests": ["GET /search?id=1' OR '1'='1"],
                    "responses": ["HTTP/1.1 200 OK"]
                },
                "status": "Confirmed"
            }
        ],
        "llm_analytics": {
            "model": "gpt-4o",
            "provider": "azure_openai",
            "total_interactions": 5,
            "total_input_tokens": 1500,
            "total_output_tokens": 2000,
            "total_tokens": 3500,
            "total_cost_usd": 0.0375,
            "average_response_time": 3.2,
            "cost_breakdown": {
                "input_cost_per_1m_tokens": 2.50,
                "output_cost_per_1m_tokens": 10.0,
                "total_input_cost": 0.00375,
                "total_output_cost": 0.02
            }
        },
        "detailed_llm_logs": "... (detailed interaction logs)",
        "evidence": {
            "raw_report": "... (original report content)",
            "prompts_used": ["... (all prompts sent to LLM)"],
            "responses_received": ["... (all responses from LLM)"]
        }
    }
    
    print(json.dumps(sample_structure, indent=2))

if __name__ == "__main__":
    print("🎯 PentestAI Report Structure & LLM Logging Test")
    print("=" * 60)
    
    # Test LLM logger
    logger_ok = test_llm_logger()
    
    # Test report structure
    report_ok = test_report_structure()
    
    # Display sample structure
    display_sample_report()
    
    if logger_ok and report_ok:
        print("\n🎉 All tests passed! New report structure is working correctly.")
        print("\nNew features implemented:")
        print("✅ Comprehensive vulnerability extraction")
        print("✅ LLM interaction logging with token tracking")
        print("✅ Cost calculation (Input: $2.50/1M tokens, Output: $10/1M tokens)")
        print("✅ Detailed evidence collection")
        print("✅ Structured JSON report format")
        print("✅ Session management and analytics")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
