#!/usr/bin/env python3
"""
PentestAI - Automated Penetration Testing System
Entry point for coordinated multi-agent penetration testing
"""
from typing import Any
from agno.models.openai import OpenAIChat

from agno.agent import Agent
from agno.models.azure import AzureOpenAI
import argparse
import json
import sys
import os
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import timedelta, datetime
from dotenv import load_dotenv
# from agno.models.azure import AzureOpenAI  # Using LoggedAzureOpenAI instead
from shared.config import get_llm_config
from shared.llm_logger import initialize_logger, get_logger
from shared.logged_azure_openai import LoggedAzureOpenAI
from agno.tools.mcp import MCPTools, StreamableHTTPClientParams
from agents.leadAgent import lead_agent

# Load environment variables from .env file
load_dotenv(Path(__file__).parent / '.env')


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="PentestAI - Automated Penetration Testing System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --url https://example.com --vuln SQLI
  python main.py --url https://example.com --vuln XSS --info '{"auth_required": true, "login_path": "/login"}'
  python main.py --url https://example.com --vuln SQLI --info '["login form", "search functionality"]'
        """
    )
    
    parser.add_argument(
        "--url", 
        required=True, 
        help="Target website URL to test (e.g., https://example.com)"
    )
    
    parser.add_argument(
        "--vuln", 
        choices=["XSS", "SQLI", "ALL"], 
        required=True,
        help="Vulnerability type to target: XSS, SQLI, or ALL"
    )
    
    parser.add_argument(
        "--info", 
        type=str,
        help="Additional information about target as JSON array/object (e.g., authentication details, specific forms to test)"
    )
    
    parser.add_argument(
        "--model", 
        default="gpt-4o", 
        help="Language model to use (default: gpt-4o)"
    )
    
    parser.add_argument(
        "--output",
        help="Output file to save the vulnerability report (default: auto-generated in reports/ directory)"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose output"
    )
    
    return parser.parse_args()


def validate_url(url: str) -> str:
    """Validate and normalize the target URL."""
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Basic URL validation
    if not any(tld in url for tld in ['.com', '.org', '.net', '.edu', '.gov', '.io', '.co']):
        print(f"Warning: URL '{url}' may not be valid")
    
    return url


def parse_additional_info(info_str: Optional[str]) -> Optional[Dict | List]:
    """Parse additional information from JSON string."""
    if not info_str:
        return None
    
    try:
        return json.loads(info_str)
    except json.JSONDecodeError as e:
        print(f"Error parsing additional info JSON: {e}")
        print(f"Provided: {info_str}")
        sys.exit(1)


async def run_penetration_test(url: str, vulnerability_type: str, additional_info: Optional[Dict | List], model_name: str):
    """Run the penetration testing with the specified parameters."""

    print(f"🎯 Starting PentestAI Mission")
    print(f"   Target URL: {url}")
    print(f"   Vulnerability: {vulnerability_type}")
    print(f"   Model: {model_name}")
    if additional_info:
        print(f"   Additional Info: {additional_info}")
    print("-" * 60)

    # Initialize LLM logging
    logger = initialize_logger(
        target_url=url,
        vulnerability_type=vulnerability_type
    )

    # Initialize the language model using Azure OpenAI with logging
    llm_config = get_llm_config()

    if llm_config.provider == "azure_openai":
        model = LoggedAzureOpenAI(
            id=llm_config.model,  # This is the deployment name in Azure
            api_key=llm_config.api_key,
            azure_endpoint=llm_config.endpoint,
            api_version=llm_config.api_version,
            agent_name="Lead Agent"
        )
    else:
        raise ValueError(f"Unsupported LLM provider: {llm_config.provider}. Please set LLM_PROVIDER=azure_openai in your .env file")

    # Configure MCP server connection
    server_params = StreamableHTTPClientParams(
        url="http://localhost:8931/mcp",
        timeout=timedelta(seconds=300),
        sse_read_timeout=timedelta(seconds=300)
    )
    
    print("🔧 Connecting to MCP server via streamable HTTP...")

    # Create MCP connection with proper async context management
    async with MCPTools(
        server_params=server_params,
        transport="streamable-http",
        timeout_seconds=300
    ) as mcp_tools:
        
        if vulnerability_type == "ALL":
            # Test both XSS and SQLI
            vulnerabilities = ["XSS", "SQLI"]
            all_reports = []
            
            for vuln in vulnerabilities:
                print(f"\n🔍 Testing for {vuln} vulnerabilities...")
                print("=" * 60)
                
                # Create team for this vulnerability type (within MCP context)
                team = await lead_agent(model, url, vuln, additional_info, mcp_tools)
                
                # Execute the mission
                mission_prompt = f"""
                Execute a comprehensive {vuln} vulnerability assessment on {url}.
                
                Mission Objectives:
                1. Perform systematic reconnaissance of the target website
                2. Identify potential {vuln} injection points
                3. Test identified vectors for {vuln} vulnerabilities
                4. Document any successful exploits with evidence
                5. Generate a comprehensive vulnerability report
                
                Additional Context: {additional_info or 'None provided'}
                """
                
                try:
                    report = await team.arun(mission_prompt)
                    all_reports.append({
                        "vulnerability_type": vuln,
                        "report": report
                    })
                except Exception as e:
                    print(f"❌ Error during {vuln} testing: {e}")
                    all_reports.append({
                        "vulnerability_type": vuln,
                        "error": str(e)
                    })
            
            # Finalize logging session for multiple vulnerabilities
            logger = get_logger()
            if logger:
                logger.finalize_session()

            return all_reports

        else:
            # Test single vulnerability type
            print(f"\n🔍 Testing for {vulnerability_type} vulnerabilities...")
            print("=" * 60)

            # Create the penetration testing team (within MCP context)
            team = await lead_agent(model, url, vulnerability_type, additional_info, mcp_tools)

            # Execute the mission
            mission_prompt = f"""
            Execute a comprehensive {vulnerability_type} vulnerability assessment on {url}.

            Mission Objectives:
            1. Perform systematic reconnaissance of the target website
            2. Identify potential {vulnerability_type} injection points
            3. Test identified vectors for {vulnerability_type} vulnerabilities
            4. Document any successful exploits with evidence
            5. Generate a comprehensive vulnerability report

            Additional Context: {additional_info or 'None provided'}
            """

            try:
                report = await team.arun(mission_prompt)

                # Finalize logging session
                logger = get_logger()
                if logger:
                    logger.finalize_session()

                return {
                    "vulnerability_type": vulnerability_type,
                    "report": report
                }
            except Exception as e:
                print(f"❌ Error during {vulnerability_type} testing: {e}")

                # Finalize logging session even on error
                logger = get_logger()
                if logger:
                    logger.finalize_session()

                return {
                    "vulnerability_type": vulnerability_type,
                    "error": str(e)
                }


def extract_vulnerabilities_from_report(report_content: str) -> List[Dict[str, Any]]:
    """Extract structured vulnerability information from report content."""
    vulnerabilities = []

    try:
        # Convert report content to string if it's not already
        content = str(report_content)

        # Look for vulnerability indicators
        vuln_indicators = {
            "SQL Injection": ["sql injection", "sqli", "union", "boolean-based", "time-based"],
            "Cross-Site Scripting": ["xss", "cross-site scripting", "script injection"],
            "Authentication Bypass": ["authentication", "login", "bypass"],
            "Information Disclosure": ["information disclosure", "sensitive data", "credentials"]
        }

        evidence_patterns = {
            "payloads": r'payload[s]?[:\s]*([^\n]+)',
            "requests": r'GET|POST|PUT|DELETE\s+[^\s]+\s+HTTP/[\d\.]+',
            "responses": r'HTTP/[\d\.]+\s+\d+\s+[^\n]+',
            "errors": r'error[s]?[:\s]*([^\n]+)',
            "urls": r'https?://[^\s]+',
            "parameters": r'(?:id|name|search|query|input)=\w+'
        }

        for vuln_type, indicators in vuln_indicators.items():
            if any(indicator.lower() in content.lower() for indicator in indicators):
                # Extract evidence for this vulnerability
                evidence = {}
                for evidence_type, pattern in evidence_patterns.items():
                    import re
                    matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                    if matches:
                        evidence[evidence_type] = matches[:5]  # Limit to first 5 matches

                # Extract severity based on keywords
                severity = "Medium"
                if any(word in content.lower() for word in ["critical", "high", "severe"]):
                    severity = "High"
                elif any(word in content.lower() for word in ["confirmed", "exploitable", "vulnerable"]):
                    severity = "High"
                elif any(word in content.lower() for word in ["low", "minor", "informational"]):
                    severity = "Low"

                vulnerability = {
                    "type": vuln_type,
                    "severity": severity,
                    "description": f"{vuln_type} vulnerability detected",
                    "evidence": evidence,
                    "status": "Confirmed" if evidence else "Potential"
                }

                vulnerabilities.append(vulnerability)

        # If no specific vulnerabilities found, create a generic entry
        if not vulnerabilities:
            vulnerabilities.append({
                "type": "General Assessment",
                "severity": "Informational",
                "description": "Security assessment completed",
                "evidence": {"report": [content[:500] + "..." if len(content) > 500 else content]},
                "status": "Completed"
            })

    except Exception as e:
        print(f"⚠️  Warning: Could not extract vulnerabilities: {e}")
        vulnerabilities.append({
            "type": "Assessment Error",
            "severity": "Unknown",
            "description": f"Error processing report: {str(e)}",
            "evidence": {},
            "status": "Error"
        })

    return vulnerabilities

def save_report(report, output_file: str):
    """Save the comprehensive vulnerability report with LLM logging data."""
    try:
        # Create reports directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)

        # Get LLM logging data
        logger = get_logger()
        llm_summary = logger.get_session_summary() if logger else {}
        llm_logs = logger.get_detailed_logs() if logger else []

        # Extract target URL from logger or report
        target_url = llm_summary.get("target_url", "Unknown")
        vulnerability_type = llm_summary.get("vulnerability_type", "Unknown")

        # Structure the comprehensive report
        if isinstance(report, list):
            # Multiple vulnerability types
            all_vulnerabilities = []
            for vuln_report in report:
                if isinstance(vuln_report, dict) and "report" in vuln_report:
                    vulnerabilities = extract_vulnerabilities_from_report(str(vuln_report["report"]))
                    for vuln in vulnerabilities:
                        vuln["scan_type"] = vuln_report.get("vulnerability_type", "Unknown")
                    all_vulnerabilities.extend(vulnerabilities)

            structured_report = {
                "scan_metadata": {
                    "scan_id": llm_summary.get("session_id", f"scan_{int(time.time())}"),
                    "timestamp": datetime.now().isoformat(),
                    "target_url": target_url,
                    "vulnerability_types_tested": [r.get("vulnerability_type", "Unknown") for r in report],
                    "total_vulnerability_types": len(report),
                    "scan_duration": _calculate_scan_duration(llm_summary),
                    "tool_version": "PentestAI v1.0"
                },
                "vulnerabilities": all_vulnerabilities,
                "llm_analytics": {
                    "model": llm_logs[0]["model"] if llm_logs else "Unknown",
                    "provider": llm_logs[0]["provider"] if llm_logs else "Unknown",
                    "total_interactions": llm_summary.get("total_interactions", 0),
                    "total_input_tokens": llm_summary.get("total_input_tokens", 0),
                    "total_output_tokens": llm_summary.get("total_output_tokens", 0),
                    "total_tokens": llm_summary.get("total_tokens", 0),
                    "total_cost_usd": llm_summary.get("total_cost", 0.0),
                    "average_response_time": llm_summary.get("average_response_time", 0.0),
                    "cost_breakdown": {
                        "input_cost_per_1m_tokens": 2.50,
                        "output_cost_per_1m_tokens": 10.0,
                        "total_input_cost": sum(log.get("input_cost", 0) for log in llm_logs),
                        "total_output_cost": sum(log.get("output_cost", 0) for log in llm_logs)
                    }
                },
                "detailed_llm_logs": llm_logs,
                "evidence": {
                    "raw_reports": [r.get("report", str(r)) for r in report],
                    "prompts_used": [log.get("prompt", "") for log in llm_logs],
                    "responses_received": [log.get("response", "") for log in llm_logs]
                }
            }

        else:
            # Single vulnerability type
            vulnerabilities = extract_vulnerabilities_from_report(str(report.get("report", report)))

            structured_report = {
                "scan_metadata": {
                    "scan_id": llm_summary.get("session_id", f"scan_{int(time.time())}"),
                    "timestamp": datetime.now().isoformat(),
                    "target_url": target_url,
                    "vulnerability_type": vulnerability_type,
                    "scan_duration": _calculate_scan_duration(llm_summary),
                    "tool_version": "PentestAI v1.0"
                },
                "vulnerabilities": vulnerabilities,
                "llm_analytics": {
                    "model": llm_logs[0]["model"] if llm_logs else "Unknown",
                    "provider": llm_logs[0]["provider"] if llm_logs else "Unknown",
                    "total_interactions": llm_summary.get("total_interactions", 0),
                    "total_input_tokens": llm_summary.get("total_input_tokens", 0),
                    "total_output_tokens": llm_summary.get("total_output_tokens", 0),
                    "total_tokens": llm_summary.get("total_tokens", 0),
                    "total_cost_usd": llm_summary.get("total_cost", 0.0),
                    "average_response_time": llm_summary.get("average_response_time", 0.0),
                    "cost_breakdown": {
                        "input_cost_per_1m_tokens": 2.50,
                        "output_cost_per_1m_tokens": 10.0,
                        "total_input_cost": sum(log.get("input_cost", 0) for log in llm_logs),
                        "total_output_cost": sum(log.get("output_cost", 0) for log in llm_logs)
                    }
                },
                "detailed_llm_logs": llm_logs,
                "evidence": {
                    "raw_report": str(report.get("report", report)),
                    "prompts_used": [log.get("prompt", "") for log in llm_logs],
                    "responses_received": [log.get("response", "") for log in llm_logs]
                }
            }

        # Save the report
        with open(output_file, 'w') as f:
            json.dump(structured_report, f, indent=2, default=str)

        print(f"\n📄 Comprehensive report saved to: {output_file}")

        # Print summary
        print(f"   🎯 Target: {target_url}")
        print(f"   🔍 Vulnerabilities Found: {len(structured_report['vulnerabilities'])}")
        print(f"   🤖 LLM Interactions: {llm_summary.get('total_interactions', 0)}")
        print(f"   � Total Cost: ${llm_summary.get('total_cost', 0.0):.4f}")
        print(f"   🔢 Total Tokens: {llm_summary.get('total_tokens', 0):,}")

    except Exception as e:
        print(f"❌ Error saving report: {e}")

def _calculate_scan_duration(llm_summary: Dict[str, Any]) -> str:
    """Calculate scan duration from LLM summary."""
    try:
        if llm_summary.get("start_time") and llm_summary.get("end_time"):
            from datetime import datetime
            start = datetime.fromisoformat(llm_summary["start_time"])
            end = datetime.fromisoformat(llm_summary["end_time"])
            duration = end - start
            return str(duration)
        return "Unknown"
    except Exception:
        return "Unknown"

def generate_report_filename(url: str, vulnerability_type: str) -> str:
    """Generate a timestamped report filename."""
    from datetime import datetime
    import re

    # Clean URL for filename
    clean_url = re.sub(r'https?://', '', url)
    clean_url = re.sub(r'[^\w\-_.]', '_', clean_url)

    # Generate timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create filename
    filename = f"pentest_report_{clean_url}_{vulnerability_type}_{timestamp}.json"

    return f"reports/{filename}"


async def main():
    """Main entry point for the penetration testing system."""

    # Check for required environment variables based on LLM provider
    llm_config = get_llm_config()

    if llm_config.provider == "azure_openai":
        if not llm_config.api_key:
            print("❌ Error: AZURE_OPENAI_API_KEY environment variable is required")
            print("Please set your Azure OpenAI API key in the .env file")
            sys.exit(1)
        if not llm_config.endpoint:
            print("❌ Error: AZURE_OPENAI_ENDPOINT environment variable is required")
            print("Please set your Azure OpenAI endpoint in the .env file")
            sys.exit(1)
        if not llm_config.model:
            print("❌ Error: AZURE_OPENAI_DEPLOYMENT environment variable is required")
            print("Please set your Azure OpenAI deployment name in the .env file")
            sys.exit(1)
    else:
        print(f"❌ Error: Unsupported LLM provider: {llm_config.provider}")
        print("Please set LLM_PROVIDER=azure_openai in your .env file")
        sys.exit(1)

    # Parse command line arguments
    args = parse_arguments()

    # Validate and process inputs
    target_url = validate_url(args.url)
    additional_info = parse_additional_info(args.info)

    if args.verbose:
        print(f"🔧 Configuration:")
        print(f"   Target URL: {target_url}")
        print(f"   Vulnerability Type: {args.vuln}")
        print(f"   Model: {args.model}")
        print(f"   Additional Info: {additional_info}")
        print()

    try:
        # Run the penetration test
        result = await run_penetration_test(
            target_url,
            args.vuln,
            additional_info,
            args.model
        )

        # Save report - use specified output file or generate one automatically
        if args.output:
            output_file = args.output
        else:
            output_file = generate_report_filename(target_url, args.vuln)

        save_report(result, output_file)

        print("\n✅ Mission completed successfully!")

    except KeyboardInterrupt:
        print("\n🛑 Mission interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Mission failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 