from agno.tools import tool
import subprocess
from typing import Any, Dict
import os
from utils.fileSystem import create_directory_from_url, save_raw_request



@tool
def sqli_scanner_tool(raw_request: str, url: str) -> Dict[str, Any]:
        """
        SQL injection scanner using sqlmap
        
        Args:
            raw_request: The raw HTTP request as a string
            url: Target URL for directory structure
            
        Returns:
            Dictionary containing scan results, output, and file paths
        """
        try:
            # Create output directory
            output_dir = create_directory_from_url(url, "sqli")
            
            # Save raw request to file
            raw_req_file = save_raw_request(raw_request)
            
            # Construct sqlmap command
            cmd = [
                "sqlmap",
                f"--output-dir={output_dir}",
                "--level", "5",
                "-f",
                "--banner",
                "--batch",
                "--skip=user-agent,referer,cookie,host",
                "--ignore-code", "400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,429,431,451,500,501,502,503,504,505,506,507,508,509,510,511",
                "--technique=BUST",
                "-r", raw_req_file
            ]
            
            # Execute command
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=300  # 5 minute timeout
            )
            
            # Clean up temp file
            os.unlink(raw_req_file)
            
            # Prepare response
            response = {
                "tool": "sqli_scanner",
                "success": result.returncode == 0,
                "command": " ".join(cmd),
                "stdout": result.stdout,
                "stderr": result.stderr,
                "output_directory": output_dir,
                "return_code": result.returncode
            }
            
            # Try to find generated files
            output_files = []
            if os.path.exists(output_dir):
                for root, dirs, files in os.walk(output_dir):
                    for file in files:
                        output_files.append(os.path.join(root, file))
            
            response["generated_files"] = output_files
            
            return response
            
        except subprocess.TimeoutExpired:
            return {
                "tool": "sqli_scanner",
                "success": False,
                "error": "Command timed out after 5 minutes",
                "output_directory": output_dir if 'output_dir' in locals() else None
            }
        except Exception as e:
            return {
                "tool": "sqli_scanner",
                "success": False,
                "error": str(e),
                "output_directory": output_dir if 'output_dir' in locals() else None
            }
