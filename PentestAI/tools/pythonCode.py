import contextlib
import io
import traceback
from dataclasses import dataclass
from black import FileMode, format_str
from pygments import highlight
from pygments.lexers import <PERSON><PERSON><PERSON><PERSON>
from pygments.formatters import Terminal256Formatter



@dataclass
class CodeRequest:
    """Request model for code execution."""
    code: str

@dataclass
class CodeResponse:
    """Response model for code execution results."""
    output: str | None
    error: str | None



def execute_python_code(req: CodeRequest) -> CodeResponse:
    """
    Execute Python code in a controlled environment with syntax highlighting.
    
    Args:
        req: CodeRequest containing the code to execute
        
    Returns:
        CodeResponse with output and/or error information
    """
    # 1️⃣ Pretty-print for logs *only* (doesn't reach the agent)
    req.code = req.code.replace("false", "False")
    req.code = req.code.replace("true", "True")
    
    try:
        formatted = format_str(req.code, mode=FileMode())
        print("\n===== PYTHON SNIPPET =====\n" +
              highlight(formatted, Python<PERSON>exer(), Terminal256<PERSON><PERSON>atter()))
    except Exception:
        # If formatting fails, use original code
        formatted = req.code

    # 2️⃣ Run and capture
    buf_out, buf_err = io.StringIO(), io.StringIO()
    
    
    with contextlib.redirect_stdout(buf_out), contextlib.redirect_stderr(buf_err):
        try:
            exec(formatted)
            return CodeResponse(
                output=buf_out.getvalue() or None,
                error=buf_err.getvalue() or None
            )
        except Exception:
            return CodeResponse(
                output=None,
                error=traceback.format_exc()
            )
