import asyncio
from datetime import timedelta
from typing import Any, Dict

from agno.tools.mcp import MCPTools, StreamableHTTPClientParams


async def playwright_mcp(server_url: str = "http://localhost:8931/mcp", timeout: int = 300) -> MCPTools:
    """
    Get Playwright MCP tools connection for Agno.
    
    Args:
        server_url: MCP server endpoint URL
        timeout: Timeout in seconds
        
    Returns:
        MCPTools object ready for use with Agno agents
    """
    server_params = StreamableHTTPClientParams(
        url=server_url,
        timeout=timedelta(seconds=timeout),
        sse_read_timeout=timedelta(seconds=timeout)
    )
    
    return MCPTools(
        server_params=server_params,
        transport="streamable-http",
        timeout_seconds=timeout
    )


