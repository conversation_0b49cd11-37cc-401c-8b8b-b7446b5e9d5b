from dataclasses import dataclass
import subprocess
import time


@dataclass
class TerminalRequest:
    """Request model for terminal command execution."""
    command: str
    working_directory: str = "."
    timeout: int = 30

@dataclass
class TerminalResponse:
    """Response model for terminal command execution results."""
    output: str | None
    error: str | None
    return_code: int
    execution_time: float


def execute_terminal_command(req: TerminalRequest) -> TerminalResponse:
    """
    Execute terminal commands in a controlled environment.
    
    Args:
        req: TerminalRequest containing the command to execute
        
    Returns:
        TerminalResponse with output, error, return code, and execution time
    """
    print(f"\n===== TERMINAL COMMAND =====")
    print(f"Working Directory: {req.working_directory}")
    print(f"Command: {req.command}")
    print(f"Timeout: {req.timeout}s")
    print("=" * 30)
    
    start_time = time.time()
    
    try:
        # Execute the command
        result = subprocess.run(
            req.command,
            shell=True,
            cwd=req.working_directory,
            capture_output=True,
            text=True,
            timeout=req.timeout
        )
        
        execution_time = time.time() - start_time
        
        output = result.stdout if result.stdout else None
        error = result.stderr if result.stderr else None
        
        # Print results for visibility
        if output:
            print(f"STDOUT:\n{output}")
        if error:
            print(f"STDERR:\n{error}")
        print(f"Return Code: {result.returncode}")
        print(f"Execution Time: {execution_time:.2f}s")
        
        return TerminalResponse(
            output=output,
            error=error,
            return_code=result.returncode,
            execution_time=execution_time
        )
        
    except subprocess.TimeoutExpired:
        execution_time = time.time() - start_time
        error_msg = f"Command timed out after {req.timeout} seconds"
        print(f"ERROR: {error_msg}")
        
        return TerminalResponse(
            output=None,
            error=error_msg,
            return_code=-1,
            execution_time=execution_time
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"Failed to execute command: {str(e)}"
        print(f"ERROR: {error_msg}")
        
        return TerminalResponse(
            output=None,
            error=error_msg,
            return_code=-1,
            execution_time=execution_time
        )
