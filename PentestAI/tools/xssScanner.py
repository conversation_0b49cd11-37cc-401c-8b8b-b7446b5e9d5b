from agno.tools import tool
from typing import Any, Dict
import os
import subprocess
import json
from utils.fileSystem import create_directory_from_url, save_raw_request


@tool
def xss_scanner_tool(raw_request: str, url: str) -> Dict[str, Any]:
        """
        XSS scanner using dalfox
        
        Args:
            raw_request: The raw HTTP request as a string
            url: Target URL for directory structure
            
        Returns:
            Dictionary containing scan results, output, and file paths
        """
        try:
            # Create output directory
            output_dir = create_directory_from_url(url, "xss")
            
            # Save raw request to file
            raw_req_file = save_raw_request(raw_request)
            
            # Output file path
            output_file = os.path.join(output_dir, "dalfox.json")
            
            # Construct dalfox command
            cmd = [
                "dalfox",
                "file",
                "--silence",
                "--report",
                "--report-format", "json",
                "--format", "json",
                "--http",
                "--poc-type", "curl",
                "--rawdata", raw_req_file,
                "-o", output_file
            ]
            
            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # Clean up temp file
            os.unlink(raw_req_file)
            
            # Prepare response
            response = {
                "tool": "xss_scanner",
                "success": result.returncode == 0,
                "command": " ".join(cmd),
                "stdout": result.stdout,
                "stderr": result.stderr,
                "output_directory": output_dir,
                "output_file": output_file,
                "return_code": result.returncode
            }
            
            # Try to parse JSON output if file exists
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r') as f:
                        response["parsed_results"] = json.load(f)
                except json.JSONDecodeError:
                    response["parse_error"] = "Could not parse JSON output"
            
            # Find all generated files
            output_files = []
            if os.path.exists(output_dir):
                for root, dirs, files in os.walk(output_dir):
                    for file in files:
                        output_files.append(os.path.join(root, file))
            
            response["generated_files"] = output_files
            
            return response
            
        except subprocess.TimeoutExpired:
            return {
                "tool": "xss_scanner",
                "success": False,
                "error": "Command timed out after 5 minutes",
                "output_directory": output_dir if 'output_dir' in locals() else None
            }
        except Exception as e:
            return {
                "tool": "xss_scanner",
                "success": False,
                "error": str(e),
                "output_directory": output_dir if 'output_dir' in locals() else None
            }

