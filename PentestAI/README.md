# PentestAI - Automated Penetration Testing System

An AI-powered multi-agent penetration testing framework using [Agno](https://docs.agno.com/teams/introduction) Teams for coordinated vulnerability assessment.

## 🎯 Overview

PentestAI uses a coordinated team of specialized AI agents to automatically discover and exploit web application vulnerabilities:

- **Lead Team**: Coordinates the entire operation using Agno Team's coordinate mode
- **Browser Agent**: Handles web navigation, interaction, and HTTP request capture
- **SQLI Agent**: Specializes in SQL injection vulnerability detection and exploitation
- **XSS Agent**: Focuses on Cross-Site Scripting vulnerability testing

## 🏗️ Architecture

The system uses [Agno Team coordinate mode](https://docs.agno.com/teams/introduction) where the lead team delegates tasks to specialized agents and synthesizes their outputs into comprehensive vulnerability reports.

```
┌─────────────────────────────────────────┐
│          Lead Team (Coordinator)        │
│     ┌─────────────────────────────┐     │
│     │    Team Leader Agent        │     │
│     │   (coordination tools)      │     │
│     └─────────────────────────────┘     │
└─────────────────┬───────────────────────┘
                  │ coordinates
    ┌─────────────┼─────────────┐
    │             │             │
┌───▼───┐    ┌────▼────┐   ┌────▼────┐
│Browser│    │  SQLI   │   │   XSS   │
│ Agent │    │ Agent   │   │ Agent   │
│       │    │ +sqlmap │   │ +xss    │
│       │    │ scanner │   │ scanner │
└───────┘    └─────────┘   └─────────┘
```

## 📦 Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd PentestAI
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   ```

## 🚀 Usage

### Basic Usage

Test a website for SQL injection vulnerabilities:
```bash
python main.py --url https://example.com --vuln SQLI
```

Test for XSS vulnerabilities:
```bash
python main.py --url https://example.com --vuln XSS
```

Test for all supported vulnerabilities:
```bash
python main.py --url https://example.com --vuln ALL
```

### Advanced Usage

**With additional context:**
```bash
python main.py --url https://example.com --vuln SQLI \
  --info '{"auth_required": true, "login_path": "/login", "credentials": {"username": "test", "password": "test"}}'
```

**With array of target areas:**
```bash
python main.py --url https://example.com --vuln XSS \
  --info '["search form", "comment section", "contact form"]'
```

**Save results to file:**
```bash
python main.py --url https://example.com --vuln ALL \
  --output vulnerability_report.json
```

**Verbose output:**
```bash
python main.py --url https://example.com --vuln SQLI --verbose
```

### Command Line Options

| Option | Required | Description | Examples |
|--------|----------|-------------|----------|
| `--url` | Yes | Target website URL | `https://example.com`, `example.com` |
| `--vuln` | Yes | Vulnerability type | `XSS`, `SQLI`, `ALL` |
| `--info` | No | Additional JSON info | `'{"key": "value"}'`, `'["item1", "item2"]'` |
| `--model` | No | Language model (default: gpt-4o) | `gpt-4o`, `gpt-4-turbo` |
| `--output` | No | Output file for report | `report.json` |
| `--verbose` | No | Enable verbose output | |

## 🧠 How It Works

### 1. Team Coordination
The system uses Agno's coordinate mode where the lead team:
- Analyzes the target and mission requirements
- Delegates specific tasks to appropriate specialist agents
- Synthesizes findings from multiple agents
- Generates comprehensive vulnerability reports

### 2. Agent Specialization

**Browser Agent:**
- Navigates websites using Playwright
- Captures screenshots and HTTP requests
- Interacts with forms and UI elements
- Provides reconnaissance data

**SQLI Agent:**
- Analyzes HTTP requests for injection points
- Tests various SQL injection techniques
- **Has sqli_scanner_tool**: Automated sqlmap integration
- Uses both automated scanning and manual payloads
- Confirms vulnerabilities with evidence

**XSS Agent:**
- Tests input fields for XSS vulnerabilities
- **Has xss_scanner_tool**: Automated XSS vulnerability scanning
- Handles reflected, stored, and DOM-based XSS
- Uses both automated scanning and browser interaction
- Captures proof-of-concept screenshots
- Generates exploit payloads

### 3. Workflow Example

For SQLI testing:
1. **Reconnaissance**: Browser Agent explores the target website
2. **Request Capture**: Browser Agent captures raw HTTP requests from forms
3. **Automated Scanning**: SQLI Agent uses its built-in sqli_scanner_tool for rapid assessment
4. **Manual Testing**: SQLI Agent performs custom payload testing for complex scenarios
5. **Exploitation**: SQLI Agent confirms and exploits discovered vulnerabilities
6. **Reporting**: Lead Team synthesizes all findings into a comprehensive report

## 📄 Output Format

The system generates detailed vulnerability reports including:

```json
{
  "vulnerability_type": "SQLI",
  "report": {
    "identified_vulnerabilities": [
      {
        "type": "SQL Injection",
        "location": "/search?q=",
        "severity": "High",
        "curl_command": "curl -X POST ...",
        "evidence": "MySQL error message revealed...",
        "impact": "Full database access possible"
      }
    ],
    "summary": "Comprehensive assessment results...",
    "recommendations": ["Implement parameterized queries", "..."]
  }
}
```

## 🛠️ Technical Details

### Dependencies
- **Agno**: Multi-agent framework with team coordination
- **Playwright**: Browser automation for web interaction
- **OpenAI API**: Language model for agent intelligence
- **SQLMap**: SQL injection testing tool integration
- **Python 3.8+**: Runtime environment

### Key Features
- **Coordinate Mode**: Lead team delegates and synthesizes using [Agno Teams](https://docs.agno.com/teams/introduction)
- **Specialized Agents**: Each agent has domain-specific tools and expertise
- **Distributed Scanners**: Each vulnerability agent has its own automated scanner
  - SQLI Agent has `sqli_scanner_tool` (sqlmap integration)
  - XSS Agent has `xss_scanner_tool` (automated XSS detection)
- **Evidence Collection**: Screenshots, HTTP requests, and exploit proofs
- **Comprehensive Reporting**: Detailed vulnerability assessments
- **Flexible Input**: JSON-based additional context

## ⚠️ Legal and Ethical Use

**IMPORTANT**: This tool is designed for authorized penetration testing only.

- ✅ **Authorized Testing**: Only test websites you own or have explicit permission to test
- ✅ **Bug Bounty Programs**: Use for legitimate bug bounty hunting with proper scope
- ✅ **Educational Purposes**: Learn about web security in controlled environments
- ❌ **Unauthorized Testing**: Never test websites without permission
- ❌ **Malicious Use**: Do not use for illegal activities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📞 Support

For issues and questions:
- Check existing GitHub issues
- Review the Agno documentation: https://docs.agno.com/teams/introduction
- Create a new issue with detailed information

## 📜 License

This project is licensed under the MIT License - see LICENSE file for details.

---

**Built with [Agno](https://docs.agno.com/teams/introduction) - The AI Agent Framework** 