#!/usr/bin/env python3
"""
Test script to verify the fixed vulnerability analysis logic
"""

import json
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from main import extract_vulnerabilities_from_report, analyze_vulnerability_evidence

def test_negative_evidence_analysis():
    """Test analysis of negative evidence (no vulnerabilities found)"""
    print("🧪 Testing Negative Evidence Analysis...")
    print("-" * 60)
    
    # Sample content showing NO vulnerabilities found
    negative_content = """
    SQLMap Analysis Results:
    
    [22:45:46] [WARNING] GET parameter 'id' does not seem to be injectable
    [22:45:46] [CRITICAL] all tested parameters do not appear to be injectable.
    
    Manual Testing Results:
    
    All payloads resulted in the same status code (200), response length (607 bytes), 
    and relatively similar response times. No evidence of behavioral variation or delay 
    suggests that these payloads did not influence the query execution.
    
    Time-based test with SLEEP(5) payload took only 0.028 seconds instead of 5+ seconds.
    """
    
    # Test the analysis function
    status, severity = analyze_vulnerability_evidence(negative_content, "SQL Injection")
    print(f"✅ Analysis Result: Status='{status}', Severity='{severity}'")
    
    # Test full vulnerability extraction
    vulnerabilities = extract_vulnerabilities_from_report(negative_content)
    print(f"✅ Vulnerabilities Found: {len(vulnerabilities)}")
    
    for vuln in vulnerabilities:
        print(f"   - Type: {vuln['type']}")
        print(f"   - Status: {vuln['status']}")
        print(f"   - Severity: {vuln['severity']}")
    
    return vulnerabilities

def test_positive_evidence_analysis():
    """Test analysis of positive evidence (vulnerabilities confirmed)"""
    print("\n🧪 Testing Positive Evidence Analysis...")
    print("-" * 60)
    
    # Sample content showing vulnerabilities found
    positive_content = """
    SQL Injection vulnerability confirmed!
    
    Vulnerable parameter: id
    Payload successful: 1' OR '1'='1
    Database error detected: MySQL syntax error
    
    Time-based injection confirmed with SLEEP(5) causing 5.2 second delay.
    """
    
    # Test the analysis function
    status, severity = analyze_vulnerability_evidence(positive_content, "SQL Injection")
    print(f"✅ Analysis Result: Status='{status}', Severity='{severity}'")
    
    # Test full vulnerability extraction
    vulnerabilities = extract_vulnerabilities_from_report(positive_content)
    print(f"✅ Vulnerabilities Found: {len(vulnerabilities)}")
    
    for vuln in vulnerabilities:
        print(f"   - Type: {vuln['type']}")
        print(f"   - Status: {vuln['status']}")
        print(f"   - Severity: {vuln['severity']}")
    
    return vulnerabilities

def test_real_report_content():
    """Test with actual problematic report content"""
    print("\n🧪 Testing Real Report Content...")
    print("-" * 60)
    
    # Actual content from the problematic report
    real_content = """
    The `sqli_scanner_tool` analyzed the target HTTP request at "http://********:8080/Less-10/?id=1" 
    but could not confirm the presence of SQL injection vulnerabilities.
    
    [22:45:46] [WARNING] GET parameter 'id' does not seem to be injectable
    [22:45:46] [CRITICAL] all tested parameters do not appear to be injectable.
    
    Manual payload testing results:
    
    1. Payload: 1' OR '1'='1
       - Status Code: 200
       - Response Time: 0.028 seconds
       - Response Length: 607 bytes

    2. Payload: 1' AND SLEEP(5)--
       - Status Code: 200
       - Response Time: 0.028 seconds
       - Response Length: 607 bytes
    
    All payloads resulted in the same status code (200), response length (607 bytes), 
    and relatively similar response times. No evidence of behavioral variation or delay 
    suggests that these payloads did not influence the query execution.
    """
    
    # Test vulnerability extraction
    vulnerabilities = extract_vulnerabilities_from_report(real_content)
    print(f"✅ Vulnerabilities Found: {len(vulnerabilities)}")
    
    for vuln in vulnerabilities:
        print(f"   - Type: {vuln['type']}")
        print(f"   - Status: {vuln['status']}")
        print(f"   - Severity: {vuln['severity']}")
        print(f"   - Description: {vuln['description']}")
    
    return vulnerabilities

def test_report_generation():
    """Test complete report generation with corrected analysis"""
    print("\n🔧 Testing Complete Report Generation...")
    print("-" * 60)
    
    from shared.llm_logger import initialize_logger, get_logger
    from main import save_report
    
    # Initialize logger for testing
    logger = initialize_logger(
        target_url="http://********:8080/Less-10/",
        vulnerability_type="SQLI"
    )
    
    # Add test interaction
    logger.log_interaction(
        model="gpt-4o",
        provider="azure_openai",
        prompt="Test SQL injection on Less-10",
        response="No SQL injection vulnerabilities found after comprehensive testing",
        input_tokens=200,
        output_tokens=300,
        total_tokens=500,
        response_time=3.5,
        agent_name="SQLI Agent",
        task_description="SQL injection assessment"
    )
    
    logger.finalize_session()
    
    # Create mock report with negative findings
    mock_report = {
        "vulnerability_type": "SQLI",
        "report": """
        SQL Injection Assessment Results:
        
        [22:45:46] [WARNING] GET parameter 'id' does not seem to be injectable
        [22:45:46] [CRITICAL] all tested parameters do not appear to be injectable.
        
        Manual Testing Results:
        All payloads resulted in the same status code (200), response length (607 bytes), 
        and relatively similar response times. No evidence of behavioral variation or delay 
        suggests that these payloads did not influence the query execution.
        
        Conclusion: No SQL injection vulnerabilities detected.
        """
    }
    
    # Generate report
    test_output_file = "test_reports/corrected_vulnerability_report.json"
    save_report(mock_report, test_output_file)
    
    # Verify the report structure
    if Path(test_output_file).exists():
        with open(test_output_file, 'r') as f:
            report_data = json.load(f)
        
        print("✅ Report generated successfully!")
        print(f"✅ Target URL: {report_data['scan_metadata']['target_url']}")
        print(f"✅ Vulnerability Type: {report_data['scan_metadata']['vulnerability_type']}")
        print(f"✅ Vulnerabilities Found: {len(report_data['vulnerabilities'])}")
        
        # Check vulnerability status
        if report_data['vulnerabilities']:
            vuln = report_data['vulnerabilities'][0]
            print(f"✅ Vulnerability Status: {vuln['status']}")
            print(f"✅ Vulnerability Severity: {vuln['severity']}")
            
            # Verify it's correctly marked as "Not Found"
            if vuln['status'] == "Not Found":
                print("🎉 SUCCESS: Vulnerability correctly marked as 'Not Found'!")
            else:
                print(f"❌ ERROR: Expected 'Not Found', got '{vuln['status']}'")
                return False
        
        return True
    else:
        print("❌ Report file was not created")
        return False

if __name__ == "__main__":
    print("🎯 Vulnerability Analysis Fix Test")
    print("=" * 70)
    
    # Test negative evidence
    negative_vulns = test_negative_evidence_analysis()
    
    # Test positive evidence
    positive_vulns = test_positive_evidence_analysis()
    
    # Test real report content
    real_vulns = test_real_report_content()
    
    # Test complete report generation
    report_ok = test_report_generation()
    
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    print(f"   Negative Evidence Test: {'✅ PASS' if negative_vulns and negative_vulns[0]['status'] == 'Not Found' else '❌ FAIL'}")
    print(f"   Positive Evidence Test: {'✅ PASS' if positive_vulns and positive_vulns[0]['status'] == 'Confirmed' else '❌ FAIL'}")
    print(f"   Real Content Test: {'✅ PASS' if real_vulns and real_vulns[0]['status'] == 'Not Found' else '❌ FAIL'}")
    print(f"   Report Generation Test: {'✅ PASS' if report_ok else '❌ FAIL'}")
    
    if all([
        negative_vulns and negative_vulns[0]['status'] == 'Not Found',
        positive_vulns and positive_vulns[0]['status'] == 'Confirmed', 
        real_vulns and real_vulns[0]['status'] == 'Not Found',
        report_ok
    ]):
        print("\n🎉 All tests passed! Vulnerability analysis is now working correctly.")
        print("\nFixed issues:")
        print("✅ Correctly identifies when vulnerabilities are NOT found")
        print("✅ Analyzes SQLMap CRITICAL failures properly")
        print("✅ Detects failed time-based injection tests")
        print("✅ Marks status as 'Not Found' instead of 'Confirmed'")
        print("✅ Sets appropriate severity levels")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
