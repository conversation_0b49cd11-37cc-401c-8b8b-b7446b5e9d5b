"""
Logged Azure OpenAI wrapper that automatically tracks all LLM interactions
"""

import time
from typing import Any, Dict, List, Optional, Union, Type
from agno.models.azure import AzureOpenAI
from agno.models.message import Message
from pydantic import BaseModel
from .llm_logger import get_logger, log_llm_interaction

class LoggedAzureOpenAI(AzureOpenAI):
    """Azure OpenAI wrapper with automatic logging"""
    
    def __init__(self, *args, **kwargs):
        # Extract agent_name before passing to parent
        self.agent_name = kwargs.pop('agent_name', 'Unknown Agent')
        super().__init__(*args, **kwargs)
    
    def _extract_prompt_from_messages(self, messages: List[Message]) -> str:
        """Extract prompt text from messages for logging"""
        try:
            prompt_parts = []
            for msg in messages:
                role = msg.role or "unknown"
                content = msg.content or ""
                if isinstance(content, str):
                    prompt_parts.append(f"[{role.upper()}]: {content}")
                else:
                    prompt_parts.append(f"[{role.upper()}]: {str(content)}")
            return "\n".join(prompt_parts)
        except Exception as e:
            return f"Error extracting prompt: {str(e)}"
    
    def _extract_response_content(self, response: Any) -> str:
        """Extract response content for logging"""
        try:
            if hasattr(response, 'content'):
                return str(response.content)
            elif hasattr(response, 'choices') and response.choices:
                if hasattr(response.choices[0], 'message'):
                    return str(response.choices[0].message.content)
            return str(response)
        except Exception as e:
            return f"Error extracting response: {str(e)}"
    
    def _extract_token_metrics(self, response: Any) -> Dict[str, int]:
        """Extract token usage metrics from response"""
        try:
            metrics = {
                'input_tokens': 0,
                'output_tokens': 0,
                'total_tokens': 0,
                'cached_tokens': 0,
                'reasoning_tokens': 0
            }
            
            # Try to get usage from response
            if hasattr(response, 'usage'):
                usage = response.usage
                if hasattr(usage, 'prompt_tokens'):
                    metrics['input_tokens'] = usage.prompt_tokens
                if hasattr(usage, 'completion_tokens'):
                    metrics['output_tokens'] = usage.completion_tokens
                if hasattr(usage, 'total_tokens'):
                    metrics['total_tokens'] = usage.total_tokens
                if hasattr(usage, 'prompt_tokens_details'):
                    details = usage.prompt_tokens_details
                    if hasattr(details, 'cached_tokens'):
                        metrics['cached_tokens'] = details.cached_tokens
                if hasattr(usage, 'completion_tokens_details'):
                    details = usage.completion_tokens_details
                    if hasattr(details, 'reasoning_tokens'):
                        metrics['reasoning_tokens'] = details.reasoning_tokens
            
            # Fallback: calculate total if not provided
            if metrics['total_tokens'] == 0:
                metrics['total_tokens'] = metrics['input_tokens'] + metrics['output_tokens']
                
            return metrics
        except Exception as e:
            print(f"Warning: Could not extract token metrics: {e}")
            return {
                'input_tokens': 0,
                'output_tokens': 0,
                'total_tokens': 0,
                'cached_tokens': 0,
                'reasoning_tokens': 0
            }
    
    def invoke(self, 
               messages: List[Message], 
               response_format: Optional[Union[Dict, Type[BaseModel]]] = None,
               tools: Optional[List[Dict[str, Any]]] = None,
               tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
               **kwargs) -> Any:
        """Invoke with automatic logging"""
        
        start_time = time.time()
        prompt = self._extract_prompt_from_messages(messages)
        error = None
        response = None
        
        try:
            # Call the parent invoke method
            response = super().invoke(
                messages=messages,
                response_format=response_format,
                tools=tools,
                tool_choice=tool_choice,
                **kwargs
            )
            
            response_content = self._extract_response_content(response)
            
        except Exception as e:
            error = str(e)
            response_content = f"Error: {error}"
            raise
        
        finally:
            # Always log the interaction, even if there was an error
            end_time = time.time()
            response_time = end_time - start_time
            
            # Extract token metrics
            token_metrics = self._extract_token_metrics(response) if response else {
                'input_tokens': 0, 'output_tokens': 0, 'total_tokens': 0,
                'cached_tokens': 0, 'reasoning_tokens': 0
            }
            
            # Log the interaction
            log_llm_interaction(
                model=self.id,
                provider=self.provider,
                prompt=prompt,
                response=response_content,
                input_tokens=token_metrics['input_tokens'],
                output_tokens=token_metrics['output_tokens'],
                total_tokens=token_metrics['total_tokens'],
                cached_tokens=token_metrics['cached_tokens'],
                reasoning_tokens=token_metrics['reasoning_tokens'],
                response_time=response_time,
                agent_name=self.agent_name,
                task_description=kwargs.get('task_description', None),
                error=error
            )
        
        return response
    
    async def ainvoke(self, 
                      messages: List[Message], 
                      response_format: Optional[Union[Dict, Type[BaseModel]]] = None,
                      tools: Optional[List[Dict[str, Any]]] = None,
                      tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
                      **kwargs) -> Any:
        """Async invoke with automatic logging"""
        
        start_time = time.time()
        prompt = self._extract_prompt_from_messages(messages)
        error = None
        response = None
        
        try:
            # Call the parent ainvoke method
            response = await super().ainvoke(
                messages=messages,
                response_format=response_format,
                tools=tools,
                tool_choice=tool_choice,
                **kwargs
            )
            
            response_content = self._extract_response_content(response)
            
        except Exception as e:
            error = str(e)
            response_content = f"Error: {error}"
            raise
        
        finally:
            # Always log the interaction, even if there was an error
            end_time = time.time()
            response_time = end_time - start_time
            
            # Extract token metrics
            token_metrics = self._extract_token_metrics(response) if response else {
                'input_tokens': 0, 'output_tokens': 0, 'total_tokens': 0,
                'cached_tokens': 0, 'reasoning_tokens': 0
            }
            
            # Log the interaction
            log_llm_interaction(
                model=self.id,
                provider=self.provider,
                prompt=prompt,
                response=response_content,
                input_tokens=token_metrics['input_tokens'],
                output_tokens=token_metrics['output_tokens'],
                total_tokens=token_metrics['total_tokens'],
                cached_tokens=token_metrics['cached_tokens'],
                reasoning_tokens=token_metrics['reasoning_tokens'],
                response_time=response_time,
                agent_name=self.agent_name,
                task_description=kwargs.get('task_description', None),
                error=error
            )
        
        return response
