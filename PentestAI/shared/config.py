"""
Configuration management for PentestAI

Handles environment variables, LLM provider configuration,
and other settings with modular support for different providers.
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

@dataclass
class LLMConfig:
    """Configuration for LLM providers"""
    provider: str
    api_key: str
    model: str
    endpoint: Optional[str] = None           # Used for Azure OpenAI
    api_version: Optional[str] = None        # Used for Azure OpenAI

@dataclass
class MCPConfig:
    """Configuration for MCP Playwright server"""
    server_url: str
    port: int
    timeout: int
    
@dataclass
class BrowserConfig:
    """Configuration for browser automation"""
    browser_type: str
    headless: bool
    timeout: int
    
@dataclass
class PentestConfig:
    """Main configuration class for PentestAI tool"""
    llm: LLMConfig
    mcp: MCPConfig
    browser: BrowserConfig
    reports_dir: str
    log_level: str
    sqlmap_path: str
    sqlmap_timeout: int
    scan_timeout: int
    agent_timeout: int

    # Rate limiting configuration
    max_tokens_per_request: int = 25000  # Stay well below 30k limit
    reconnaissance_phases: int = 3  # Number of phases to break reconnaissance into
    phase_delay_seconds: int = 2  # Delay between phases
    max_retries: int = 3  # Max retries for rate limited requests

def get_llm_config() -> LLMConfig:
    """Get LLM configuration based on provider"""
    provider = os.getenv("LLM_PROVIDER", "openai").lower()
    
    if provider == "openai":
        return LLMConfig(
            provider="openai",
            api_key=os.getenv("OPENAI_API_KEY", ""),
            model=os.getenv("OPENAI_MODEL", "gpt-4o")
        )
    elif provider == "azure_openai":
        return LLMConfig(
            provider="azure_openai",
            api_key=os.getenv("AZURE_OPENAI_API_KEY", ""),
            model=os.getenv("AZURE_OPENAI_DEPLOYMENT", ""),  # Azure model = deployment name
            endpoint=os.getenv("AZURE_OPENAI_ENDPOINT", ""),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION", "")
        )
    elif provider == "gemini":
        return LLMConfig(
            provider="gemini",
            api_key=os.getenv("GEMINI_API_KEY", ""),
            model=os.getenv("GEMINI_MODEL", "gemini-pro")
        )
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")

def get_mcp_config() -> MCPConfig:
    """Get MCP server configuration"""
    return MCPConfig(
        server_url=os.getenv("MCP_SERVER_URL", "http://localhost:8931/mcp"),
        port=int(os.getenv("MCP_SERVER_PORT", "8931")),
        timeout=int(os.getenv("MCP_TIMEOUT", "300"))
    )

def get_browser_config() -> BrowserConfig:
    """Get browser configuration"""
    return BrowserConfig(
        browser_type=os.getenv("BROWSER_TYPE", "chromium"),
        headless=os.getenv("BROWSER_HEADLESS", "true").lower() == "true",
        timeout=int(os.getenv("BROWSER_TIMEOUT", "30000"))
    )

def get_pentest_config() -> PentestConfig:
    """Get complete PentestAI configuration"""
    return PentestConfig(
        llm=get_llm_config(),
        mcp=get_mcp_config(),
        browser=get_browser_config(),
        reports_dir=os.getenv("REPORTS_DIR", "./reports"),
        log_level=os.getenv("LOG_LEVEL", "INFO"),
        sqlmap_path=os.getenv("SQLMAP_PATH", "sqlmap"),
        sqlmap_timeout=int(os.getenv("SQLMAP_TIMEOUT", "300")),
        scan_timeout=int(os.getenv("SCAN_TIMEOUT", "600")),
        agent_timeout=int(os.getenv("AGENT_TIMEOUT", "300"))
    )

def validate_config(config: PentestConfig) -> bool:
    """Validate configuration settings"""
    if not config.llm.api_key:
        raise ValueError(f"API key not set for {config.llm.provider}")
    
    if not os.path.exists(config.reports_dir):
        os.makedirs(config.reports_dir, exist_ok=True)
    
    return True
