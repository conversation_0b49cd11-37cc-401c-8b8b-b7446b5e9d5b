"""
LLM Logging System for PentestAI

Tracks all LLM interactions including prompts, responses, token usage, and costs.
Provides comprehensive logging for audit trails and cost analysis.
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import threading

@dataclass
class LLMInteraction:
    """Single LLM interaction record"""
    timestamp: str
    model: str
    provider: str
    prompt: str
    response: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    cached_tokens: int = 0
    reasoning_tokens: int = 0
    input_cost: float = 0.0
    output_cost: float = 0.0
    total_cost: float = 0.0
    response_time: float = 0.0
    agent_name: Optional[str] = None
    task_description: Optional[str] = None
    error: Optional[str] = None

@dataclass
class LLMSession:
    """Complete LLM session with multiple interactions"""
    session_id: str
    start_time: str
    end_time: Optional[str] = None
    target_url: str = ""
    vulnerability_type: str = ""
    interactions: List[LLMInteraction] = None
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    
    def __post_init__(self):
        if self.interactions is None:
            self.interactions = []

class LLMLogger:
    """Centralized LLM logging system"""
    
    # Azure OpenAI pricing (per 1M tokens)
    PRICING = {
        "azure_openai": {
            "gpt-4o": {
                "input": 2.50,   # $2.50 per 1M input tokens
                "output": 10.0   # $10.00 per 1M output tokens
            },
            "gpt-4": {
                "input": 30.0,
                "output": 60.0
            },
            "gpt-4-turbo": {
                "input": 10.0,
                "output": 30.0
            }
        },
        "openai": {
            "gpt-4o": {
                "input": 2.50,
                "output": 10.0
            },
            "gpt-4": {
                "input": 30.0,
                "output": 60.0
            }
        }
    }
    
    def __init__(self, session_id: str = None, target_url: str = "", vulnerability_type: str = ""):
        self.session_id = session_id or f"session_{int(time.time())}"
        self.session = LLMSession(
            session_id=self.session_id,
            start_time=datetime.now().isoformat(),
            target_url=target_url,
            vulnerability_type=vulnerability_type
        )
        self._lock = threading.Lock()
        
    def calculate_cost(self, provider: str, model: str, input_tokens: int, output_tokens: int) -> tuple:
        """Calculate cost based on token usage"""
        try:
            # Normalize model name for pricing lookup
            model_key = model.lower()
            if "gpt-4o" in model_key:
                model_key = "gpt-4o"
            elif "gpt-4-turbo" in model_key:
                model_key = "gpt-4-turbo"
            elif "gpt-4" in model_key:
                model_key = "gpt-4"
            
            pricing = self.PRICING.get(provider, {}).get(model_key, {"input": 0, "output": 0})
            
            input_cost = (input_tokens / 1_000_000) * pricing["input"]
            output_cost = (output_tokens / 1_000_000) * pricing["output"]
            total_cost = input_cost + output_cost
            
            return input_cost, output_cost, total_cost
        except Exception:
            return 0.0, 0.0, 0.0
    
    def log_interaction(self, 
                       model: str,
                       provider: str,
                       prompt: str,
                       response: str,
                       input_tokens: int,
                       output_tokens: int,
                       total_tokens: int,
                       response_time: float = 0.0,
                       cached_tokens: int = 0,
                       reasoning_tokens: int = 0,
                       agent_name: str = None,
                       task_description: str = None,
                       error: str = None) -> LLMInteraction:
        """Log a single LLM interaction"""
        
        input_cost, output_cost, total_cost = self.calculate_cost(
            provider, model, input_tokens, output_tokens
        )
        
        interaction = LLMInteraction(
            timestamp=datetime.now().isoformat(),
            model=model,
            provider=provider,
            prompt=prompt,
            response=response,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            cached_tokens=cached_tokens,
            reasoning_tokens=reasoning_tokens,
            input_cost=input_cost,
            output_cost=output_cost,
            total_cost=total_cost,
            response_time=response_time,
            agent_name=agent_name,
            task_description=task_description,
            error=error
        )
        
        with self._lock:
            self.session.interactions.append(interaction)
            self.session.total_input_tokens += input_tokens
            self.session.total_output_tokens += output_tokens
            self.session.total_tokens += total_tokens
            self.session.total_cost += total_cost
        
        return interaction
    
    def finalize_session(self):
        """Finalize the logging session"""
        with self._lock:
            self.session.end_time = datetime.now().isoformat()
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get session summary with aggregated metrics"""
        with self._lock:
            return {
                "session_id": self.session.session_id,
                "start_time": self.session.start_time,
                "end_time": self.session.end_time,
                "target_url": self.session.target_url,
                "vulnerability_type": self.session.vulnerability_type,
                "total_interactions": len(self.session.interactions),
                "total_input_tokens": self.session.total_input_tokens,
                "total_output_tokens": self.session.total_output_tokens,
                "total_tokens": self.session.total_tokens,
                "total_cost": round(self.session.total_cost, 4),
                "average_response_time": round(
                    sum(i.response_time for i in self.session.interactions) / 
                    max(len(self.session.interactions), 1), 2
                )
            }
    
    def get_detailed_logs(self) -> List[Dict[str, Any]]:
        """Get detailed interaction logs"""
        with self._lock:
            return [asdict(interaction) for interaction in self.session.interactions]
    
    def save_logs(self, filepath: str):
        """Save logs to file"""
        with self._lock:
            log_data = {
                "session": asdict(self.session),
                "summary": self.get_session_summary()
            }
            
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            with open(filepath, 'w') as f:
                json.dump(log_data, f, indent=2, default=str)

# Global logger instance
_global_logger: Optional[LLMLogger] = None

def initialize_logger(session_id: str = None, target_url: str = "", vulnerability_type: str = "") -> LLMLogger:
    """Initialize global logger"""
    global _global_logger
    _global_logger = LLMLogger(session_id, target_url, vulnerability_type)
    return _global_logger

def get_logger() -> Optional[LLMLogger]:
    """Get global logger instance"""
    return _global_logger

def log_llm_interaction(**kwargs) -> Optional[LLMInteraction]:
    """Log interaction using global logger"""
    if _global_logger:
        return _global_logger.log_interaction(**kwargs)
    return None
