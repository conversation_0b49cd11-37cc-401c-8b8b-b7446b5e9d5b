# PentestAI Report Structure Documentation

This document describes the comprehensive JSON report structure implemented in PentestAI, which includes vulnerability findings, LLM interaction logs, cost analysis, and detailed evidence.

## Overview

The new report structure provides:
- ✅ **Structured vulnerability information** with evidence
- ✅ **Complete LLM interaction logging** with token tracking
- ✅ **Cost analysis** with Azure OpenAI pricing ($2.50/1M input, $10/1M output tokens)
- ✅ **Detailed evidence collection** including prompts and responses
- ✅ **Session management** with timing and analytics
- ✅ **JSON format** for easy parsing and integration

## Report Structure

### 1. <PERSON><PERSON> Metadata
```json
{
  "scan_metadata": {
    "scan_id": "session_1234567890",
    "timestamp": "2025-07-31T12:00:00",
    "target_url": "https://example.com",
    "vulnerability_type": "SQLI",
    "scan_duration": "0:05:23",
    "tool_version": "PentestAI v1.0"
  }
}
```

**Fields:**
- `scan_id`: Unique identifier for the scan session
- `timestamp`: When the scan was completed
- `target_url`: The target website URL
- `vulnerability_type`: Type of vulnerability tested (SQLI, XSS, ALL)
- `scan_duration`: Total time taken for the scan
- `tool_version`: Version of PentestAI used

### 2. Vulnerabilities
```json
{
  "vulnerabilities": [
    {
      "type": "SQL Injection",
      "severity": "High",
      "description": "SQL Injection vulnerability detected",
      "evidence": {
        "payloads": ["1' OR '1'='1", "1; DROP TABLE users--"],
        "requests": ["GET /search?id=1' OR '1'='1"],
        "responses": ["HTTP/1.1 200 OK"],
        "urls": ["https://example.com/search"],
        "parameters": ["id=1"]
      },
      "status": "Confirmed"
    }
  ]
}
```

**Fields:**
- `type`: Vulnerability type (SQL Injection, Cross-Site Scripting, etc.)
- `severity`: Risk level (High, Medium, Low, Informational)
- `description`: Human-readable description
- `evidence`: Technical evidence including payloads, requests, responses
- `status`: Confirmation status (Confirmed, Potential, Error)

### 3. LLM Analytics
```json
{
  "llm_analytics": {
    "model": "gpt-4o",
    "provider": "azure_openai",
    "total_interactions": 5,
    "total_input_tokens": 1500,
    "total_output_tokens": 2000,
    "total_tokens": 3500,
    "total_cost_usd": 0.0375,
    "average_response_time": 3.2,
    "cost_breakdown": {
      "input_cost_per_1m_tokens": 2.50,
      "output_cost_per_1m_tokens": 10.0,
      "total_input_cost": 0.00375,
      "total_output_cost": 0.02
    }
  }
}
```

**Fields:**
- `model`: LLM model used (e.g., gpt-4o)
- `provider`: LLM provider (azure_openai)
- `total_interactions`: Number of LLM API calls made
- `total_input_tokens`: Total tokens sent to LLM
- `total_output_tokens`: Total tokens received from LLM
- `total_tokens`: Sum of input and output tokens
- `total_cost_usd`: Total cost in USD
- `average_response_time`: Average response time in seconds
- `cost_breakdown`: Detailed cost analysis

### 4. Detailed LLM Logs
```json
{
  "detailed_llm_logs": [
    {
      "timestamp": "2025-07-31T12:00:00",
      "model": "gpt-4o",
      "provider": "azure_openai",
      "prompt": "Analyze this website for vulnerabilities...",
      "response": "Found SQL injection in parameter...",
      "input_tokens": 150,
      "output_tokens": 200,
      "total_tokens": 350,
      "cached_tokens": 0,
      "reasoning_tokens": 0,
      "input_cost": 0.000375,
      "output_cost": 0.002,
      "total_cost": 0.002375,
      "response_time": 2.5,
      "agent_name": "Lead Agent",
      "task_description": "Initial reconnaissance",
      "error": null
    }
  ]
}
```

**Fields:**
- `timestamp`: When the interaction occurred
- `model`: Model used for this interaction
- `provider`: Provider used
- `prompt`: Full prompt sent to LLM
- `response`: Full response received
- `input_tokens`: Tokens in the prompt
- `output_tokens`: Tokens in the response
- `total_tokens`: Sum of input and output
- `cached_tokens`: Number of cached tokens (if any)
- `reasoning_tokens`: Reasoning tokens used (if any)
- `input_cost`: Cost for input tokens
- `output_cost`: Cost for output tokens
- `total_cost`: Total cost for this interaction
- `response_time`: Time taken for this interaction
- `agent_name`: Which agent made the request
- `task_description`: Description of the task
- `error`: Error message if any

### 5. Evidence
```json
{
  "evidence": {
    "raw_report": "Original report content...",
    "prompts_used": [
      "Analyze this website for vulnerabilities",
      "Test the following HTTP request for SQL injection"
    ],
    "responses_received": [
      "Found potential vulnerabilities...",
      "SQL injection confirmed..."
    ]
  }
}
```

**Fields:**
- `raw_report`: Original unprocessed report content
- `prompts_used`: All prompts sent to LLM during the scan
- `responses_received`: All responses received from LLM

## Cost Calculation

The system uses Azure OpenAI pricing:
- **Input tokens**: $2.50 per 1 million tokens
- **Output tokens**: $10.00 per 1 million tokens

Cost calculation formula:
```
input_cost = (input_tokens / 1,000,000) * 2.50
output_cost = (output_tokens / 1,000,000) * 10.00
total_cost = input_cost + output_cost
```

## Usage Examples

### Accessing Vulnerability Information
```python
import json

with open('report.json', 'r') as f:
    report = json.load(f)

# Get all vulnerabilities
vulnerabilities = report['vulnerabilities']
for vuln in vulnerabilities:
    print(f"Found {vuln['type']} - Severity: {vuln['severity']}")

# Get cost information
cost = report['llm_analytics']['total_cost_usd']
print(f"Total scan cost: ${cost:.4f}")
```

### Analyzing LLM Usage
```python
# Get token usage
analytics = report['llm_analytics']
print(f"Total tokens used: {analytics['total_tokens']:,}")
print(f"Average response time: {analytics['average_response_time']:.2f}s")

# Get detailed logs
logs = report['detailed_llm_logs']
for log in logs:
    print(f"Agent: {log['agent_name']} - Cost: ${log['total_cost']:.4f}")
```

## Benefits

1. **Comprehensive Tracking**: Every LLM interaction is logged with full context
2. **Cost Transparency**: Exact cost breakdown for budget management
3. **Evidence Collection**: All prompts and responses preserved for audit
4. **Structured Data**: Easy to parse and integrate with other tools
5. **Performance Metrics**: Response times and token efficiency tracking
6. **Vulnerability Details**: Rich evidence for each finding

## File Locations

- **Reports**: Saved to `reports/` directory
- **Filename Format**: `pentest_report_{target}_{vuln_type}_{timestamp}.json`
- **Test Reports**: `test_reports/` directory for testing

## Integration

The new report format is designed for easy integration with:
- Security dashboards
- SIEM systems
- Vulnerability management tools
- Cost tracking systems
- Audit and compliance tools
