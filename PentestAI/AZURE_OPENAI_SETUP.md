# Azure OpenAI Setup Guide

This guide explains how to configure PentestAI to use Azure OpenAI instead of regular OpenAI.

## Prerequisites

1. **Azure Subscription**: You need an active Azure subscription
2. **Azure OpenAI Resource**: Create an Azure OpenAI resource in the Azure portal
3. **Model Deployment**: Deploy a GPT-4 model (recommended: gpt-4o) in your Azure OpenAI resource

## Step 1: Create Azure OpenAI Resource

1. Go to the [Azure Portal](https://portal.azure.com)
2. Search for "Azure OpenAI" and create a new resource
3. Choose your subscription, resource group, and region
4. Select a pricing tier (Standard is recommended)
5. Wait for the deployment to complete

## Step 2: Deploy a Model

1. Go to your Azure OpenAI resource in the Azure portal
2. Click on "Model deployments" or go to Azure OpenAI Studio
3. Click "Create new deployment"
4. Select a model (recommended: `gpt-4o` or `gpt-4`)
5. Give your deployment a name (e.g., `gpt-4o-deployment`)
6. Configure the deployment settings and create

## Step 3: Get Your Configuration Details

From your Azure OpenAI resource, collect the following information:

1. **API Key**: Go to "Keys and Endpoint" → Copy one of the keys
2. **Endpoint**: Go to "Keys and Endpoint" → Copy the endpoint URL
3. **Deployment Name**: The name you gave your model deployment
4. **API Version**: Use `2024-02-15-preview` (or latest available)

## Step 4: Configure Environment Variables

Edit your `.env` file in the PentestAI directory:

```bash
# Set the LLM provider to Azure OpenAI
LLM_PROVIDER=azure_openai

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_actual_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your-deployment-name
AZURE_OPENAI_API_VERSION=2024-02-15-preview
```

### Example Configuration

```bash
LLM_PROVIDER=azure_openai
AZURE_OPENAI_API_KEY=1234567890abcdef1234567890abcdef
AZURE_OPENAI_ENDPOINT=https://my-pentest-ai.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=gpt-4o-deployment
AZURE_OPENAI_API_VERSION=2024-02-15-preview
```

## Step 5: Test Your Configuration

Run the test script to verify everything is working:

```bash
cd PentestAI
python test_azure_openai.py
```

You should see output like:
```
🎯 Azure OpenAI Configuration Test
============================================================
🧪 Testing Azure OpenAI Configuration...
--------------------------------------------------
✅ LLM Provider: azure_openai
✅ Model/Deployment: gpt-4o-deployment
✅ Endpoint: https://my-pentest-ai.openai.azure.com/
✅ API Version: 2024-02-15-preview
✅ API Key: **********

🔧 Creating Azure OpenAI model instance...
✅ Azure OpenAI model created successfully!

🗣️  Testing Simple Chat Completion...
--------------------------------------------------
📤 Sending test message...
📥 Response: Hello from Azure OpenAI!
✅ Chat completion test successful!

🎉 All tests passed! Azure OpenAI is configured correctly.
```

## Step 6: Run PentestAI

Now you can run PentestAI as usual:

```bash
python main.py --url https://example.com --vuln SQLI
```

## Troubleshooting

### Common Issues

1. **"API key not set" error**
   - Make sure `AZURE_OPENAI_API_KEY` is set in your `.env` file
   - Verify the API key is correct (no extra spaces or characters)

2. **"Endpoint not set" error**
   - Make sure `AZURE_OPENAI_ENDPOINT` is set in your `.env` file
   - Verify the endpoint URL is correct and includes `https://`

3. **"Deployment not set" error**
   - Make sure `AZURE_OPENAI_DEPLOYMENT` is set in your `.env` file
   - Verify the deployment name matches exactly what you created in Azure

4. **"Invalid API version" error**
   - Try using a different API version like `2024-10-21` or `2023-12-01-preview`

5. **"Resource not found" error**
   - Verify your Azure OpenAI resource is deployed and active
   - Check that your deployment is in the "Succeeded" state

6. **Rate limiting errors**
   - Azure OpenAI has different rate limits than regular OpenAI
   - Consider adjusting the request frequency in your configuration

### Getting Help

If you encounter issues:

1. Run the test script: `python test_azure_openai.py`
2. Check the Azure OpenAI resource status in the Azure portal
3. Verify your model deployment is active and has quota allocated
4. Check the Azure OpenAI service health status

## Benefits of Azure OpenAI

- **Enterprise Security**: Enhanced security and compliance features
- **Data Residency**: Keep your data in specific geographic regions
- **Private Networking**: Use private endpoints for enhanced security
- **SLA**: Enterprise-grade service level agreements
- **Integration**: Better integration with other Azure services

## Cost Considerations

- Azure OpenAI pricing may differ from regular OpenAI
- Monitor your usage in the Azure portal
- Set up billing alerts to avoid unexpected charges
- Consider using quota management to control costs
