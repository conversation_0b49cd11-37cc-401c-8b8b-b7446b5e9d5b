import os
import tempfile
from urllib.parse import urlparse


def create_directory_from_url(url: str, scan_type: str) -> str:
        """Create directory structure based on URL and scan type"""
        parsed_url = urlparse(url)
        domain = parsed_url.netloc or "unknown_domain"
        path = parsed_url.path.replace("/", "_") or "root"
        method = "GET"  # Default method, could be extracted from raw request
        
        # Clean up path for filesystem
        path = path.strip("_") or "root"
        
        # Create directory structure: domain/METHOD_path/scan_type/
        dir_path = f"tools_output/{domain}/{method}_{path}/{scan_type}/"
        os.makedirs(dir_path, exist_ok=True)
        return dir_path
    
def save_raw_request(raw_request: str) -> str:
    """Save raw request to temporary file and return path"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(raw_request)
            return f.name
