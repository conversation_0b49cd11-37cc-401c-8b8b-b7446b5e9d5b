import os
import json
import pandas as pd
from io import String<PERSON>
from pdfminer.high_level import extract_text
from docx import Document
from langchain.text_splitter import MarkdownHeaderTextSplitter, RecursiveCharacterTextSplitter

CHUNK_SIZE = 500
CHUNK_OVERLAP = 50

# Header-aware splitter (does not accept chunk_size or chunk_overlap)
markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=[
    ("#", "Header1"),
    ("##", "Header2"),
    ("###", "Header3")
])

# Fallback for plain files (and markdown sub-splitting)
fallback_splitter = RecursiveCharacterTextSplitter(
    chunk_size=CHUNK_SIZE,
    chunk_overlap=CHUNK_OVERLAP
)

def read_file(file_path, ext):
    try:
        if ext == ".pdf":
            return extract_text(file_path)
        elif ext == ".docx":
            doc = Document(file_path)
            return "\n".join([p.text for p in doc.paragraphs])
        elif ext == ".csv":
            df = pd.read_csv(file_path)
            return df.to_csv(index=False)
        elif ext == ".json":
            with open(file_path, "r") as f:
                return json.dumps(json.load(f), indent=2)
        else:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
    except Exception as e:
        print(f"[!] Failed to read {file_path}: {e}")
        return ""

def load_documents_from_folder(folder_path):
    supported_extensions = ['.txt', '.md', '.json', '.log', '.csv', '.pdf', '.docx']
    documents = []

    for file_name in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file_name)
        ext = os.path.splitext(file_name)[1].lower()

        if ext in supported_extensions:
            content = read_file(file_path, ext)
            if not content.strip():
                continue

            if ext == ".md":
                md_chunks = markdown_splitter.split_text(content)
                for section in md_chunks:
                    # Further split each section into manageable chunks
                    sub_chunks = fallback_splitter.split_text(section.page_content)
                    for i, chunk in enumerate(sub_chunks):
                        documents.append({
                            "file_name": f"{file_name} [chunk {i+1}]",
                            "file_content": chunk,
                            "vuln_type": "unspecified"
                        })
            else:
                chunks = fallback_splitter.split_text(content)
                for i, chunk in enumerate(chunks):
                    documents.append({
                        "file_name": f"{file_name} [chunk {i+1}]",
                        "file_content": chunk,
                        "vuln_type": "unspecified"
                    })

    return documents

def add_payload_documents(vector_store, documents):
    for doc in documents:
        existing = vector_store.similarity_search(doc["file_content"], k=1)
        if existing and any(d.metadata.get("file_name") == doc["file_name"] for d in existing):
            print(f"[!] Replacing existing entry for {doc['file_name']}")
        vector_store.add_texts(
            texts=[doc["file_content"]],
            metadatas=[{
                "file_name": doc["file_name"],
                "vuln_type": doc.get("vuln_type", "unspecified"),
                "model_name": doc.get("model_name", "bge-base-en-v1.5")
            }]
        )
