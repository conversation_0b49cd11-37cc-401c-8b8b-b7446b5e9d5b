from operator import itemgetter

def query_similar_payloads(vector_store, query, k=10, sort_by="distance"):
    results = vector_store.similarity_search_with_score(query, k=k * 2)  # overfetch to deduplicate

    # Deduplicate based on page_content (can also include file_name for stricter match)
    seen = set()
    unique_results = []
    for doc, score in results:
        key = (doc.metadata.get("file_name"), doc.page_content.strip())
        if key not in seen:
            seen.add(key)
            unique_results.append((doc, score))
        if len(unique_results) == k:
            break

    # Sort based on score
    if sort_by == "distance":
        unique_results.sort(key=itemgetter(1))
    elif sort_by == "certainty":
        unique_results.sort(key=lambda x: 1 - x[1], reverse=True)

    # Display results
    for r, score in unique_results:
        print("+-----------------------------------------------------------------------------------------------------------------------------+")
        print("Match from ->:", r.metadata.get("file_name"))
        print("Type ->", r.metadata.get("vuln_type"))
        print(f"Score -> {score:.4f}")
        print("Content ->", r.page_content[:500] + "...\n")
        print("+-----------------------------------------------------------------------------------------------------------------------------+")
