from langchain_community.vectorstores import PGVector
from embeddings.router import get_embedding_function
import os
from dotenv import load_dotenv

load_dotenv()

DB_CONN = os.getenv("DB_URL")

def get_vector_store(provider="bge"):
    embedding_fn = get_embedding_function(provider)
    return PGVector(
        collection_name="payload_existing_knowledgebase",
        connection_string=DB_CONN,
        embedding_function=embedding_fn
    )