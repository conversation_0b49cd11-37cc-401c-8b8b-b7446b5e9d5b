from langchain_community.embeddings import OpenAIEmbeddings, HuggingFaceEmbeddings
import os

def get_embedding_function(provider="bge", model=None, api_key=None):
    if provider == "openai":
        return OpenAIEmbeddings(
            model=model or "text-embedding-3-small",
            openai_api_key=api_key or os.getenv("OPENAI_API_KEY")
        )
    elif provider == "bge":
        return HuggingFaceEmbeddings(
            model_name=model or "BAAI/bge-base-en-v1.5"
        )
    else:
        raise ValueError("Unsupported provider: " + provider)