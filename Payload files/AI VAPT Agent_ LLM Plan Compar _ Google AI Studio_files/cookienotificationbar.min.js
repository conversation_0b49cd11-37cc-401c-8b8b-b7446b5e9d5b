(function(){'use strict';var l;function ba(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ea(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var fa=ea(this);function p(a,b){if(b)a:{var c=fa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ca(c,a,{configurable:!0,writable:!0,value:b})}}
p("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;ca(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.g};var d="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",e=0;return b});
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=fa[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(ba(this))}})}return a});function ha(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
var ia=typeof Object.create=="function"?Object.create:function(a){function b(){}b.prototype=a;return new b},ja;if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja;
function q(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.O=b.prototype}function r(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ba(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}
function t(a,b){return Object.prototype.hasOwnProperty.call(a,b)}var oa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)t(d,e)&&(a[e]=d[e])}return a};p("Object.assign",function(a){return a||oa});function pa(){this.m=!1;this.h=null;this.j=void 0;this.g=1;this.v=this.u=0;this.i=null}function qa(a){if(a.m)throw new TypeError("Generator is already running");a.m=!0}pa.prototype.o=function(a){this.j=a};
function sa(a,b){a.i={X:b,la:!0};a.g=a.u||a.v}pa.prototype.return=function(a){this.i={return:a};this.g=this.v};function ta(a,b,c){a.g=c;return{value:b}}function ua(a){this.g=new pa;this.h=a}function va(a,b){qa(a.g);var c=a.g.h;if(c)return wa(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return xa(a)}
function wa(a,b,c,d){try{var e=b.call(a.g.h,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.m=!1,e;var f=e.value}catch(g){return a.g.h=null,sa(a.g,g),xa(a)}a.g.h=null;d.call(a.g,f);return xa(a)}function xa(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.m=!1,{value:b.value,done:!1}}catch(c){a.g.j=void 0,sa(a.g,c)}a.g.m=!1;if(a.g.i){b=a.g.i;a.g.i=null;if(b.la)throw b.X;return{value:b.return,done:!0}}return{value:void 0,done:!0}}
function ya(a){this.next=function(b){qa(a.g);a.g.h?b=wa(a,a.g.h.next,b,a.g.o):(a.g.o(b),b=xa(a));return b};this.throw=function(b){qa(a.g);a.g.h?b=wa(a,a.g.h["throw"],b,a.g.o):(sa(a.g,b),b=xa(a));return b};this.return=function(b){return va(a,b)};this[Symbol.iterator]=function(){return this}}function Ba(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})}
function Ca(a){return Ba(new ya(new ua(a)))}p("Reflect.setPrototypeOf",function(a){return a?a:na?function(b,c){try{return na(b,c),!0}catch(d){return!1}}:null});
p("Promise",function(a){function b(g){this.g=0;this.i=void 0;this.h=[];this.u=!1;var h=this.j();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.h=function(g){if(this.g==null){this.g=[];var h=this;this.i(function(){h.m()})}this.g.push(g)};var e=fa.setTimeout;c.prototype.i=function(g){e(g,0)};c.prototype.m=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=
g[h];g[h]=null;try{k()}catch(m){this.j(m)}}}this.g=null};c.prototype.j=function(g){this.i(function(){throw g;})};b.prototype.j=function(){function g(m){return function(n){k||(k=!0,m.call(h,n))}}var h=this,k=!1;return{resolve:g(this.C),reject:g(this.m)}};b.prototype.C=function(g){if(g===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.D(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.B(g):this.o(g)}};
b.prototype.B=function(g){var h=void 0;try{h=g.then}catch(k){this.m(k);return}typeof h=="function"?this.F(h,g):this.o(g)};b.prototype.m=function(g){this.v(2,g)};b.prototype.o=function(g){this.v(1,g)};b.prototype.v=function(g,h){if(this.g!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.i=h;this.g===2&&this.I();this.A()};b.prototype.I=function(){var g=this;e(function(){if(g.G()){var h=fa.console;typeof h!=="undefined"&&h.error(g.i)}},1)};b.prototype.G=
function(){if(this.u)return!1;var g=fa.CustomEvent,h=fa.Event,k=fa.dispatchEvent;if(typeof k==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=fa.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.i;return k(g)};b.prototype.A=function(){if(this.h!=null){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new c;
b.prototype.D=function(g){var h=this.j();g.S(h.resolve,h.reject)};b.prototype.F=function(g,h){var k=this.j();try{g.call(h,k.resolve,k.reject)}catch(m){k.reject(m)}};b.prototype.then=function(g,h){function k(x,C){return typeof x=="function"?function(y){try{m(x(y))}catch(I){n(I)}}:C}var m,n,u=new b(function(x,C){m=x;n=C});this.S(k(g,m),k(h,n));return u};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.S=function(g,h){function k(){switch(m.g){case 1:g(m.i);break;case 2:h(m.i);break;
default:throw Error("Unexpected state: "+m.g);}}var m=this;this.h==null?f.h(k):this.h.push(k);this.u=!0};b.resolve=d;b.reject=function(g){return new b(function(h,k){k(g)})};b.race=function(g){return new b(function(h,k){for(var m=r(g),n=m.next();!n.done;n=m.next())d(n.value).S(h,k)})};b.all=function(g){var h=r(g),k=h.next();return k.done?d([]):new b(function(m,n){function u(y){return function(I){x[y]=I;C--;C==0&&m(x)}}var x=[],C=0;do x.push(void 0),C++,d(k.value).S(u(x.length-1),n),k=h.next();while(!k.done)})};
return b});p("Object.setPrototypeOf",function(a){return a||na});p("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});p("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
p("WeakMap",function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=r(k);for(var m;!(m=k.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(k){var m=typeof k;return m==="object"&&k!==null||m==="function"}function e(k){if(!t(k,g)){var m=new c;ca(k,g,{value:m})}}function f(k){var m=Object[k];m&&(Object[k]=function(n){if(n instanceof c)return n;Object.isExtensible(n)&&e(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),m=Object.seal({}),
n=new a([[k,2],[m,3]]);if(n.get(k)!=2||n.get(m)!=3)return!1;n.delete(k);n.set(m,4);return!n.has(k)&&n.get(m)==4}catch(u){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(k,m){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!t(k,g))throw Error("WeakMap key fail: "+k);k[g][this.g]=m;return this};b.prototype.get=function(k){return d(k)&&t(k,g)?k[g][this.g]:void 0};b.prototype.has=function(k){return d(k)&&t(k,
g)&&t(k[g],this.g)};b.prototype.delete=function(k){return d(k)&&t(k,g)&&t(k[g],this.g)?delete k[g][this.g]:!1};return b});
p("Map",function(a){function b(){var h={};return h.H=h.next=h.head=h}function c(h,k){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.H;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})}function d(h,k){var m=k&&typeof k;m=="object"||m=="function"?f.has(k)?m=f.get(k):(m=""+ ++g,f.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&t(h[0],m))for(h=0;h<n.length;h++){var u=n[h];if(k!==k&&u.key!==u.key||k===u.key)return{id:m,list:n,index:h,entry:u}}return{id:m,
list:n,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=r(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(r([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var m=k.entries(),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=
4||n.value[1]!="t"||!m.next().done?!1:!0}catch(u){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=k:(m.entry={next:this[1],H:this[1].H,head:this[1],key:h,value:k},m.list.push(m.entry),this[1].H.next=m.entry,this[1].H=m.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.H.next=h.entry.next,
h.entry.next.H=h.entry.H,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].H=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,function(h){return h.value})};e.prototype.forEach=
function(h,k){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});
p("Set",function(a){function b(c){this.g=new Map;if(c){c=r(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(r([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)t(b,d)&&c.push(b[d]);return c}});p("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
p("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
p("String.prototype.includes",function(a){return a?a:function(b,c){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return this.indexOf(b,c||0)!==-1}});
p("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)t(b,d)&&c.push([d,b[d]]);return c}});
p("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});p("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});p("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});p("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});p("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});
function Da(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}p("Array.prototype.entries",function(a){return a?a:function(){return Da(this,function(b,c){return[b,c]})}});p("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});
p("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});p("Array.prototype.keys",function(a){return a?a:function(){return Da(this,function(b){return b})}});p("Array.prototype.values",function(a){return a?a:function(){return Da(this,function(b,c){return c})}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ea=Ea||{},A=this||self;function Fa(a,b){a=a.split(".");for(var c=A,d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}function Ga(a,b){a=a.split(".");b=b||A;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function Ha(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function Ia(a,b,c){return a.call.apply(a.bind,arguments)}
function Ja(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function Ka(a,b,c){Ka=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Ia:Ja;return Ka.apply(null,arguments)}function La(a){return a}
function Ma(a,b){function c(){}c.prototype=b.prototype;a.O=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.va=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Na,Ra=typeof String.prototype.isWellFormed==="function",Sa=typeof TextEncoder!=="undefined";
function Ta(a){var b=!1;b=b===void 0?!1:b;if(Sa){if(b&&(Ra?!a.isWellFormed():/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(a)))throw Error("Found an unpaired surrogate");a=(Na||(Na=new TextEncoder)).encode(a)}else{for(var c=0,d=new Uint8Array(3*a.length),e=0;e<a.length;e++){var f=a.charCodeAt(e);if(f<128)d[c++]=f;else{if(f<2048)d[c++]=f>>6|192;else{if(f>=55296&&f<=57343){if(f<=56319&&e<a.length){var g=a.charCodeAt(++e);if(g>=56320&&g<=57343){f=(f-55296)*1024+g-56320+
65536;d[c++]=f>>18|240;d[c++]=f>>12&63|128;d[c++]=f>>6&63|128;d[c++]=f&63|128;continue}else e--}if(b)throw Error("Found an unpaired surrogate");f=65533}d[c++]=f>>12|224;d[c++]=f>>6&63|128}d[c++]=f&63|128}}a=c===d.length?d:d.subarray(0,c)}return a};function Ua(a){A.setTimeout(function(){throw a;},0)};var Va,Wa=Ga("CLOSURE_FLAGS"),Xa=Wa&&Wa[610401301];Va=Xa!=null?Xa:!1;function Ya(){var a=A.navigator;return a&&(a=a.userAgent)?a:""}var Za,$a=A.navigator;Za=$a?$a.userAgentData||null:null;var ab=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};var bb={},cb=null;function db(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):"=.".indexOf(a[b-1])!=-1&&(c="=.".indexOf(a[b-2])!=-1?c-2:c-1);var d=new Uint8Array(c),e=0;eb(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d}
function eb(a,b){function c(k){for(;d<a.length;){var m=a.charAt(d++),n=cb[m];if(n!=null)return n;if(!/^[\s\xa0]*$/.test(m))throw Error("Unknown base64 encoding at char: "+m);}return k}fb();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}}
function fb(){if(!cb){cb={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));bb[c]=d;for(var e=0;e<d.length;e++){var f=d[e];cb[f]===void 0&&(cb[f]=e)}}}};var gb=typeof Uint8Array!=="undefined",hb=!(Va&&Za&&Za.brands.length>0?0:Ya().indexOf("Trident")!=-1||Ya().indexOf("MSIE")!=-1)&&typeof btoa==="function",ib=/[-_.]/g,jb={"-":"+",_:"/",".":"="};function kb(a){return jb[a]||""}function lb(a){if(!hb)return db(a);a=ib.test(a)?a.replace(ib,kb):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b}var mb={};function nb(a,b){ob(b);this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}function pb(a){ob(mb);var b=a.g;b=b==null||gb&&b!=null&&b instanceof Uint8Array?b:typeof b==="string"?lb(b):null;return b==null?b:a.g=b}var qb;function ob(a){if(a!==mb)throw Error("illegal external caller");};function rb(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var sb=void 0;function tb(a){a=Error(a);rb(a,"warning");return a}function ub(a,b){if(a!=null){var c;var d=(c=sb)!=null?c:sb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),rb(a,"incident"),Ua(a))}};var vb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function wb(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var xb=wb("jas",void 0,!0),yb=wb(void 0,"1oa"),zb=wb(void 0,Symbol()),Ab=wb(void 0,"0ubs"),Bb=wb(void 0,"0ubsb"),Cb=wb(void 0,"0actk"),Db=wb("m_m","ya",!0);var Eb={ka:{value:0,configurable:!0,writable:!0,enumerable:!1}},Fb=Object.defineProperties,B=vb?xb:"ka",Gb,Hb=[];D(Hb,7);Gb=Object.freeze(Hb);function Ib(a,b){vb||B in a||Fb(a,Eb);a[B]|=b}function D(a,b){vb||B in a||Fb(a,Eb);a[B]=b};function Jb(){return typeof BigInt==="function"};var Kb={};function Lb(a,b){return b===void 0?a.g!==Mb&&!!(2&(a.l[B]|0)):!!(2&b)&&a.g!==Mb}var Mb={},Nb=Object.freeze({});function Ob(a,b,c){var d=b&128?0:-1,e=a.length,f;if(f=!!e)f=a[e-1],f=f!=null&&typeof f==="object"&&f.constructor===Object;var g=e+(f?-1:0);for(b=b&128?1:0;b<g;b++)c(b-d,a[b]);if(f){a=a[e-1];for(var h in a)!isNaN(h)&&c(+h,a[h])}};function Pb(a){a.xa=!0;return a};var Qb=Pb(function(a){return typeof a==="number"}),Rb=Pb(function(a){return typeof a==="string"}),Sb=Pb(function(a){return typeof a==="boolean"});var Tb=typeof A.BigInt==="function"&&typeof A.BigInt(0)==="bigint";function Ub(a){var b=a;if(Rb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Qb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Tb?BigInt(a):a=Sb(a)?a?"1":"0":Rb(a)?a.trim()||"0":String(a)}var $b=Pb(function(a){return Tb?a>=Vb&&a<=Wb:a[0]==="-"?Xb(a,Yb):Xb(a,Zb)}),Yb=Number.MIN_SAFE_INTEGER.toString(),Vb=Tb?BigInt(Number.MIN_SAFE_INTEGER):void 0,Zb=Number.MAX_SAFE_INTEGER.toString(),Wb=Tb?BigInt(Number.MAX_SAFE_INTEGER):void 0;
function Xb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var E=0,F=0,ac;function bc(a){var b=a>>>0;E=b;F=(a-b)/4294967296>>>0}function cc(a){if(a<0){bc(-a);var b=r(dc(E,F));a=b.next().value;b=b.next().value;E=a>>>0;F=b>>>0}else bc(a)}function ec(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:fc(a,b)}
function fc(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Jb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+hc(c)+hc(a));return c}function hc(a){a=String(a);return"0000000".slice(a.length)+a}
function ic(){var a=E,b=F;b&2147483648?Jb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=r(dc(a,b)),a=b.next().value,b=b.next().value,a="-"+fc(a,b)):a=fc(a,b);return a}
function jc(a){if(a.length<16)cc(Number(a));else if(Jb())a=BigInt(a),E=Number(a&BigInt(4294967295))>>>0,F=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");F=E=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),F*=1E6,E=E*1E6+d,E>=4294967296&&(F+=Math.trunc(E/4294967296),F>>>=0,E>>>=0);b&&(b=r(dc(E,F)),a=b.next().value,b=b.next().value,E=a,F=b)}}function dc(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var kc=typeof BigInt==="function"?BigInt.asIntN:void 0,lc=typeof BigInt==="function"?BigInt.asUintN:void 0,mc=Number.isSafeInteger,nc=Number.isFinite,oc=Math.trunc,pc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function qc(a){switch(typeof a){case "bigint":return!0;case "number":return nc(a);case "string":return pc.test(a);default:return!1}}function rc(a){if(!nc(a))throw tb("enum");return a|0}
function sc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return nc(a)?a|0:void 0}
function tc(a){var b=0;b=b===void 0?0:b;if(!qc(a))throw tb("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return uc(a);case "bigint":return String(kc(64,a));default:return vc(a)}case 1024:switch(c){case "string":return b=oc(Number(a)),mc(b)?a=Ub(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Jb()?Ub(kc(64,BigInt(a))):Ub(wc(a))),a;case "bigint":return Ub(kc(64,a));default:return mc(a)?Ub(xc(a)):Ub(vc(a))}case 0:switch(c){case "string":return uc(a);case "bigint":return Ub(kc(64,
a));default:return xc(a)}default:throw Error("Unknown format requested type for int64");}}function yc(a){if(a[0]==="-")return!1;var b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467}function zc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Ac(a){if(a<0){cc(a);var b=fc(E,F);a=Number(b);return mc(a)?a:b}b=String(a);if(yc(b))return b;cc(a);return ec(E,F)}
function wc(a){if(zc(a))return a;jc(a);return ic()}function xc(a){a=oc(a);if(!mc(a)){cc(a);var b=E,c=F;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=ec(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function vc(a){a=oc(a);if(mc(a))a=String(a);else{var b=String(a);zc(b)?a=b:(cc(a),a=ic())}return a}function uc(a){var b=oc(Number(a));if(mc(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return wc(a)}
function Bc(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(kc(64,a));if(qc(a)){if(b==="string")return uc(a);if(b==="number")return xc(a)}}function Cc(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(lc(64,a));if(qc(a)){if(b==="string")return b=oc(Number(a)),mc(b)&&b>=0?a=String(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),yc(a)||(jc(a),a=fc(E,F))),a;if(b==="number")return a=oc(a),a>=0&&mc(a)?a:Ac(a)}}
function Dc(a){return a==null||typeof a==="string"?a:void 0};function Ec(a){return a};function Fc(a){var b=La(zb);return b?a[b]:void 0}function Gc(){}function Hc(a,b){for(var c in a)!isNaN(c)&&b(a,+c,a[c])}function Ic(a){var b=new Gc;Hc(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b}function Jc(a,b){b<100||ub(Ab,1)};function Kc(a,b,c,d){var e=d!==void 0;d=!!d;var f=La(zb),g;!e&&vb&&f&&(g=a[f])&&Hc(g,Jc);f=[];var h=a.length;g=4294967295;var k=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var u=h&&a[h-1];u!=null&&typeof u==="object"&&u.constructor===Object?(h--,g=h):u=void 0;if(m&&!(b&128)&&!e){k=!0;var x;g=((x=Lc)!=null?x:Ec)(g-n,n,a,u)+n}}b=void 0;for(x=0;x<h;x++){var C=a[x];if(C!=null&&(C=c(C,d))!=null)if(m&&x>=g){var y=x-n,I=void 0;((I=b)!=null?I:b={})[y]=C}else f[x]=C}if(u)for(var w in u)h=u[w],h!=null&&
(h=c(h,d))!=null&&(x=+w,C=void 0,m&&!Number.isNaN(x)&&(C=x+n)<g?f[C]=h:(x=void 0,((x=b)!=null?x:b={})[w]=h));b&&(k?f.push(b):f[g]=b);e&&La(zb)&&(a=Fc(a))&&a instanceof Gc&&(f[zb]=Ic(a));return f}
function Mc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return $b(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[B]|0;return a.length===0&&b&1?void 0:Kc(a,b,Mc)}if(a!=null&&a[Db]===Kb)return Nc(a);if(a instanceof nb){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(hb){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):
b);b=btoa(c)}else{c===void 0&&(c=0);fb();c=bb[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(var f=0,g=0;f<b.length-2;f+=3){var h=b[f],k=b[f+1],m=b[f+2],n=c[h>>2];h=c[(h&3)<<4|k>>4];k=c[(k&15)<<2|m>>6];m=c[m&63];d[g++]=n+h+k+m}n=0;m=e;switch(b.length-f){case 2:n=b[f+1],m=c[(n&15)<<2]||e;case 1:b=b[f],d[g]=c[b>>2]+c[(b&3)<<4|n>>4]+m+e}b=d.join("")}a=a.g=b}return a}return}return a}var Lc;function Nc(a){a=a.l;return Kc(a,a[B]|0,Mc)};var Pc,Qc;function Rc(a){switch(typeof a){case "boolean":return Pc||(Pc=[0,void 0,!0]);case "number":return a>0?void 0:a===0?Qc||(Qc=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}}
function G(a,b,c,d){d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[B]|0;2048&e&&!(2&e)&&Sc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||D(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var k in h)f=+k,f<g&&(c[f+b]=h[k],delete h[k]);
e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);D(a,e);return a}function Sc(){ub(Cb,5)};function Tc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[B]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Uc(a,c,!1,b&&!(c&16)):(Ib(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Db]===Kb)return b=a.l,c=b[B]|0,Lb(a,c)?a:Vc(a,b,c)?Wc(a,b):Uc(b,c);if(a instanceof nb)return a}function Wc(a,b,c){a=new a.constructor(b);c&&(a.g=Mb);a.h=Mb;return a}function Uc(a,b,c,d){d!=null||(d=!!(34&b));a=Kc(a,b,Tc,d);d=32;c&&(d|=2);b=b&8380609|d;D(a,b);return a}
function Xc(a){if(a.g!==Mb)return!1;var b=a.l;b=Uc(b,b[B]|0);Ib(b,2048);a.l=b;a.g=void 0;a.h=void 0;return!0}function Yc(a){if(!Xc(a)&&Lb(a,a.l[B]|0))throw Error();}function Zc(a,b){b===void 0&&(b=a[B]|0);b&32&&!(b&4096)&&D(a,b|4096)}function Vc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(D(b,c|2),a.g=Mb,!0):!1};var $c={};function ad(a,b,c){b=bd(a.l,b);if(b!==null||c&&a.h!==Mb)return b}function bd(a,b){if(b===-1)return null;var c=b+-1,d=a.length-1;if(!(d<0)){if(c>=d)if(a=a[d],a!=null&&typeof a==="object"&&a.constructor===Object)b=a[b];else if(c===d)b=a;else return;else b=a[c];return b}}function cd(a,b,c){Yc(a);var d=a.l;dd(d,d[B]|0,b,c);return a}
function dd(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[B]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}function ed(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function fd(a){if(vb){var b;return(b=a[yb])!=null?b:a[yb]=new Map}if(yb in a)return a[yb];b=new Map;Object.defineProperty(a,yb,{value:b});return b}
function gd(a,b,c){var d=hd,e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var g=d[f];bd(b,g)!=null&&(e!==0&&(c=dd(b,c,e)),e=g)}a.set(d,e);return e}function id(a){a==null&&(a=void 0);return a}function H(a,b,c){c=id(c);cd(a,b,c);c&&!Lb(c)&&Zc(a.l);return a}function jd(a,b){return a=(2&b?a|2:a&-3)&-273}function kd(a,b){var c=Date.now();c=c==null?c:tc(c);return cd(a,b,c)}function ld(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return cd(a,b,c)};function md(a,b,c){this.g=a;if(c&&!b)throw Error();}
function nd(a){if(typeof a==="string")return new md(lb(a),!0);if(Array.isArray(a))return new md(new Uint8Array(a),!0);if(a.constructor===Uint8Array)return new md(a,!1);if(a.constructor===ArrayBuffer)return a=new Uint8Array(a),new md(a,!1);if(a.constructor===nb){var b=pb(a)||new Uint8Array(0);return new md(b,!0,a)}if(a instanceof Uint8Array)return a=a.constructor===Uint8Array?a:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),new md(a,!1);throw Error();};function od(a,b){this.h=a>>>0;this.g=b>>>0}function pd(a){if(!a)return qd||(qd=new od(0,0));if(!/^\d+$/.test(a))return null;jc(a);return new od(E,F)}var qd;function rd(a,b){this.h=a>>>0;this.g=b>>>0}function sd(a){a=BigInt.asUintN(64,a);return new rd(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))}function td(a){if(!a)return ud||(ud=new rd(0,0));if(!/^-?\d+$/.test(a))return null;jc(a);return new rd(E,F)}var ud;function vd(){this.g=[]}vd.prototype.length=function(){return this.g.length};vd.prototype.end=function(){var a=this.g;this.g=[];return a};function wd(a,b,c){for(;c>0||b>127;)a.g.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.g.push(b)}function xd(a,b){for(;b>127;)a.g.push(b&127|128),b>>>=7;a.g.push(b)}function yd(a,b){if(b>=0)xd(a,b);else{for(var c=0;c<9;c++)a.g.push(b&127|128),b>>=7;a.g.push(1)}};function zd(){this.i=[];this.h=0;this.g=new vd}function Ad(a,b){b.length!==0&&(a.i.push(b),a.h+=b.length)}function Bd(a,b){Cd(a,b,2);b=a.g.end();Ad(a,b);b.push(a.h);return b}function Dd(a,b){var c=b.pop();for(c=a.h+a.g.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.h++;b.push(c);a.h++}function Cd(a,b,c){xd(a.g,b*8+c)}function Ed(a,b,c){if(c!=null)switch(Cd(a,b,0),typeof c){case "number":a=a.g;cc(c);wd(a,E,F);break;case "bigint":c=sd(c);wd(a.g,c.h,c.g);break;default:c=td(c),wd(a.g,c.h,c.g)}}
function Fd(a,b,c){c!=null&&(c=parseInt(c,10),Cd(a,b,0),yd(a.g,c))}function Gd(a,b,c){Cd(a,b,2);xd(a.g,c.length);Ad(a,a.g.end());Ad(a,c)};function Hd(){function a(){throw Error();}Object.setPrototypeOf(a,a.prototype);return a}var Id=Hd(),Jd=Hd(),Kd=Hd(),Ld=Hd(),Md=Hd(),Nd=Hd(),Od=Hd(),Pd=Hd(),Qd=Hd();function J(a,b,c){this.l=G(a,b,c)}J.prototype.toJSON=function(){return Nc(this)};J.prototype[Db]=Kb;J.prototype.toString=function(){return this.l.toString()};function Rd(a,b){this.g=a;a=La(Id);this.h=!!a&&b===a||!1}function Sd(a){var b=b===void 0?Id:b;return new Rd(a,b)}function Td(a,b,c,d,e){b=Ud(b,d);b!=null&&(c=Bd(a,c),e(b,a),Dd(a,c))}var Vd=Sd(Td),Wd=Sd(Td),Xd=Symbol(),Yd=Symbol(),Zd,$d;
function ae(a){var b=be,c=ce,d=a[Xd];if(d)return d;d={};d.wa=a;d.Y=Rc(a[0]);var e=a[1],f=1;e&&e.constructor===Object&&(d.fa=e,e=a[++f],typeof e==="function"&&(d.ma=!0,Zd!=null||(Zd=e),$d!=null||($d=a[f+1]),e=a[f+=2]));for(var g={};e&&Array.isArray(e)&&e.length&&typeof e[0]==="number"&&e[0]>0;){for(var h=0;h<e.length;h++)g[e[h]]=e;e=a[++f]}for(h=1;e!==void 0;){typeof e==="number"&&(h+=e,e=a[++f]);var k=void 0;if(e instanceof Rd)var m=e;else m=Vd,f--;e=void 0;if((e=m)==null?0:e.h){e=a[++f];k=a;var n=
f;typeof e==="function"&&(e=e(),k[n]=e);k=e}e=a[++f];n=h+1;typeof e==="number"&&e<0&&(n-=e,e=a[++f]);for(;h<n;h++){var u=g[h];k?c(d,h,m,k,u):b(d,h,m,u)}}return a[Xd]=d}function Ud(a,b){if(a instanceof J)return a.l;if(Array.isArray(a))return G(a,b[0],b[1],2)};function be(a,b,c){a[b]=c.g}function ce(a,b,c,d){var e,f,g=c.g;a[b]=function(h,k,m){return g(h,k,m,f||(f=ae(d).Y),e||(e=de(d)))}}function de(a){var b=a[Yd];if(!b){var c=ae(a);b=function(d,e){return ee(d,e,c)};a[Yd]=b}return b}function ee(a,b,c){Ob(a,a[B]|0,function(d,e){if(e!=null){var f=fe(c,d);f?f(b,e,d):d<500||ub(Bb,3)}});(a=Fc(a))&&Hc(a,function(d,e,f){Ad(b,b.g.end());for(d=0;d<f.length;d++)Ad(b,pb(f[d])||new Uint8Array(0))})}
function fe(a,b){var c=a[b];if(c)return c;if(c=a.fa)if(c=c[b]){c=Array.isArray(c)?c[0]instanceof Rd?c:[Wd,c]:[c,void 0];var d=c[0].g;if(c=c[1]){var e=de(c),f=ae(c).Y;c=a.ma?$d(f,e):function(g,h,k){return d(g,h,k,f,e)}}else c=d;return a[b]=c}};function ge(a,b,c){if(Array.isArray(b)){var d=b[B]|0;if(d&4)return b;for(var e=0,f=0;e<b.length;e++){var g=a(b[e]);g!=null&&(b[f++]=g)}f<e&&(b.length=f);c&&(D(b,(d|5)&-1537),d&2&&Object.freeze(b));return b}}function K(a,b){return new Rd(a,b)}function he(a,b){return new Rd(a,b)}
function ie(a,b,c){b=Cc(b);if(b!=null){switch(typeof b){case "string":pd(b)}if(b!=null)switch(Cd(a,c,0),typeof b){case "number":a=a.g;cc(b);wd(a,E,F);break;case "bigint":c=BigInt.asUintN(64,b);c=new od(Number(c&BigInt(4294967295)),Number(c>>BigInt(32)));wd(a.g,c.h,c.g);break;default:c=pd(b),wd(a.g,c.h,c.g)}}}function je(a,b,c){b=b==null||typeof b==="boolean"?b:typeof b==="number"?!!b:void 0;b!=null&&(Cd(a,c,0),a.g.g.push(b?1:0))}function ke(a,b,c){b=Dc(b);b!=null&&Gd(a,c,Ta(b))}
function le(a,b,c,d,e){b=Ud(b,d);b!=null&&(c=Bd(a,c),e(b,a),Dd(a,c))}
var me=K(function(a,b,c){b=b==null||typeof b==="number"?b:b==="NaN"||b==="Infinity"||b==="-Infinity"?Number(b):void 0;b!=null&&(Cd(a,c,5),a=a.g,c=ac||(ac=new DataView(new ArrayBuffer(8))),c.setFloat32(0,+b,!0),F=0,b=E=c.getUint32(0,!0),a.g.push(b>>>0&255),a.g.push(b>>>8&255),a.g.push(b>>>16&255),a.g.push(b>>>24&255))},Od),L=K(function(a,b,c){b=Bc(b);if(b!=null){switch(typeof b){case "string":td(b)}Ed(a,c,b)}},Md),ne=he(function(a,b,c){b=ge(Bc,b,!1);if(b!=null)for(var d=0;d<b.length;d++)Ed(a,c,b[d])},
Md),oe=he(function(a,b,c){b=ge(Bc,b,!1);if(b!=null&&b.length){c=Bd(a,c);for(var d=0;d<b.length;d++){var e=b[d];switch(typeof e){case "number":var f=a.g;cc(e);wd(f,E,F);break;case "bigint":e=sd(e);wd(a.g,e.h,e.g);break;default:e=td(e),wd(a.g,e.h,e.g)}}Dd(a,c)}},Md),pe=K(ie,Nd),qe=K(ie,Nd),M=K(function(a,b,c){b=sc(b);b!=null&&b!=null&&(Cd(a,c,0),yd(a.g,b))},Ld),re=he(function(a,b,c){b=ge(sc,b,!0);if(b!=null)for(var d=0;d<b.length;d++){var e=a,f=c,g=b[d];g!=null&&(Cd(e,f,0),yd(e.g,g))}},Ld),N=K(je,Jd),
se=K(je,Jd),O=K(ke,Kd),te=he(function(a,b,c){b=ge(Dc,b,!0);if(b!=null)for(var d=0;d<b.length;d++){var e=a,f=c,g=b[d];g!=null&&Gd(e,f,Ta(g))}},Kd),ue=K(ke,Kd),Q,ve=void 0;ve=ve===void 0?Id:ve;Q=new Rd(function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)le(a,b[f],c,d,e)},ve);
var S=Sd(le),we=K(function(a,b,c){b=b==null||typeof b=="string"||b instanceof nb?b:void 0;b!=null&&Gd(a,c,nd(b).g)},Pd),U=K(function(a,b,c){Fd(a,c,sc(b))},Qd),xe=he(function(a,b,c){b=ge(sc,b,!0);if(b!=null)for(var d=0;d<b.length;d++)Fd(a,c,b[d])},Qd);function ye(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Ib(b,32);b=new a(b)}return b}};function ze(a){this.l=G(a)}q(ze,J);function Ae(a){this.l=G(a)}q(Ae,J);var hd=[1,2,3,4,5];function Be(a){this.l=G(a)}q(Be,J);function Ce(a){this.l=G(a)}q(Ce,J);function De(a){this.l=G(a)}q(De,J);var Ee=ye(De);function Fe(a,b,c){this.qa=a;this.na=b;this.metadata=c}Fe.prototype.getMetadata=function(){return this.metadata};function Ge(a,b){b=b===void 0?{}:b;this.ra=a;this.metadata=b;this.status=null}Ge.prototype.getMetadata=function(){return this.metadata};Ge.prototype.L=function(){return this.status};function He(a){this.name="/google.internal.api.auditrecording.v1.AuditRecordingMobileService/CreateAuditRecord";this.g=a;this.h=Ee}function Ie(a,b){var c=Je;b=b===void 0?{}:b;return new Fe(a,c,b)}He.prototype.getName=function(){return this.name};var Je=new He(function(a){return JSON.stringify(Nc(a))});var Ke=new Set(["SAPISIDHASH","APISIDHASH"]);function Le(a){switch(a){case 200:return 0;case 400:return 3;case 401:return 16;case 403:return 7;case 404:return 5;case 409:return 10;case 412:return 9;case 429:return 8;case 499:return 1;case 500:return 2;case 501:return 12;case 503:return 14;case 504:return 4;default:return 2}}
function Me(a){switch(a){case 0:return"OK";case 1:return"CANCELLED";case 2:return"UNKNOWN";case 3:return"INVALID_ARGUMENT";case 4:return"DEADLINE_EXCEEDED";case 5:return"NOT_FOUND";case 6:return"ALREADY_EXISTS";case 7:return"PERMISSION_DENIED";case 16:return"UNAUTHENTICATED";case 8:return"RESOURCE_EXHAUSTED";case 9:return"FAILED_PRECONDITION";case 10:return"ABORTED";case 11:return"OUT_OF_RANGE";case 12:return"UNIMPLEMENTED";case 13:return"INTERNAL";case 14:return"UNAVAILABLE";case 15:return"DATA_LOSS";
default:return""}};function Ne(a,b,c){c=c===void 0?{}:c;b=Error.call(this,b);this.message=b.message;"stack"in b&&(this.stack=b.stack);this.code=a;this.metadata=c;this.name="RpcError";Object.setPrototypeOf(this,this.constructor.prototype)}q(Ne,Error);Ne.prototype.toString=function(){var a="RpcError("+(Me(this.code)||String(this.code))+")";this.message&&(a+=": "+this.message);return a};function Oe(a){this.l=G(a)}q(Oe,J);function Pe(a){this.l=G(a)}q(Pe,J);var Qe=ye(Pe);function Re(){this.u=this.u;this.G=this.G}Re.prototype.u=!1;Re.prototype.dispose=function(){this.u||(this.u=!0,this.M())};Re.prototype[Symbol.dispose]=function(){this.dispose()};Re.prototype.M=function(){if(this.G)for(;this.G.length;)this.G.shift()()};function Se(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1}Se.prototype.h=function(){this.defaultPrevented=!0};var Te=function(){if(!A.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};A.addEventListener("test",c,b);A.removeEventListener("test",c,b)}catch(d){}return a}();function Ue(a,b){Se.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;a&&this.init(a,b)}Ma(Ue,Se);
Ue.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==
void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.i=a;a.defaultPrevented&&Ue.O.h.call(this)};Ue.prototype.h=function(){Ue.O.h.call(this);var a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};var Ve="closure_listenable_"+(Math.random()*1E6|0);var We=0;function Xe(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.U=e;this.key=++We;this.N=this.R=!1}function $e(a){a.N=!0;a.listener=null;a.proxy=null;a.src=null;a.U=null};function af(a,b){for(var c in a)b.call(void 0,a[c],c,a)}function bf(a,b){var c={},d;for(d in a)c[d]=b.call(void 0,a[d],d,a);return c}var cf="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function df(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<cf.length;f++)c=cf[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function ef(a){this.src=a;this.g={};this.h=0}ef.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=ff(a,b,d,e);g>-1?(b=a[g],c||(b.R=!1)):(b=new Xe(b,this.src,f,!!d,e),b.R=c,a.push(b));return b};function gf(a,b){var c=b.type;if(c in a.g){var d=a.g[c],e=ab(d,b),f;(f=e>=0)&&Array.prototype.splice.call(d,e,1);f&&($e(b),a.g[c].length==0&&(delete a.g[c],a.h--))}}
function ff(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.N&&f.listener==b&&f.capture==!!c&&f.U==d)return e}return-1};var hf="closure_lm_"+(Math.random()*1E6|0),jf={},kf=0;function lf(a,b,c,d,e){if(d&&d.once)mf(a,b,c,d,e);else if(Array.isArray(b))for(var f=0;f<b.length;f++)lf(a,b[f],c,d,e);else c=nf(c),a&&a[Ve]?a.listen(b,c,Ha(d)?!!d.capture:!!d,e):of(a,b,c,!1,d,e)}
function of(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=Ha(e)?!!e.capture:!!e,h=pf(a);h||(a[hf]=h=new ef(a));c=h.add(b,c,d,g,f);if(!c.proxy){d=qf();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Te||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(rf(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");kf++}}
function qf(){function a(c){return b.call(a.src,a.listener,c)}var b=sf;return a}function mf(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)mf(a,b[f],c,d,e);else c=nf(c),a&&a[Ve]?a.i.add(String(b),c,!0,Ha(d)?!!d.capture:!!d,e):of(a,b,c,!0,d,e)}function tf(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)tf(a,b[f],c,d,e);else(d=Ha(d)?!!d.capture:!!d,c=nf(c),a&&a[Ve])?a.unlisten(b,c,d,e):a&&(a=pf(a))&&(b=a.g[b.toString()],a=-1,b&&(a=ff(b,c,d,e)),(c=a>-1?b[a]:null)&&uf(c))}
function uf(a){if(typeof a!=="number"&&a&&!a.N){var b=a.src;if(b&&b[Ve])gf(b.i,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(rf(c),d):b.addListener&&b.removeListener&&b.removeListener(d);kf--;(c=pf(b))?(gf(c,a),c.h==0&&(c.src=null,b[hf]=null)):$e(a)}}}function rf(a){return a in jf?jf[a]:jf[a]="on"+a}function sf(a,b){if(a.N)a=!0;else{b=new Ue(b,this);var c=a.listener,d=a.U||a.src;a.R&&uf(a);a=c.call(d,b)}return a}
function pf(a){a=a[hf];return a instanceof ef?a:null}var vf="__closure_events_fn_"+(Math.random()*1E9>>>0);function nf(a){if(typeof a==="function")return a;a[vf]||(a[vf]=function(b){return a.handleEvent(b)});return a[vf]};function wf(a){switch(a){case 0:return"No Error";case 1:return"Access denied to content document";case 2:return"File not found";case 3:return"Firefox silently errored";case 4:return"Application custom error";case 5:return"An exception occurred";case 6:return"Http response at 400 or 500 level";case 7:return"Request was aborted";case 8:return"Request timed out";case 9:return"The resource is not available offline";default:return"Unrecognized error code"}};var xf=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};function V(){Re.call(this);this.i=new ef(this);this.V=this;this.I=null}Ma(V,Re);V.prototype[Ve]=!0;V.prototype.removeEventListener=function(a,b,c,d){tf(this,a,b,c,d)};
function yf(a,b){var c,d=a.I;if(d)for(c=[];d;d=d.I)c.push(d);a=a.V;d=b.type||b;if(typeof b==="string")b=new Se(b,a);else if(b instanceof Se)b.target=b.target||a;else{var e=b;b=new Se(d,a);df(b,e)}e=!0;var f;if(c)for(f=c.length-1;f>=0;f--){var g=b.g=c[f];e=zf(g,d,!0,b)&&e}g=b.g=a;e=zf(g,d,!0,b)&&e;e=zf(g,d,!1,b)&&e;if(c)for(f=0;f<c.length;f++)g=b.g=c[f],e=zf(g,d,!1,b)&&e}
V.prototype.M=function(){V.O.M.call(this);if(this.i){var a=this.i,b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,$e(d[e]);delete a.g[c];a.h--}}this.I=null};V.prototype.listen=function(a,b,c,d){return this.i.add(String(a),b,!1,c,d)};V.prototype.unlisten=function(a,b,c,d){var e=this.i;a=String(a).toString();if(a in e.g){var f=e.g[a];b=ff(f,b,c,d);b>-1?($e(f[b]),Array.prototype.splice.call(f,b,1),f.length==0&&(delete e.g[a],e.h--),e=!0):e=!1}else e=!1;return e};
function zf(a,b,c,d){b=a.i.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.N&&g.capture==c){var h=g.listener,k=g.U||g.src;g.R&&gf(a.i,g);e=h.call(k,d)!==!1&&e}}return e&&!d.defaultPrevented};function Af(){};var Bf;function Cf(){}Ma(Cf,Af);Cf.prototype.g=function(){return new XMLHttpRequest};Bf=new Cf;/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Df=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Ef(a,b){b=Df.test(b)?b:void 0;b!==void 0&&(a.href=b)};function Ff(a){var b=1;a=a.split(":");for(var c=[];b>0&&a.length;)c.push(a.shift()),b--;a.length&&c.push(a.join(":"));return c};var Gf=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Hf(a){V.call(this);this.headers=new Map;this.P=a||null;this.h=!1;this.g=null;this.v="";this.m=0;this.o="";this.j=this.C=this.A=this.B=!1;this.D=null;this.K="";this.F=!1}Ma(Hf,V);var If=/^https?$/i,Jf=["POST","PUT"];
function Kf(a,b,c){if(a.g)throw Error("[goog.net.XhrIo] Object is active with another request="+a.v+"; newUri="+b);a.v=b;a.o="";a.m=0;a.B=!1;a.h=!0;a.g=a.P?a.P.g():Bf.g();a.g.onreadystatechange=xf(Ka(a.Z,a));try{a.C=!0,a.g.open("POST",String(b),!0),a.C=!1}catch(f){Lf(a,f);return}b=c||"";c=new Map(a.headers);var d=Array.from(c.keys()).find(function(f){return"content-type"==f.toLowerCase()}),e=A.FormData&&b instanceof A.FormData;!(ab(Jf,"POST")>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");
c=r(c);for(d=c.next();!d.done;d=c.next())e=r(d.value),d=e.next().value,e=e.next().value,a.g.setRequestHeader(d,e);a.K&&(a.g.responseType=a.K);"withCredentials"in a.g&&a.g.withCredentials!==a.F&&(a.g.withCredentials=a.F);try{Mf(a),a.A=!0,a.g.send(b),a.A=!1}catch(f){Lf(a,f)}}function Lf(a,b){a.h=!1;a.g&&(a.j=!0,a.g.abort(),a.j=!1);a.o=b;a.m=5;Nf(a);Of(a)}function Nf(a){a.B||(a.B=!0,yf(a,"complete"),yf(a,"error"))}l=Hf.prototype;
l.abort=function(a){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=a||7,yf(this,"complete"),yf(this,"abort"),Of(this))};l.M=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Of(this,!0));Hf.O.M.call(this)};l.Z=function(){this.u||(this.C||this.A||this.j?Pf(this):this.pa())};l.pa=function(){Pf(this)};
function Pf(a){if(a.h&&typeof Ea!="undefined")if(a.A&&Qf(a)==4)setTimeout(a.Z.bind(a),0);else if(yf(a,"readystatechange"),Qf(a)==4){a.h=!1;try{if(Rf(a))yf(a,"complete"),yf(a,"success");else{a.m=6;try{var b=Qf(a)>2?a.g.statusText:""}catch(c){b=""}a.o=b+" ["+a.L()+"]";Nf(a)}}finally{Of(a)}}}function Of(a,b){if(a.g){Mf(a);var c=a.g;a.g=null;b||yf(a,"ready");try{c.onreadystatechange=null}catch(d){}}}function Mf(a){a.D&&(clearTimeout(a.D),a.D=null)}l.isActive=function(){return!!this.g};
function Rf(a){var b=a.L();a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=b===0)a=String(a.v).match(Gf)[1]||null,!a&&A.self&&A.self.location&&(a=A.self.location.protocol.slice(0,-1)),b=!If.test(a?a.toLowerCase():"");c=b}return c}function Qf(a){return a.g?a.g.readyState:0}l.L=function(){try{return Qf(this)>2?this.g.status:-1}catch(a){return-1}};function Sf(a){try{return a.g?a.g.responseText:""}catch(b){return""}}
function Tf(a){var b={};a=(a.g&&Qf(a)>=2?a.g.getAllResponseHeaders()||"":"").split("\r\n");for(var c=0;c<a.length;c++)if(!/^[\s\xa0]*$/.test(a[c])){var d=Ff(a[c]),e=d[0];d=d[1];if(typeof d==="string"){d=d.trim();var f=b[e]||[];b[e]=f;f.push(d)}}return bf(b,function(g){return g.join(", ")})}function Uf(a){return typeof a.o==="string"?a.o:String(a.o)};function Vf(a,b){this.u=a.oa;this.v=b;this.g=a.ua;this.i=[];this.m=[];this.o=[];this.j=[];this.h=[];this.u&&Wf(this)}
function Xf(a,b){var c=new Yf;lf(a.g,"complete",function(){if(Rf(a.g)){var d=Sf(a.g);var e;if(e=b)e=a.g,e.g&&Qf(e)==4?(e=e.g.getResponseHeader("Content-Type"),e=e===null?void 0:e):e=void 0,e=e==="text/plain";if(e){if(!atob)throw Error("Cannot decode Base64 response");d=atob(d)}try{var f=a.v(d)}catch(h){Zf(a,$f(new Ne(13,"Error when deserializing response data; error: "+h+(", response: "+d)),c));return}d=Le(a.g.L());ag(a,bg(a));d==0?cg(a,f):Zf(a,$f(new Ne(d,"Xhr succeeded but the status code is not 200"),
c))}else{d=Sf(a.g);f=bg(a);if(d){var g=dg(a,d);d=g.code;e=g.details;g=g.metadata}else d=2,e="Rpc failed due to xhr error. uri: "+String(a.g.v)+", error code: "+a.g.m+", error: "+Uf(a.g),g=f;ag(a,f);Zf(a,$f(new Ne(d,e,g),c))}})}
function Wf(a){a.u.J("data",function(b){if("1"in b){var c=b["1"];try{var d=a.v(c)}catch(e){Zf(a,new Ne(13,"Error when deserializing response data; error: "+e+(", response: "+c)))}d&&cg(a,d)}if("2"in b)for(b=dg(a,b["2"]),c=0;c<a.o.length;c++)a.o[c](b)});a.u.J("end",function(){ag(a,bg(a));for(var b=0;b<a.j.length;b++)a.j[b]()});a.u.J("error",function(){if(a.h.length!=0){var b=a.g.m;b!==0||Rf(a.g)||(b=6);var c=-1;switch(b){case 0:var d=2;break;case 7:d=10;break;case 8:d=4;break;case 6:c=a.g.L();d=Le(c);
break;default:d=14}ag(a,bg(a));b=wf(b)+", error: "+Uf(a.g);c!=-1&&(b+=", http status code: "+c);Zf(a,new Ne(d,b))}})}function bg(a){var b={},c=Tf(a.g);Object.keys(c).forEach(function(d){b[d]=c[d]});return b}
function dg(a,b){var c=2,d={};try{var e=Qe(b);var f=f===void 0?0:f;var g;c=(g=sc(ad(e,1)))!=null?g:f;var h=h===void 0?"":h;var k;var m=(k=Dc(ad(e,2)))!=null?k:h;var n=e.l;g=n;var u=n[B]|0;n=void 0===Nb?2:4;f=!1;var x=Lb(e,u);n=x?1:n;f=!!f||n===3;x=!x;(n===2||x)&&Xc(e)&&(g=e.l,u=g[B]|0);var C=bd(g,3);var y=Array.isArray(C)?C:Gb;var I=y===Gb?7:y[B]|0;e=I;2&u&&(e|=2);var w=e|1;if(e=!(4&w)){C=y;h=w;k=u;var Oa=!!(2&h);Oa&&(k|=2);for(var za=!Oa,v=!0,z=0,aa=0;z<C.length;z++){var R=C[z],P=Oe,ra=k;if(R!=null&&
R[Db]===Kb)var T=R;else if(Array.isArray(R)){var Pa=R[B]|0;var Oc=Pa|ra&32;Oc|=ra&2;Oc!==Pa&&D(R,Oc);T=new P(R)}else T=void 0;P=T;if(P instanceof Oe){if(!Oa){var Ye=Lb(P);za&&(za=!Ye);v&&(v=Ye)}C[aa++]=P}}aa<z&&(C.length=aa);h|=4;h=v?h&-4097:h|4096;w=za?h|8:h&-9}w!==I&&(D(y,w),2&w&&Object.freeze(y));if(x&&!(8&w||!y.length&&(n===1||(n!==4?0:2&w||!(16&w)&&32&u)))){ed(w)&&(y=Array.prototype.slice.call(y),w=jd(w,u),u=dd(g,u,3,y));I=y;T=w;for(R=0;R<I.length;R++){var da=I[R],Qa=da.l,Aa=Qa[B]|0,Ze=Lb(da,
Aa)?Vc(da,Qa,Aa)?Wc(da,Qa,!0):new da.constructor(Uc(Qa,Aa,!1)):da;da!==Ze&&(I[R]=Ze)}T|=8;w=T=I.length?T|4096:T&-4097;D(y,w)}da=g;Qa=f;Aa=w;n===1||(n!==4?0:2&w||!(16&w)&&32&u)?ed(w)||(w|=!y.length||e&&!(4096&w)||32&u&&!(4096&w||16&w)?2:256,w!==Aa&&D(y,w),Object.freeze(y)):(n===2&&ed(w)&&(y=Array.prototype.slice.call(y),Aa=0,w=jd(w,u),u=dd(da,u,3,y)),ed(w)||(Qa||(w|=16),w!==Aa&&D(y,w)));2&w||!(4096&w||16&w)||Zc(da,u);y.length&&(d["grpc-web-status-details-bin"]=b)}catch(hh){a.g&&a.g.L()===404?(c=5,
m="Not Found: "+String(a.g.v)):(c=14,m="Unable to parse RpcStatus: "+hh)}return{code:c,details:m,metadata:d}}Vf.prototype.J=function(a,b){a=="data"?this.i.push(b):a=="metadata"?this.m.push(b):a=="status"?this.o.push(b):a=="end"?this.j.push(b):a=="error"&&this.h.push(b);return this};function eg(a,b){b=a.indexOf(b);b>-1&&a.splice(b,1)}Vf.prototype.removeListener=function(a,b){a=="data"?eg(this.i,b):a=="metadata"?eg(this.m,b):a=="status"?eg(this.o,b):a=="end"?eg(this.j,b):a=="error"&&eg(this.h,b);return this};
Vf.prototype.cancel=function(){this.g.abort()};function cg(a,b){for(var c=0;c<a.i.length;c++)a.i[c](b)}function ag(a,b){for(var c=0;c<a.m.length;c++)a.m[c](b)}function Zf(a,b){for(var c=0;c<a.h.length;c++)a.h[c](b)}Vf.prototype.cancel=Vf.prototype.cancel;Vf.prototype.removeListener=Vf.prototype.removeListener;Vf.prototype.on=Vf.prototype.J;
function Yf(){var a=Error.call(this);this.message=a.message;"stack"in a&&(this.stack=a.stack);Object.setPrototypeOf(this,this.constructor.prototype);this.name="AsyncStack"}q(Yf,Error);function $f(a,b){b.stack&&(a.stack+="\n"+b.stack);return a};function fg(a){this.i=a.ta||null;this.h=a.sa||!1}Ma(fg,Af);fg.prototype.g=function(){return new gg(this.i,this.h)};function gg(a,b){V.call(this);this.K=a;this.v=b;this.o=void 0;this.status=this.readyState=0;this.responseType=this.responseText=this.response=this.statusText="";this.onreadystatechange=null;this.C=new Headers;this.h=null;this.D="GET";this.F="";this.g=!1;this.A=this.j=this.m=null;this.B=new AbortController}Ma(gg,V);l=gg.prototype;
l.open=function(a,b){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.D=a;this.F=b;this.readyState=1;hg(this)};l.send=function(a){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");if(this.B.signal.aborted)throw this.abort(),Error("Request was aborted.");this.g=!0;var b={headers:this.C,method:this.D,credentials:this.o,cache:void 0,signal:this.B.signal};a&&(b.body=a);(this.K||A).fetch(new Request(this.F,b)).then(this.ja.bind(this),this.T.bind(this))};
l.abort=function(){this.response=this.responseText="";this.C=new Headers;this.status=0;this.B.abort();this.j&&this.j.cancel("Request was aborted.").catch(function(){});this.readyState>=1&&this.g&&this.readyState!=4&&(this.g=!1,ig(this));this.readyState=0};
l.ja=function(a){if(this.g&&(this.m=a,this.h||(this.status=this.m.status,this.statusText=this.m.statusText,this.h=a.headers,this.readyState=2,hg(this)),this.g&&(this.readyState=3,hg(this),this.g)))if(this.responseType==="arraybuffer")a.arrayBuffer().then(this.ha.bind(this),this.T.bind(this));else if(typeof A.ReadableStream!=="undefined"&&"body"in a){this.j=a.body.getReader();if(this.v){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=
[]}else this.response=this.responseText="",this.A=new TextDecoder;jg(this)}else a.text().then(this.ia.bind(this),this.T.bind(this))};function jg(a){a.j.read().then(a.ga.bind(a)).catch(a.T.bind(a))}l.ga=function(a){if(this.g){if(this.v&&a.value)this.response.push(a.value);else if(!this.v){var b=a.value?a.value:new Uint8Array(0);if(b=this.A.decode(b,{stream:!a.done}))this.response=this.responseText+=b}a.done?ig(this):hg(this);this.readyState==3&&jg(this)}};
l.ia=function(a){this.g&&(this.response=this.responseText=a,ig(this))};l.ha=function(a){this.g&&(this.response=a,ig(this))};l.T=function(){this.g&&ig(this)};function ig(a){a.readyState=4;a.m=null;a.j=null;a.A=null;hg(a)}l.setRequestHeader=function(a,b){this.C.append(a,b)};l.getResponseHeader=function(a){return this.h?this.h.get(a.toLowerCase())||"":""};
l.getAllResponseHeaders=function(){if(!this.h)return"";for(var a=[],b=this.h.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};function hg(a){a.onreadystatechange&&a.onreadystatechange.call(a)}Object.defineProperty(gg.prototype,"withCredentials",{get:function(){return this.o==="include"},set:function(a){this.o=a?"include":"same-origin"}});function kg(a){var b="";af(a,function(c,d){b+=d;b+=":";b+=c;b+="\r\n"});return b};function lg(a){a=a===void 0?{}:a;this.aa=a.aa||Ga("suppressCorsPreflight",a)||!1;this.withCredentials=a.withCredentials||Ga("withCredentials",a)||!1;this.ba=a.ba||[];this.W=a.W;this.g=a.za||!1}
function mg(a,b,c,d,e){e=e===void 0?{}:e;var f=b.substring(0,b.length-Je.name.length),g,h=(g=e)==null?void 0:g.signal;return ng(function(k){return new Promise(function(m,n){if(h==null?0:h.aborted){var u=new Ne(1,"Aborted");u.cause=h.reason;n(u)}else{var x={},C=og(a,k,f);C.J("error",function(y){return void n(y)});C.J("metadata",function(y){x=y});C.J("data",function(y){var I=x;I=I===void 0?{}:I;m(new Ge(y,I))});h&&h.addEventListener("abort",function(){C.cancel();var y=new Ne(1,"Aborted");y.cause=h.reason;
n(y)})}})},a.ba).call(a,Ie(c,d)).then(function(k){return k.ra})}
function og(a,b,c){var d=b.na,e=b.getMetadata();var f=a.g&&!1;f=a.W||f?new Hf(new fg({ta:a.W,sa:f})):new Hf;c+=d.getName();e["Content-Type"]="application/json+protobuf";e["X-User-Agent"]="grpc-web-javascript/0.1";var g=e.Authorization;if(g&&Ke.has(g.split(" ")[0])||a.withCredentials)f.F=!0;if(a.aa){b:{for(h in e){var h=!1;break b}h=!0}if(!h)if(a=kg(e),typeof c==="string"){if(h=encodeURIComponent("$httpHeaders"),a=a!=null?"="+encodeURIComponent(String(a)):"",h+=a){a=c.indexOf("#");a<0&&(a=c.length);
g=c.indexOf("?");if(g<0||g>a){g=a;var k=""}else k=c.substring(g+1,a);c=[c.slice(0,g),k,c.slice(a)];a=c[1];c[1]=h?a?a+"&"+h:h:a;c=c[0]+(c[1]?"?"+c[1]:"")+c[2]}}else c.g("$httpHeaders",a)}else for(h=r(Object.keys(e)),a=h.next();!a.done;a=h.next())a=a.value,f.headers.set(a,e[a]);h=new Vf({ua:f,oa:void 0},d.h);Xf(h,e["X-Goog-Encode-Response-If-Executable"]==="base64");b=d.g(b.qa);Kf(f,c,b);return h}function ng(a,b){return b.reduce(function(c,d){return function(e){return d.intercept(e,c)}},a)};function pg(){var a={};a=a||{};a.format="jspb";this.g=new lg(a);this.h="https://auditrecording-pa.googleapis.com".replace(/\/+$/,"")};var qg=[0,N,-1];var rg=[0,[1,2,3,4],S,[0,[0,N,-1,U,N],[0,N,-1],N,U],S,[0,U],S,[0,U,-2],S,[0,U,-1]];var sg=[0,M];function tg(a){this.l=G(a)}q(tg,J);var W=[0,U];var ug=[0,W,N,-4,W,N,W,U,N,M];var vg=[0,W,-1,O];var wg=[0,W,U];var xg=[0,U];var yg=[0,we];var zg=[0,1,U,W];var Ag=[0,W,N];var Bg=[0,W,N];var Cg=[0,W];var Dg=[0,U,W];var Eg=[0,W];var Fg=[0,W];var Gg=[0,W];var Hg=[0,Q,[0,O,-2],U];var Ig=[0,W];var Jg=[0,W,-3,U];var Kg=[0,U];var Lg=[0,U];var Mg=[0,W,-6];var Ng=[0,W];var Og=[0,U];var Pg=[0,W];var Qg=[0,W];var Rg=[0,O];var Sg=[0,O];var Tg=[0,O];var Ug=[0,W];var Vg=[0,W];var Wg=[0,W,-1];function Xg(a){this.l=G(a)}q(Xg,J);var Yg=[0,W];var Zg=[0,U,1,N];var $g=[0,[1,2,3,4,5,6,7,8],S,[0,W,-1,U,-2],S,[0,U,-3],S,[0,U],S,[0,W,-1,U],S,[0,U],S,[0,U,-1],S,[0,W],S,[0,U]];var ah=[0,W];var bh=[0,W];var ch=[0,W,-7,1,W,-5,2,W,-4];var dh=[0,[1,2,3,4,5,6,7,8,9,11,12,15,16,18,19,20,21,22,23,24,25,26,27],S,[0,Q,[0,U,-1]],S,[0,U],S,[0,O],S,[0,W],S,[0,W],S,[0,W],S,[0,W],S,[0,W],S,[0,W],1,S,[0,W],S,[0,W],2,S,[0,W],S,[0,W],1,S,[0,W],S,[0,O],S,[0,W],S,[0,W],S,[0,W],S,[0,W],S,[0,U],S,[0,W],S,[0,W],S,[0,W]];var eh=[0,[1,2,3],S,[0,W,N,-2],S,[0,N],S,[0,N]];var fh=[0,[1,2,3,4],S,[0,N,W],S,[0,W],S,[0,W],S,[0,W]];var gh=[0,[1,2,3,4,5,6,7,8],S,[0,U],S,[0,U],S,[0,U],S,[0,U],S,[0,U],S,[0,U],S,[0,U],S,[0,U]];var ih=[0,W,O];var jh=[0,Q,[0,U,-1]];var kh=[0,W];var lh=[0,W,-1,N];var mh=[0,Q,[0,U,-1],[0]];var nh=[0,N,[0,O,-1,U]];var oh=[0,W];var ph=[0,W,O];var qh=[0,[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],S,[0,W,M,-1],S,[0,W],S,[0,W],S,[0,W],S,[0,W],S,ph,S,[0,W,O],S,ph,-1,S,[0,W],S,[0,W],S,[0,O,N,te],S,[0,W],S,[0,W,te],S,[0,W],S,[0,W],S,[0,W],S,[0,W],S,[0,W,te],S,[0,W],S,[0,W]];var rh=[0,U,Q,[0,U,W],O];var sh=[0,O];var th=[0,W];var uh=[0,W,xe];var vh=[0,W];var wh=[0,W];var xh=[0,W];var yh=[0,W];var zh=[0,W];var Ah=[0,W];var Bh=[0,[1,2,3,4,5],S,[0,W],S,[0,W],S,[0,W],S,[0,Q,[0,U,-1]],S,[0,W]];var Ch=[0,W,-1];var Dh=[0,W];var Eh=[0,W];var Fh=[0,W];var Gh=[0,W];var Hh=[0,W];var Ih=[0,U,M];var Jh=[0,M,U,M];var Kh=[0,W,U,M];var Lh=[0,W,U,M];var Mh=[0,we];var Nh=[0,3,L,Q,[0,O],2,L];var Oh=[0,W];var Ph=[0,W];var Qh=[0,O];var Rh=[0,W,O,U];var Sh=[0,O,W];var Th=[0,W,U,Q,[0,O]];var Uh=[0,[0,W],-8];var Vh=[0,xe,[0,[0,O],[0,O]]];var Wh=[0,U,ne];var Xh=[0,W];var Yh=[0,W];var Zh=[0,W,[0,U,te,N,-2,U,L,W,N,[0,O,U,W],[0,W]],[0,W,N],U,-2];var $h=[0,W];var ai=[0,W];var bi=[0,W,[0,U]];var ci=[0,W];var di=[0,W,O];var ei=[0,W,U];var fi=[0,W];var gi=[0,W];var hi=[0,W,O];var ii=[0,W,[0,te,L,-1,N,L,N],[0,N,-1,U]];var ji=[0,W,-4,U,-1,Q,[0,O,U,W],W,-1,U,M,W];var ki=[0,W];var li=[0,W];var mi=[0,N];var ni=[0,U,N];var oi=[0,W];var pi=[0,W];var qi=[0,U,W,U,W];var ri=[0,W];var si=[0,W,N];var ti=[0,L];var ui=[0,W,-4];var vi=[0,W,U,-1];function wi(a){this.l=G(a)}q(wi,J);var xi=[0,ni,ug,rh,vg,qh,Zh,$h,ii,ji,yg,Zg,wg,Lg,vi,Vh,Wh,dh,Bh,Jg,Ch,$g,Mg,Cg,Ig,Ug,si,li,Kg,ch,sh,Ng,kh,mh,Vg,bi,1,Ph,Ih,Mh,nh,xg,lh,fh,Nh,1,Rh,Ag,Tg,Sh,ri,ki,gh,Uh,Th,Wg,Sg,ah,pi,qi,ci,1,Rg,Oh,zg,oi,Gg,Dh,hi,oh,jh,di,Xh,fi,Og,Kh,Lh,bh,ui,sg,Hg,ti,Jh,vh,xh,Hh,qg,Yg,rg,zh,wh,yh,uh,ai,th,Yh,Pg,Qg,Eh,Eg,Ah,eh,Fg,Gh,Qh,1,Dg,gi,Fh,Bg,ih,ei,mi];var yi=[0,O,[0,O,-1]];var zi=[0,M,-1,we,-1,M,we,-1,N];var Ai=[0,L];var Bi=[0,[1,2],ue,se];function Ci(a){this.l=G(a)}q(Ci,J);var Di=[0,O];var Ei=[0,O];var Fi=[0,O];var Gi=[0,L];var Hi=[0,O];var Ii=[0,O];var Ji=[0,[0,O,-2],[0,O]];var Ki=[0,pe];var Li=[0,O];var Mi=[0,[1,3],ue,1,ue];var Ni=[0,O];var Oi=[0,O];var Pi=[0,[1,2],qe,ue];function Qi(a){this.l=G(a)}q(Qi,J);var Ri=[0,Bi,yi,U,Mi,Fi,Li,Ei,Ii,Ni,Ji,Hi,Pi,Gi,1,Oi,Ki,zi,Ai,Di];var Si=[0,U,Ri];function Ti(a){this.l=G(a)}q(Ti,J);Ti.prototype.getName=function(){var a=ad(this,1,$c);return a==null?a:nc(a)?a|0:void 0};var Ui=[0,U,xi];var Vi=[0,O,L,O];function Wi(a){this.l=G(a)}q(Wi,J);function Xi(a){this.l=G(a)}q(Xi,J);function Yi(a){this.l=G(a)}q(Yi,J);var X=[0,L,Q,[0,M,O]];var Y=[0,re];var Zi=[0,N,Y,X,Y,-1];var $i=[0,N,Y,-3];var aj=[0,ne];var bj=[0,M,Q,[0,O,-1,M,O]];var cj=[0,L,Q,[0,M,O,-1]];var Z=[0,Q,[0,O,-1]];var dj=[0,Y,X,-3];var ej=[0,[2,5],X,S,Y,Q,X,Y,S,X];var fj=[0,U,O,[0,[0,U],Y,[0,Y,-3,$i,-1,[0,Y,-2],-1,$i,-2],[0,Y,$i,-1],[0,Q,[0,U,cj,-2],cj,-5],[0,$i,Y,$i],[0,X,-1,Y,Zi,Y,-1,N,Y,Zi,Y],$i,[0,Y,X,Y,-2,X,Y,-1,[0,Y,Q,[0,[2,4],X,S,Y,Q,X,S,X],Y,X,-1,Y],[0,Y,Q,X,Y],Y,-2],[0,[6,28],Y,-3,X,S,Y,Y,X,[0,Y,-1,1,Q,X],Y,X,Q,ej,Y,-6,X,[0,Y,Q,X,Y,-4],[0,[3,4],Y,Q,ej,S,Y,S,X,X],X,-1,1,Zi,[0,Y,dj,Y,Q,[0,[3,4],Y,X,S,Y,S,X]],S,X,1,X,-1,[0,Y,-3],Y,-1,[0,Y,Q,Y,Y],X,dj,Y,-1,[0,Y,-3],[0,X,Y,-2]],aj,[0,Y,-1,Q,Y,X,Y,-1,X,Y],[0,Y,-1,Q,X,Y,-3],[0,Q,[0,O]],1,[0,L,Q,[0,O,-1]],
[0,Y],[0,Y,-1],Z,X,[0,Y,-2,O,-1],[0,Q,[0,O,-1],Q,[0,[0,me,-2,[0,me]],O],Q,[0,O,L,O,-1],Q,[0,O,-1]],[0,O,-1],[0,U,O,L,Y,O,-5,aj,O],[0,[0,O]],[0,Y,-6,Zi,1,X,Zi,X,Zi,$i,X,2,X,-1,Y,Zi,-1],[0,Y,-5],[0,Y,-2,Zi],[0,Y,-3,O,Y,-3,O],[0,Y,-2,O,Y,-4,1,Y],[0,Y],[0,[0,Y,-2,O,-1,aj],-8],Zi,1,[0,3,L,-2,oe],[0,$i,Y,-2,$i],1,[0,O],[0,Y,-1],[0,Y,-1],[0,Y,-1],[0,Z,-1],[0,Z,-1],1,[0,Y,O,Y,-3,O],[0,O,Y,-1,O,Y,$i],[0,Z,-5,1,Z,-1],[0,Y,-3,O,-1],[0,Y,-4],[0,Y,-4],[0,Y,-4,[0,Y,-3]],[0,[0,Z,-3]],[0,Z,-4,[0,Z,-3]],[0,aj,-1],
[0,O,M],[0,O,M],[0,O,M],[0,[0,U,Y,-2,O,-1],[0,U,Z,-2,O,-1]],[0,Z,-4],[0,O,-1],[0,te,-3,O,-1],[0,O,M],[0,O,aj,O],[0,Y,O,te],[0,Y],[0,O,M],[0,O,-1],[0,Y,-3],[0,Y,-1],[0,Z,-1],[0,Z,-3],[0,O],[0,Y,-3,O,-1],[0,Y,-3],[0,Y,O,-2],[0,bj,Q,[0,bj,-2],M,-1],[0,O,Y,O,-1],[0,U],[0,O,-1,L]],Vi];function gj(a){this.l=G(a)}q(gj,J);gj.prototype.i=function(a){return function(){var b=new zd;ee(this.l,b,ae(a));Ad(b,b.g.end());for(var c=new Uint8Array(b.h),d=b.i,e=d.length,f=0,g=0;g<e;g++){var h=d[g];c.set(h,f);f+=h.length}b.i=[c];return c}}([0,Q,Si,Ui,fj,Ri,-1]);var hj="ar bg ca cs da de el en en-GB es es-419 et eu fi fr fr-CA gl hr hu is it iw ja ko lt lv mt nl no pl pt-BR pt-PT ro ru sk sl sr sv tr uk".split(" ");function ij(a){if(typeof a!=="string")return!1;a=a.split("-");return a.length!==5||a[0].length!==8||a[1].length!==4||a[2].length!==4||a[3].length!==4||a[4].length!==12?!1:!0}function jj(a){return Math.floor((1+Math.random())*Math.pow(16,a)).toString(16).substring(1)};function kj(a,b,c,d,e){var f,g,h,k,m,n,u,x,C,y,I,w,Oa;return Ca(function(za){if(za.g==1){f=new pg;var v=new Qi;var z=new Ci;z=ld(z,1,c);g=H(v,19,z);h=a?1:2;v=new Xg;z=new tg;z=cd(z,1,h==null?h:rc(h));k=H(v,1,z);v=new wi;m=H(v,87,k);v=new Ti;v=cd(v,1,rc(535));n=H(v,2,m);v=new Wi;v=ld(v,1,d);u=ld(v,2,b);v=new Yi;v=cd(v,1,rc(969));z=new Xi;z=H(z,60,u);x=H(v,3,z);v=new gj;v=H(v,4,g);v=H(v,5,g);v=H(v,2,n);C=H(v,3,x);v=new Be;v=kd(v,1);y=kd(v,2);v=new ze;v=ld(v,1,Ya());I=ld(v,2,e);v=new Ce;v=H(v,4,y);if(!nc(264))throw tb("int32");
v=cd(v,2,264);z=C.i();if(z!=null)if(typeof z==="string")z=z?new nb(z,mb):qb||(qb=new nb(null,mb));else if(z.constructor!==nb)if(gb&&z!=null&&z instanceof Uint8Array)z=z.length?new nb(new Uint8Array(z),mb):qb||(qb=new nb(null,mb));else throw Error();v=cd(v,1,z);z=new Ae;var aa=I;aa=id(aa);a:{var R=aa;Yc(z);var P=z.l,ra=P[B]|0;if(R==null){var T=fd(P);if(gd(T,P,ra)===5)T.set(hd,0);else break a}else{T=fd(P);var Pa=gd(T,P,ra);Pa!==5&&(Pa&&(ra=dd(P,ra,Pa)),T.set(hd,5))}dd(P,ra,5,R)}aa&&!Lb(aa)&&Zc(z.l);
w=H(v,3,z);Oa={"X-Goog-Api-Key":"AIzaSyDtMB89gyutGObyfQggx9OOfjS0QWFYpFc"};v=f.g;z=f.h+"/$rpc/google.internal.api.auditrecording.v1.AuditRecordingMobileService/CreateAuditRecord";aa=w;R=Oa||{};P=void 0;P=P===void 0?{}:P;v=mg(v,z,aa,R,P);return ta(za,v,2)}return za.return(c)})};var lj={TWO_A:"2A",TWO_B:"2B"},mj={ACCEPTED:"ACCEPTED",REJECTED:"REJECTED",UNKNOWN:"UNKNOWN"},nj=/\[(.+)\]/;function oj(a){this.h=a;this.delay=250}oj.prototype.debounce=function(){clearTimeout(this.g);this.g=window.setTimeout(this.h,this.delay)};oj.prototype.cancel=function(){clearTimeout(this.g)};window.dataLayer=window.dataLayer||[];var pj=0;
function qj(a,b){var c=this;this.root=a=a===void 0?document.body:a;this.i=[];this.m=document.createElement("div");this.A=!1;this.j="UNKNOWN";this.I=!1;this.P=function(d){c.g=d.eea;var e=rj(c.options,c.g);if(e=sj(c).find(e)){var f=new Date;f.setMonth(f.getMonth()-13);e=e.date>f?e.status:"UNKNOWN"}else e="UNKNOWN";c.j=d.required||e!=="UNKNOWN"?e:"ACCEPTED";c.options.category==="2A"&&window.dataLayer.unshift(tj("set","url_passthrough",!0),uj("default",!1,!1),uj("update",c.u,c.o),vj(c.g||!d.required?
"UNKNOWN":c.status,c.g,c.u,c.o));e={required:d.required,status:c.status};c.g&&(e.eea=c.g);wj(c,"loaded",e);if(d.required)return c.listen("statuschange",c.da),c.status==="UNKNOWN"&&c.open(),xj(c,d)};this.K=function(d){d.preventDefault();c.status="ACCEPTED";c.close()};this.G=function(d){d.preventDefault();c.open(!0)};this.B=function(d){switch(d.key){case "Enter":case " ":d.preventDefault(),c.open(!0)}};this.V=function(d){d.preventDefault();c.status="REJECTED";c.close()};this.da=function(d){d=d.detail.status;
if(c.options.category==="2A"&&!c.g){var e=c.status==="ACCEPTED";window.dataLayer.push(uj("update",c.options.analyticsStorage||e,e),yj(c.status,c.g,c.u,c.o))}var f=c.options;e=f.siteId;var g={category:f.category,date:new Date,eea:c.g,siteId:e,status:d};c.F&&d!=="UNKNOWN"&&(f=zj(c),ij(f)||(f="randomUUID"in crypto?crypto.randomUUID():jj(8)+"-"+jj(4)+"-4"+jj(3)+"-8"+jj(3)+"-"+jj(12)),f?kj(d==="ACCEPTED",e,f,c.v,Aj(c.options.language)).then(function(h){h&&(g.uuid=h);Bj(c,g)}).catch(function(h){console.error(h);
Bj(c,g)}):console.error("ARI UUID could not be generated."));Bj(c,g)};this.ca=function(){Cj(c)};this.C=function(){c.ea.debounce()};this.options=Object.assign({},qj.g,b);pj+=1;this.id="glue-cookie-notification-bar-"+pj;window.glueCookieNotificationBarLoaded&&this.listen("loaded",window.glueCookieNotificationBarLoaded);this.h=document.querySelector("."+this.options.autoMarginClass);this.ea=new oj(this.ca);this.v="";this.config=Dj(this);this.rendered=this.config.then(this.P)}
function Dj(a){var b,c,d,e,f,g;return Ca(function(h){switch(h.g){case 1:return c=(b=Aj(a.options.language))?"?hl="+b:"",d=a.options.category==="2A"?"/glue/cookienotificationbar/config/2a.json":"/glue/cookienotificationbar/config/2b.json",h.u=2,ta(h,fetch("https://www.gstatic.com"+d+c),4);case 4:return e=h.j,ta(h,e.json(),5);case 5:return f=h.j,h.return(f);case 2:h.u=0;var k=h.i.X;h.i=null;g=k;console.error(g);return h.return({required:!1})}})}
function xj(a,b){var c,d,e,f,g,h,k,m,n,u;return Ca(function(x){if(x.g==1)return ta(x,Ej(),2);a.root||(a.root=document.body);c=Object.assign({},b.content,a.options.content);d=a.id+"-label";e=c.mainText.replace(nj,function(){return a.options.siteId});f=document.createElement("div");f.classList.add("glue-cookie-notification-bar");f.id=a.id;f.setAttribute("aria-hidden","true");f.setAttribute("aria-labelledby",d);f.setAttribute("inert","true");f.setAttribute("role","region");f.dataset.nosnippet="";f.tabIndex=
-1;a.options.language!==b.language&&(f.dir=b.rtl?"rtl":"ltr",f.lang=b.language);g=document.createElement("p");g.classList.add("glue-cookie-notification-bar__text");f.appendChild(g);h=document.createElement("span");h.id=d;h.textContent=e+" ";g.appendChild(h);k=document.createElement("a");Ef(k,c.moreUrl);k.classList.add("glue-cookie-notification-bar__more");k.rel="noopener";k.setAttribute("aria-describedby",d);k.target="_blank";k.textContent=c.moreText;k.ariaLabel=c.moreAriaLabelText;g.appendChild(k);
m=b.language==="ja"?"\u3002":".";g.appendChild(document.createTextNode(m));n=document.createElement("button");n.addEventListener("click",a.K);n.classList.add("glue-cookie-notification-bar__accept");n.textContent=c.consentText;f.appendChild(n);c.rejectText&&(u=document.createElement("button"),u.addEventListener("click",a.V),u.classList.add("glue-cookie-notification-bar__reject"),u.textContent=c.rejectText,f.appendChild(u));c.managementText&&Fj(a,c.managementText);a.h&&window.addEventListener("resize",
a.C);a.v=e+" <a href='"+c.moreUrl+"'>"+c.moreText+"</a>.";a.root.prepend(f);return x.return(f)})}
function Fj(a,b){var c=a.i.push,d=c.apply,e=a.i,f=document.querySelectorAll(".glue-cookie-notification-bar-control");if(!(f instanceof Array)){f=r(f);for(var g,h=[];!(g=f.next()).done;)h.push(g.value);f=h}d.call(c,e,f);c=r(a.i);for(d=c.next();!d.done;d=c.next())d=d.value,d.addEventListener("click",a.G),d.removeAttribute("aria-hidden"),d.setAttribute("aria-controls",a.id),d.setAttribute("aria-expanded","false"),d.textContent=b,d instanceof HTMLButtonElement||d.addEventListener("keydown",a.B),Gj(d.parentElement)&&
d.parentElement.removeAttribute("aria-hidden")}l=qj.prototype;l.open=function(a){return Hj(this,!0,a===void 0?!1:a)};l.close=function(){return Hj(this,!1)};
function Hj(a,b,c){c=c===void 0?!1:c;var d,e,f,g,h;return Ca(function(k){if(k.g==1)return ta(k,a.rendered,2);d=k.j;if(!d||b===a.visible)return d&&b&&c&&d.focus(),k.return();if(!a.A){a.A=!0;var m=document.querySelector("html");if(m){m=window.getComputedStyle(m).fontSize;var n=Number(m.replace("px",""));window.getComputedStyle(d).fontSize===m&&n<=18&&(d.style.fontSize=16/n+"rem")}}b?(d.removeAttribute("aria-hidden"),d.removeAttribute("inert"),c&&d.focus()):(d.setAttribute("aria-hidden","true"),d.setAttribute("inert",
"true"),d.blur());e=""+b;f=r(a.i);for(g=f.next();!g.done;g=f.next())h=g.value,h.setAttribute("aria-expanded",e);a.I=b;wj(a,"visibilitychange",{visible:b});return ta(k,Cj(a),0)})}l.listen=function(a,b){this.m.addEventListener(a,b)};l.unlisten=function(a,b){this.m.removeEventListener(a,b)};function wj(a,b,c){a.m.dispatchEvent(new CustomEvent(b,{detail:c}))}
function sj(a){try{var b=window.localStorage.getItem("glue.CookieNotificationBar"),c=b&&JSON.parse(b);return Array.isArray(c)?c.map(function(d){if(!(d instanceof Object)||Array.isArray(d))return null;var e=d.category,f=d.date,g=d.eea,h=d.siteId,k=d.status;d=d.uuid;var m=(f=f&&new Date(f))&&!isNaN(Number(f))&&Object.values(lj).includes(e)&&typeof h==="string"&&(!a.F||d)&&Object.values(mj).includes(k);d=d?{uuid:d}:{};return m?Object.assign({},{category:e,date:f,eea:g,siteId:h,status:k},d):null}).filter(function(d){return!!d}):
[]}catch(d){return console.error(d),[]}}function zj(a){var b=rj(a.options,a.g),c;return(c=sj(a).filter(b).find(function(d){return d.uuid}))==null?void 0:c.uuid}function Bj(a,b){var c=rj(a.options,a.g);a=sj(a).filter(function(d){return!c(d)});a.push(b);try{window.localStorage.setItem("glue.CookieNotificationBar",JSON.stringify(a))}catch(d){console.error(d)}}
function Cj(a){var b,c;return Ca(function(d){if(d.g==1)return ta(d,a.rendered,2);b=d.j;b instanceof HTMLElement&&a.h&&(a.visible?(c=getComputedStyle(b),a.h.style.marginBottom=""+c.height):a.h.style.marginBottom!=="0px"&&(a.h.style.marginBottom="0px"));d.g=0})}
l.destroy=function(){var a=this,b,c,d,e,f;return Ca(function(g){if(g.g==1)return ta(g,a.rendered,2);b=g.j;((c=b)==null?0:c.parentNode)&&b.parentNode.removeChild(b);d=r(a.i);for(e=d.next();!e.done;e=d.next())f=e.value,f.removeEventListener("click",a.G),f.setAttribute("aria-hidden","true"),f instanceof HTMLButtonElement||f.removeEventListener("keydown",a.B),Gj(f.parentElement)&&f.parentElement.setAttribute("aria-hidden","true");a.h&&(a.h.style.marginBottom="0px",window.removeEventListener("resize",
a.C));g.g=0})};
fa.Object.defineProperties(qj.prototype,{visible:{configurable:!0,enumerable:!0,get:function(){return this.I}},status:{configurable:!0,enumerable:!0,get:function(){return this.j},set:function(a){a!==this.j&&(this.j=a,wj(this,"statuschange",{status:a}))}},D:{configurable:!0,enumerable:!0,get:function(){return this.status==="ACCEPTED"}},u:{configurable:!0,enumerable:!0,get:function(){return this.g||this.options.analyticsStorage||this.D}},o:{configurable:!0,enumerable:!0,get:function(){return!this.g&&this.D}},
F:{configurable:!0,enumerable:!0,get:function(){return this.options.category==="2A"&&!this.g}}});fa.Object.defineProperties(qj,{g:{configurable:!0,enumerable:!0,get:function(){return{analyticsStorage:!1,category:"2A",language:document.documentElement.lang,siteId:location.host,autoMarginClass:"glue-footer"}}}});function Aj(a){var b=r(a.split(/[-_]/));a=b.next().value;b=b.next().value;b=a+"-"+(b||a).toUpperCase();return hj.includes(b)?b:hj.includes(a)?a:""}
function Ej(){return Ca(function(a){return document.readyState!=="loading"?a.return():a.return(new Promise(function(b){document.addEventListener("DOMContentLoaded",function(){b()})}))})}function Gj(a){return!!a&&(a.classList.contains("glue-footer__global-links-list-item")||a.classList.contains("h-c-footer__global-links-list-item"))}function rj(a,b){var c=a.category;var d=a.siteId;return function(e){return e.category===c&&e.siteId===d&&e.eea===b}}function tj(){return arguments}
function uj(a,b,c){var d={};return tj("consent",a,(d.ad_storage=(c===void 0?0:c)?"granted":"denied",d.analytics_storage=(b===void 0?0:b)?"granted":"denied",d))}function yj(a,b,c,d){var e={};return Object.assign({},(e.event="user_consent",e),vj(a,b===void 0?!1:b,c===void 0?!1:c,d===void 0?!1:d))}function vj(a,b,c,d){var e={};return e.cookieConsent=a==="UNKNOWN"?"none":""+(a==="ACCEPTED"),e.isEEA=""+(b===void 0?!1:b),e.adsStorage=""+(d===void 0?!1:d),e.analyticsStorage=""+(c===void 0?!1:c),e};function Ij(a){return Jj.dataset["glueCookieNotificationBar"+(a.charAt(0).toUpperCase()+a.slice(1))]}function Kj(){qj.apply(this,arguments)}q(Kj,qj);Kj.category=lj;Fa("module$exports$google3$marketing$glue$lib$cookienotificationbar$cdn.CdnCookieNotificationBar.category",Kj.category);Kj.status=mj;Fa("module$exports$google3$marketing$glue$lib$cookienotificationbar$cdn.CdnCookieNotificationBar.status",Kj.status);var Lj;var Jj=void 0,Mj;
if(Jj===void 0)if(document.currentScript)Mj=document.currentScript;else{var Nj=document.getElementsByTagName("script");Mj=Nj[Nj.length-1]}else Mj=Jj;Jj=Mj;
if(Ij("autoload")!=="false"){var Oj=Ij("analyticsStorage"),Pj=Ij("category"),Qj=Ij("content"),Rj=Ij("language"),Sj=Ij("siteId"),Tj=Ij("autoMarginClass"),Uj={};Oj&&(Uj.analyticsStorage=Oj==="true");Object.values(lj).includes(Pj)&&(Uj.category=Pj);Rj&&(Uj.language=Rj);Sj&&(Uj.siteId=Sj);Tj&&(Uj.autoMarginClass=Tj);Qj&&(Uj.content=JSON.parse(Qj));Lj=new Kj(document.body,Uj)}else Lj=void 0;Kj.instance=Lj;window.glue=Object.assign({},window.glue,{CookieNotificationBar:Kj});}).call(this);
