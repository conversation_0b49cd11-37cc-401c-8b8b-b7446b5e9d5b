gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var aa,ca,fa,ma,na,qa,Ba,Ca;aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
fa=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.ha=fa(this);ma=function(a,b){if(b)a:{var c=_.ha;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ca(c,a,{configurable:!0,writable:!0,value:b})}};
ma("Symbol",function(a){if(a)return a;var b=function(f,h){this.C2=f;ca(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.C2};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
ma("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.ha[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return na(aa(this))}})}return a});na=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.pa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")qa=Object.setPrototypeOf;else{var sa;a:{var ua={a:!0},xa={};try{xa.__proto__=ua;sa=xa.a;break a}catch(a){}sa=!1}qa=sa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.ya=qa;
_.za=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error("b`"+String(a));};Ba=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ca=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Ba(d,e)&&(a[e]=d[e])}return a};ma("Object.assign",function(a){return a||Ca});
ma("globalThis",function(a){return a||_.ha});ma("Reflect.setPrototypeOf",function(a){return a?a:_.ya?function(b,c){try{return(0,_.ya)(b,c),!0}catch(d){return!1}}:null});
ma("Promise",function(a){function b(){this.xf=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.BP=function(h){if(this.xf==null){this.xf=[];var k=this;this.CP(function(){k.r9()})}this.xf.push(h)};var d=_.ha.setTimeout;b.prototype.CP=function(h){d(h,0)};b.prototype.r9=function(){for(;this.xf&&this.xf.length;){var h=this.xf;this.xf=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Zp(m)}}}this.xf=null};b.prototype.Zp=function(h){this.CP(function(){throw h;
})};var e=function(h){this.Ca=0;this.nf=void 0;this.Lr=[];this.fW=!1;var k=this.GF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.GF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.ufa),reject:h(this.tK)}};e.prototype.ufa=function(h){if(h===this)this.tK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.Zga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.tfa(h):this.eT(h)}};e.prototype.tfa=function(h){var k=void 0;try{k=h.then}catch(l){this.tK(l);return}typeof k=="function"?this.aha(k,h):this.eT(h)};e.prototype.tK=function(h){this.g0(2,h)};e.prototype.eT=function(h){this.g0(1,h)};e.prototype.g0=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.nf=k;this.Ca===2&&this.Jfa();this.s9()};e.prototype.Jfa=function(){var h=this;d(function(){if(h.Hda()){var k=_.ha.console;typeof k!=="undefined"&&k.error(h.nf)}},
1)};e.prototype.Hda=function(){if(this.fW)return!1;var h=_.ha.CustomEvent,k=_.ha.Event,l=_.ha.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.ha.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.nf;return l(h)};e.prototype.s9=function(){if(this.Lr!=null){for(var h=0;h<this.Lr.length;++h)f.BP(this.Lr[h]);
this.Lr=null}};var f=new b;e.prototype.Zga=function(h){var k=this.GF();h.Dy(k.resolve,k.reject)};e.prototype.aha=function(h,k){var l=this.GF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,t){return typeof q=="function"?function(v){try{m(q(v))}catch(u){n(u)}}:t}var m,n,p=new e(function(q,t){m=q;n=t});this.Dy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.Dy=function(h,k){function l(){switch(m.Ca){case 1:h(m.nf);
break;case 2:k(m.nf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Lr==null?f.BP(l):this.Lr.push(l);this.fW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.za(h),n=m.next();!n.done;n=m.next())c(n.value).Dy(k,l)})};e.all=function(h){var k=_.za(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(v){return function(u){q[v]=u;t--;t==0&&m(q)}}var q=[],t=0;do q.push(void 0),t++,c(l.value).Dy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Da=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
ma("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Da(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});ma("Object.setPrototypeOf",function(a){return a||_.ya});ma("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
ma("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Ba(l,f)){var m=new b;ca(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.za(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Ba(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Ba(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Ba(l,f)&&Ba(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Ba(l,f)&&Ba(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
ma("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.za([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.za(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=l:(m.entry={next:this[1],Jk:this[1].Jk,head:this[1],key:k,value:l},m.list.push(m.entry),this[1].Jk.next=m.entry,this[1].Jk=m.entry,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.entry&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.entry.Jk.next=
k.entry.next,k.entry.next.Jk=k.entry.Jk,k.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Jk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).entry};c.prototype.get=function(k){return(k=d(this,k).entry)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=
function(k,l){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Ba(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,entry:p}}return{id:m,list:n,index:-1,entry:void 0}},e=function(k,l){var m=k[1];return na(function(){if(m){for(;m.head!=
k[1];)m=m.Jk;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Jk=k.next=k.head=k},h=0;return c});
ma("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.za([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.za(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ea=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};ma("Array.prototype.entries",function(a){return a?a:function(){return Ea(this,function(b,c){return[b,c]})}});
ma("Array.prototype.keys",function(a){return a?a:function(){return Ea(this,function(b){return b})}});ma("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Da(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
ma("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});ma("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Ba(b,d)&&c.push([d,b[d]]);return c}});
ma("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Da(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});ma("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ka=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{i:e,YD:f}}return{i:-1,YD:void 0}};ma("Array.prototype.find",function(a){return a?a:function(b,c){return Ka(this,b,c).YD}});ma("Array.prototype.values",function(a){return a?a:function(){return Ea(this,function(b,c){return c})}});
ma("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});ma("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
ma("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});ma("String.prototype.includes",function(a){return a?a:function(b,c){return Da(this,b,"includes").indexOf(b,c||0)!==-1}});
ma("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});ma("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Ba(b,d)&&c.push(b[d]);return c}});
ma("Set.prototype.intersection",function(a){return a?a:function(b){if(!(this instanceof Set))throw new TypeError("Method must be called on an instance of Set.");if(typeof b!=="object"||b===null||typeof b.size!=="number"||b.size<0||typeof b.keys!=="function"||typeof b.has!=="function")throw new TypeError("Argument must be set-like");var c=new Set;if(this.size<=b.size)b={D0:this.keys(),vW:b};else{b=b.keys();if(typeof b!=="object"||b===null||typeof b.next!=="function")throw new TypeError("Invalid iterator.");
b={D0:b,vW:this}}var d=b;b=d.D0;d=d.vW;for(var e=b.next();!e.done;)d.has(e.value)&&c.add(e.value),e=b.next();return c}});ma("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});ma("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});ma("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});
ma("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});ma("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});ma("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});
ma("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});ma("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});ma("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
ma("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var La=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
ma("Array.prototype.at",function(a){return a?a:La});var Ra=function(a){return a?a:La};ma("Int8Array.prototype.at",Ra);ma("Uint8Array.prototype.at",Ra);ma("Uint8ClampedArray.prototype.at",Ra);ma("Int16Array.prototype.at",Ra);ma("Uint16Array.prototype.at",Ra);ma("Int32Array.prototype.at",Ra);ma("Uint32Array.prototype.at",Ra);ma("Float32Array.prototype.at",Ra);ma("Float64Array.prototype.at",Ra);ma("String.prototype.at",function(a){return a?a:La});
ma("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ka(this,b,c).i}});_.Sa={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Ua=_.Ua||{};_.Xa=this||self;_.Ya=_.Xa._F_toggles||[];_.$a="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.r=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.cb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.eb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var kb;_.ib=function(a){return function(){return _.gb[a].apply(this,arguments)}};_.jb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.jb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.yZ=!0};kb=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.jb.call(this,c+a[d])};_.gb=[];_.cb(_.jb,Error);_.jb.prototype.name="CustomError";_.cb(kb,_.jb);kb.prototype.name="AssertionError";
var wb,yb,zb;_.lb=function(a,b){return _.gb[a]=b};_.nb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.qb=function(a,b){return(0,_.ob)(a,b)>=0};_.sb=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.ub=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.pa)(b.prototype);a.prototype.constructor=a;if(_.ya)(0,_.ya)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.vb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};wb=function(a){var b=_.vb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.Ab=function(a,b,c){_.Ab=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.Ab.apply(null,arguments)};_.ob=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Bb=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Cb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Db=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Fb=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Hb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Jb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Nb=!!(_.Ya[0]>>15&1),Ob=!!(_.Ya[0]>>16&1),Pb=!!(_.Ya[0]&32),Qb=!!(_.Ya[0]>>17&1),Rb=!!(_.Ya[0]&16);_.Sb=Nb?Ob:wb(610401301);_.Tb=Nb?Pb:wb(**********);_.Vb=Nb?Qb:wb(651175828);_.Wb=Nb?Rb:wb(555019702);_.Xb=function(a){_.Xb[" "](a);return a};_.Xb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,sc,Fc,Nc,cd,md;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.UY;throw Error("j");};_.ic=function(a){return new _.hc(a)};_.kc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.jc(a)};_.lc=function(a){if(a instanceof _.jc)return a.VY;throw Error("j");};_.oc=function(a){return a instanceof _.mc};_.qc=function(a){if(_.oc(a))return a.XY;throw Error("j");};sc=function(a){return new _.rc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.uc=function(a){if(tc.test(a))return a};
_.wc=function(a){return a instanceof _.mc?_.qc(a):_.uc(a)};_.xc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.Gsa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.Iea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.Hsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.zc=function(a){var b=_.yc.apply(1,arguments);if(b.length===0)return _.kc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.kc(c)};_.Bc=function(a,b){return a.lastIndexOf(b,0)==0};_.Cc=function(a){return/^[\s\xa0]*$/.test(a)};_.Dc=function(a,b){return a.indexOf(b)!=-1};
_.Gc=function(a,b){var c=0;a=(0,_.Ec)(String(a)).split(".");b=(0,_.Ec)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Fc(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Fc(f[2].length==0,h[2].length==0)||Fc(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Fc=function(a,b){return a<b?-1:a>b?1:0};_.Hc=function(a,b){b=_.wc(b);b!==void 0&&(a.href=b)};_.Ic=function(a,b,c,d){b=_.wc(b);return b!==void 0?a.open(b,c,d):null};_.Jc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Kc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Lc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Nc=function(a){if(!_.Sb||!_.Mc)return!1;for(var b=0;b<_.Mc.brands.length;b++){var c=_.Mc.brands[b].brand;if(c&&_.Dc(c,a))return!0}return!1};_.Oc=function(a){return _.Dc(_.Lc(),a)};_.Pc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Qc=function(){return _.Sb?!!_.Mc&&_.Mc.brands.length>0:!1};_.Rc=function(){return _.Qc()?!1:_.Oc("Opera")};
_.Tc=function(){return _.Qc()?!1:_.Oc("Trident")||_.Oc("MSIE")};_.Uc=function(){return _.Qc()?!1:_.Oc("Edge")};_.Vc=function(){return _.Qc()?Nc("Microsoft Edge"):_.Oc("Edg/")};_.Wc=function(){return _.Qc()?Nc("Opera"):_.Oc("OPR")};_.Xc=function(){return _.Oc("Firefox")||_.Oc("FxiOS")};_.Yc=function(){return _.Qc()?Nc("Chromium"):(_.Oc("Chrome")||_.Oc("CriOS"))&&!_.Uc()||_.Oc("Silk")};
_.Zc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.$c=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
cd=function(){return _.Sb?!!_.Mc&&!!_.Mc.platform:!1};_.dd=function(){return cd()?_.Mc.platform==="Android":_.Oc("Android")};_.ed=function(){return _.Oc("iPhone")&&!_.Oc("iPod")&&!_.Oc("iPad")};_.fd=function(){return _.ed()||_.Oc("iPad")||_.Oc("iPod")};_.gd=function(){return cd()?_.Mc.platform==="macOS":_.Oc("Macintosh")};_.hd=function(){return cd()?_.Mc.platform==="Windows":_.Oc("Windows")};_.id=function(){return cd()?_.Mc.platform==="Chrome OS":_.Oc("CrOS")};
_.kd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.ld=function(a){return _.kd(a,a)};_.yc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.nd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.od=function(a){var b=_.nd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.pd=function(){return Date.now()};var qd=globalThis.trustedTypes,$b=qd,bc;_.dc=function(a){this.UY=a};_.dc.prototype.toString=function(){return this.UY+""};_.rd=function(){return new _.dc(qd?qd.emptyHTML:"")}();_.hc=function(a){this.WY=a};_.hc.prototype.toString=function(){return this.WY};_.jc=function(a){this.VY=a};_.jc.prototype.toString=function(){return this.VY+""};_.mc=function(a){this.XY=a};_.mc.prototype.toString=function(){return this.XY};_.sd=new _.mc("about:invalid#zClosurez");var tc;_.rc=function(a){this.yj=a};_.td=[sc("data"),sc("http"),sc("https"),sc("mailto"),sc("ftp"),new _.rc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.ud=function(){return typeof URL==="function"}();tc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.vd=function(a,b){this.width=a;this.height=b};_.wd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.vd.prototype;_.g.clone=function(){return new _.vd(this.width,this.height)};_.g.area=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.area()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.Ec=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.xd=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.yd=Math.random()*2147483648|0;var zd;zd=_.Xa.navigator;_.Mc=zd?zd.userAgentData||null:null;var Pd,Qd,Vd;_.Ad=_.Rc();_.Bd=_.Tc();_.Cd=_.Oc("Edge");_.Dd=_.Cd||_.Bd;_.Ed=_.Oc("Gecko")&&!(_.Dc(_.Lc().toLowerCase(),"webkit")&&!_.Oc("Edge"))&&!(_.Oc("Trident")||_.Oc("MSIE"))&&!_.Oc("Edge");_.Fd=_.Dc(_.Lc().toLowerCase(),"webkit")&&!_.Oc("Edge");_.Gd=_.Fd&&_.Oc("Mobile");_.Hd=_.gd();_.Id=_.hd();_.Jd=(cd()?_.Mc.platform==="Linux":_.Oc("Linux"))||_.id();_.Kd=_.dd();_.Ld=_.ed();_.Md=_.Oc("iPad");_.Nd=_.Oc("iPod");_.Od=_.fd();Pd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Rd="",Sd=function(){var a=_.Lc();if(_.Ed)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/Edge\/([\d\.]+)/.exec(a);if(_.Bd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Fd)return/WebKit\/(\S+)/.exec(a);if(_.Ad)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Sd&&(Rd=Sd?Sd[1]:"");if(_.Bd){var Td=Pd();if(Td!=null&&Td>parseFloat(Rd)){Qd=String(Td);break a}}Qd=Rd}_.Ud=Qd;if(_.Xa.document&&_.Bd){var Wd=Pd();Vd=Wd?Wd:parseInt(_.Ud,10)||void 0}else Vd=void 0;_.Xd=Vd;var ce,je,ie;_.$d=function(a){return a?new _.Yd(_.Zd(a)):md||(md=new _.Yd)};_.ae=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.be=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.de=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:ce.hasOwnProperty(d)?a.setAttribute(ce[d],c):_.Bc(d,"aria-")||_.Bc(d,"data-")?a.setAttribute(d,c):a[d]=c})};ce={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.fe=function(a){return _.ee(a||window)};
_.ee=function(a){a=a.document;a=_.ge(a)?a.documentElement:a.body;return new _.vd(a.clientWidth,a.clientHeight)};_.he=function(a){return a?a.defaultView:window};_.ke=function(a,b){var c=b[1],d=ie(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.de(d,c));b.length>2&&je(a,d,b,2);return d};
je=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.od(f)||_.ub(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.ub(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Cb(h?_.Yb(f):f,e)}}};_.ne=function(a){return ie(document,a)};
ie=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.ge=function(a){return a.compatMode=="CSS1Compat"};_.oe=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.pe=function(a,b){je(_.Zd(a),a,arguments,1)};_.qe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.re=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.se=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.te=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.ue=function(a){return _.ub(a)&&a.nodeType==1};
_.ve=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Zd=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.we=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.qe(a),a.appendChild(_.Zd(a).createTextNode(String(b)))};_.Yd=function(a){this.zc=a||_.Xa.document||document};_.g=_.Yd.prototype;_.g.Ha=_.$d;_.g.xL=_.ib(0);_.g.ub=function(){return this.zc};_.g.O=_.ib(1);_.g.getElementsByTagName=function(a,b){return(b||this.zc).getElementsByTagName(String(a))};
_.g.wH=_.ib(2);_.g.wa=function(a,b,c){return _.ke(this.zc,arguments)};_.g.createElement=function(a){return ie(this.zc,a)};_.g.createTextNode=function(a){return this.zc.createTextNode(String(a))};_.g.getWindow=function(){return this.zc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.pe;_.g.canHaveChildren=_.oe;_.g.ne=_.qe;_.g.CV=_.re;_.g.removeNode=_.se;_.g.GG=_.te;_.g.isElement=_.ue;_.g.contains=_.ve;_.g.ZG=_.Zd;_.g.wj=_.ib(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.xe=function(a){return a===null?"null":a===void 0?"undefined":a};_.ye=window;_.Be=document;_.Ce=_.ye.location;_.De=/\[native code\]/;_.Ee=function(a,b,c){return a[b]=a[b]||c};_.Fe=function(){var a;if((a=Object.create)&&_.De.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.Ge=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.He=function(a,b){a=a||{};for(var c in a)_.Ge(a,c)&&(b[c]=a[c])};_.Ie=_.Ee(_.ye,"gapi",{});_.Je=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.Ke=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Le=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Me=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Oe=function(a,b,c){_.Ne(a,b,c,"add","at")};_.Ne=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Pe={};_.Pe=_.Ee(_.ye,"___jsl",_.Fe());_.Ee(_.Pe,"I",0);_.Ee(_.Pe,"hel",10);var Qe,Re,Se,Te,We,Ue,Ve,Xe,Ye;Qe=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Re=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Se=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Te=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Se(a[d])&&!Se(b[d])?Te(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Se(b[d])?[]:{},Te(a[d],b[d])):a[d]=b[d])};
We=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Qe("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Ue())if(e=Ve(c),d.push(25),typeof e===
"object")return e;return{}}};Ue=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Ve=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Xe=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Te(c,b);a.push(c)};
Ye=function(a){Re(!0);var b=window.___gcfg,c=Qe("cu"),d=window.___gu;b&&b!==d&&(Xe(c,b),window.___gu=b);b=Qe("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Qe("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=We(f,h))&&b.push(f));a&&Xe(c,a);d=Qe("cd");a=0;for(b=d.length;a<b;++a)Te(Re(),d[a],!0);d=Qe("ci");a=0;for(b=d.length;a<b;++a)Te(Re(),d[a],!0);a=0;for(b=c.length;a<b;++a)Te(Re(),c[a],!0)};_.Ze=function(a,b){var c=Re();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.$e=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;Ye(c)};var af=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Ee(_.Pe,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};af&&af();Ye();_.r("gapi.config.get",_.Ze);_.r("gapi.config.update",_.$e);
_.bf=function(a){a=_.xe(a);return _.ec(a)};
_.Eg=(window.gapi||{}).load;
_.Vn=_.Ee(_.Pe,"rw",_.Fe());
var Wn=function(a,b){(a=_.Vn[a])&&a.state<b&&(a.state=b)};var Xn=function(a){a=(a=_.Vn[a])?a.oid:void 0;if(a){var b=_.Be.getElementById(a);b&&b.parentNode.removeChild(b);delete _.Vn[a];Xn(a)}};_.Yn=function(a){a=a.container;typeof a==="string"&&(a=document.getElementById(a));return a};_.Zn=function(a){var b=a.clientWidth;return"position:absolute;top:-10000px;width:"+(b?b+"px":a.style.width||"300px")+";margin:0px;border-style:none;"};
_.$n=function(a,b){var c={},d=a.vc(),e=b&&b.width,f=b&&b.height,h=b&&b.verticalAlign;h&&(c.verticalAlign=h);e||(e=d.width||a.width);f||(f=d.height||a.height);d.width=c.width=e;d.height=c.height=f;d=a.getIframeEl();e=a.getId();Wn(e,2);a:{e=a.getSiteEl();c=c||{};var k;if(_.Pe.oa&&(k=d.id)){f=(f=_.Vn[k])?f.state:void 0;if(f===1||f===4)break a;Xn(k)}(f=e.nextSibling)&&f.dataset&&f.dataset.gapistub&&(e.parentNode.removeChild(f),e.style.cssText="");f=c.width;h=c.height;var l=e.style;l.textIndent="0";l.margin=
"0";l.padding="0";l.background="transparent";l.borderStyle="none";l.cssFloat="none";l.styleFloat="none";l.lineHeight="normal";l.fontSize="1px";l.verticalAlign="baseline";e=e.style;e.display="inline-block";d=d.style;d.position="static";d.left="0";d.top="0";d.visibility="visible";f&&(e.width=d.width=f+"px");h&&(e.height=d.height=h+"px");c.verticalAlign&&(e.verticalAlign=c.verticalAlign);k&&Wn(k,3)}(k=b?b.title:null)&&a.getIframeEl().setAttribute("title",k);(b=b?b.ariaLabel:null)&&a.getIframeEl().setAttribute("aria-label",
b)};_.ao=function(a){var b=a.getSiteEl();b&&b.removeChild(a.getIframeEl())};_.bo=function(a){a.where=_.Yn(a);var b=a.messageHandlers=a.messageHandlers||{},c=function(e){_.$n(this,e)};b._ready=c;b._renderstart=c;var d=a.onClose;a.onClose=function(e){d&&d.call(this,e);_.ao(this)};a.onCreate=function(e){e=e.getIframeEl();e.style.cssText=_.Zn(e)}};
_.gf=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.$e(a());return{register:function(b,c,d){d&&d(_.Ze())},get:function(b){return _.Ze(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.$e(b)},init:function(){}}}();_.r("gadgets.config.register",_.gf.register);_.r("gadgets.config.get",_.gf.get);_.r("gadgets.config.init",_.gf.init);_.r("gadgets.config.update",_.gf.update);
var hf,jf,kf,lf,mf,of,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Cf,Df,Ef,Ff,Gf,Hf,If,Jf,Lf,Mf,Nf,Of,Pf,Sf,Tf;kf=void 0;lf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};mf=function(a){return Object.prototype.toString.call(a)};of=mf(0);qf=mf(new Date(0));rf=mf(!0);sf=mf("");tf=mf({});uf=mf([]);
vf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=mf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==uf||a.constructor!==Array&&a.constructor!==Object)&&(e!==tf||a.constructor!==Array&&a.constructor!==Object)&&e!==sf&&e!==of&&e!==rf&&e!==qf))return vf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===of)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===rf)b[b.length]=String(!!Number(a));else{if(e===qf)return vf(a.toISOString.call(a),c);if(e===uf&&mf(a.length)===of){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=vf(a[f],c)||"null";b[b.length]="]"}else if(e==sf&&mf(a.length)===of){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=vf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=vf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};wf=/[\0-\x07\x0b\x0e-\x1f]/;
xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;yf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;zf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;Af=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;Bf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Cf=/[ \t\n\r]+/g;Df=/[^"]:/;Ef=/""/g;Ff=/true|false|null/g;Gf=/00/;Hf=/[\{]([^0\}]|0[^:])/;If=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Jf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(wf.test(a)||xf.test(a)||yf.test(a)||zf.test(a))return!1;var b=a.replace(Af,'""');b=b.replace(Bf,"0");b=b.replace(Cf,"");if(Df.test(b))return!1;b=b.replace(Ef,"0");b=b.replace(Ff,"0");if(Gf.test(b)||Hf.test(b)||If.test(b)||Jf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=kf?[lf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((hf===void 0||kf===void 0||jf!==a)&&jf!==-1){hf=kf=!1;jf=-1;try{try{kf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&lf("true")===!0&&lf('[{"a":3}]')[0].a===3}catch(b){}hf=kf&&!lf("[00]")&&!lf('"\u0007"')&&!lf('"\\0"')&&!lf('"\\v"')}finally{jf=a}}};_.Qf=function(a){if(jf===-1)return!1;Pf();return(hf?lf:Of)(a)};
_.Rf=function(a){if(jf!==-1)return Pf(),kf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):vf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
_.r("gadgets.json.stringify",_.Rf);_.r("gadgets.json.parse",_.Qf);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.df=function(e){a(2,e)};_.ef=function(e){a(3,e)};_.ff=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.cf=_.cf||{};
_.cf=_.cf||{};(function(){var a=[];_.cf.Lsa=function(b){a.push(b)};_.cf.Zsa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
_.cf=_.cf||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.cf.Pg=a;a()})();_.r("gadgets.util.getUrlParameters",_.cf.Pg);
var Uf=function(){this.Bg=window.console};Uf.prototype.log=function(a){this.Bg&&this.Bg.log&&this.Bg.log(a)};Uf.prototype.error=function(a){this.Bg&&(this.Bg.error?this.Bg.error(a):this.Bg.log&&this.Bg.log(a))};Uf.prototype.warn=function(a){this.Bg&&(this.Bg.warn?this.Bg.warn(a):this.Bg.log&&this.Bg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Wf=function(){var a=_.Be.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Xf=function(a){if(_.Wf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.ye.addEventListener?(_.ye.addEventListener("load",c,!1),_.ye.addEventListener("DOMContentLoaded",c,!1)):_.ye.attachEvent&&(_.ye.attachEvent("onreadystatechange",function(){_.Wf()&&c.apply(this,arguments)}),_.ye.attachEvent("onload",c))}};
_.Yf=function(a,b){var c=_.Ee(_.Pe,"watt",_.Fe());_.Ee(c,a,b)};_.Je(_.ye.location.href,"rpctoken")&&_.Oe(_.Be,"unload",function(){});var Zf=Zf||{};Zf.PZ=null;Zf.xX=null;Zf.LA=null;Zf.frameElement=null;Zf=Zf||{};
Zf.WN||(Zf.WN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Qf(f.data);if(h&&h.f){_.ff();var k=_.$f.ho(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.ef("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{uT:function(){return"wpm"},Hca:function(){return!0},init:function(f,h){_.gf.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Hb:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.$f.ho(f),m=_.$f.PO(f);l?window.setTimeout(function(){var n=_.Rf(k);_.ff();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.ef("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.$f!="undefined"&&_.$f||(_.$f=window.gadgets.rpc,_.$f.config=_.$f.config,_.$f.register=_.$f.register,_.$f.unregister=_.$f.unregister,_.$f.qZ=_.$f.registerDefault,_.$f.M1=_.$f.unregisterDefault,_.$f.YS=_.$f.forceParentVerifiable,_.$f.call=_.$f.call,_.$f.Ku=_.$f.getRelayUrl,_.$f.Pj=_.$f.setRelayUrl,_.$f.UC=_.$f.setAuthToken,_.$f.Mw=_.$f.setupReceiver,_.$f.Sn=_.$f.getAuthToken,_.$f.yK=_.$f.removeReceiver,_.$f.TT=_.$f.getRelayChannel,_.$f.lZ=_.$f.receive,
_.$f.mZ=_.$f.receiveSameDomain,_.$f.getOrigin=_.$f.getOrigin,_.$f.ho=_.$f.getTargetOrigin,_.$f.PO=_.$f._getTargetWin,_.$f.j7=_.$f._parseSiblingId);else{_.$f=function(){function a(J,la){if(!S[J]){var Na=fb;la||(Na=Ta);S[J]=Na;la=L[J]||[];for(var ra=0;ra<la.length;++ra){var V=la[ra];V.t=D[J];Na.call(J,V.f,V)}L[J]=[]}}function b(){function J(){Mb=!0}Kb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",J,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
J),Kb=!0)}function c(J,la,Na,ra,V){D[la]&&D[la]===Na||(_.ef("Invalid gadgets.rpc token. "+D[la]+" vs "+Na),rb(la,2));V.onunload=function(){R[la]&&!Mb&&(rb(la,1),_.$f.yK(la))};b();ra=_.Qf(decodeURIComponent(ra))}function d(J,la){if(J&&typeof J.s==="string"&&typeof J.f==="string"&&J.a instanceof Array)if(D[J.f]&&D[J.f]!==J.t&&(_.ef("Invalid gadgets.rpc token. "+D[J.f]+" vs "+J.t),rb(J.f,2)),J.s==="__ack")window.setTimeout(function(){a(J.f,!0)},0);else{J.c&&(J.callback=function(Ja){_.$f.call(J.f,(J.g?
"legacy__":"")+"__cb",null,J.c,Ja)});if(la){var Na=e(la);J.origin=la;var ra=J.r;try{var V=e(ra)}catch(Ja){}ra&&V==Na||(ra=la);J.referer=ra}la=(x[J.s]||x[""]).apply(J,J.a);J.c&&typeof la!=="undefined"&&_.$f.call(J.f,"__cb",null,J.c,la)}}function e(J){if(!J)return"";J=J.split("#")[0].split("?")[0];J=J.toLowerCase();J.indexOf("//")==0&&(J=window.location.protocol+J);J.indexOf("://")==-1&&(J=window.location.protocol+"//"+J);var la=J.substring(J.indexOf("://")+3),Na=la.indexOf("/");Na!=-1&&(la=la.substring(0,
Na));J=J.substring(0,J.indexOf("://"));if(J!=="http"&&J!=="https"&&J!=="chrome-extension"&&J!=="file"&&J!=="android-app"&&J!=="chrome-search"&&J!=="chrome-untrusted"&&J!=="chrome"&&J!=="devtools")throw Error("l");Na="";var ra=la.indexOf(":");if(ra!=-1){var V=la.substring(ra+1);la=la.substring(0,ra);if(J==="http"&&V!=="80"||J==="https"&&V!=="443")Na=":"+V}return J+"://"+la+Na}function f(J){if(J.charAt(0)=="/"){var la=J.indexOf("|"),Na=la>0?J.substring(1,la):J.substring(1);J=la>0?J.substring(la+1):
null;return{id:Na,origin:J}}return null}function h(J){if(typeof J==="undefined"||J==="..")return window.parent;var la=f(J);if(la)return k(window.top.frames[la.id]);J=String(J);return(la=window.frames[J])?k(la):(la=document.getElementById(J))&&la.contentWindow?la.contentWindow:null}function k(J){return J?"postMessage"in J?J:J instanceof HTMLIFrameElement&&"contentWindow"in J&&J.contentWindow!=null&&"postMessage"in J.contentWindow?J.contentWindow:null:null}function l(J,la){if(R[J]!==!0){typeof R[J]===
"undefined"&&(R[J]=0);var Na=h(J);J!==".."&&Na==null||fb.Hb(J,la)!==!0?R[J]!==!0&&R[J]++<10?window.setTimeout(function(){l(J,la)},500):(S[J]=Ta,R[J]=!0):R[J]=!0}}function m(J){(J=A[J])&&J.substring(0,1)==="/"&&(J=J.substring(1,2)==="/"?document.location.protocol+J:document.location.protocol+"//"+document.location.host+J);return J}function n(J,la,Na){la&&!/http(s)?:\/\/.+/.test(la)&&(la.indexOf("//")==0?la=window.location.protocol+la:la.charAt(0)=="/"?la=window.location.protocol+"//"+window.location.host+
la:la.indexOf("://")==-1&&(la=window.location.protocol+"//"+la));A[J]=la;typeof Na!=="undefined"&&(C[J]=!!Na)}function p(J,la){la=la||"";D[J]=String(la);l(J,la)}function q(J){J=(J.passReferrer||"").split(":",2);O=J[0]||"none";ba=J[1]||"origin"}function t(J){String(J.useLegacyProtocol)==="true"&&(fb=Zf.LA||Ta,fb.init(d,a))}function v(J,la){function Na(ra){ra=ra&&ra.rpc||{};q(ra);var V=ra.parentRelayUrl||"";V=e(ea.parent||la)+V;n("..",V,String(ra.useLegacyProtocol)==="true");t(ra);p("..",J)}!ea.parent&&
la?Na({}):_.gf.register("rpc",null,Na)}function u(J,la,Na){if(J==="..")v(Na||ea.rpctoken||ea.ifpctok||"",la);else a:{var ra=null;if(J.charAt(0)!="/"){if(!_.cf)break a;ra=document.getElementById(J);if(!ra)throw Error("m`"+J);}ra=ra&&ra.src;la=la||e(ra);n(J,la);la=_.cf.Pg(ra);p(J,Na||la.rpctoken)}}var x={},A={},C={},D={},P=0,F={},R={},ea={},S={},L={},O=null,ba=null,oa=window.top!==window.self,Oa=window.name,rb=function(){},hb=window.console,Eb=hb&&hb.log&&function(J){hb.log(J)}||function(){},Ta=function(){function J(la){return function(){Eb(la+
": call ignored")}}return{uT:function(){return"noop"},Hca:function(){return!0},init:J("init"),Hb:J("setup"),call:J("call")}}();_.cf&&(ea=_.cf.Pg());var Mb=!1,Kb=!1,fb=function(){if(ea.rpctx=="rmr")return Zf.PZ;var J=typeof window.postMessage==="function"?Zf.WN:typeof window.postMessage==="object"?Zf.WN:window.ActiveXObject?Zf.xX?Zf.xX:Zf.LA:navigator.userAgent.indexOf("WebKit")>0?Zf.PZ:navigator.product==="Gecko"?Zf.frameElement:Zf.LA;J||(J=Ta);return J}();x[""]=function(){Eb("Unknown RPC service: "+
this.s)};x.__cb=function(J,la){var Na=F[J];Na&&(delete F[J],Na.call(this,la))};return{config:function(J){typeof J.d_==="function"&&(rb=J.d_)},register:function(J,la){if(J==="__cb"||J==="__ack")throw Error("n");if(J==="")throw Error("o");x[J]=la},unregister:function(J){if(J==="__cb"||J==="__ack")throw Error("p");if(J==="")throw Error("q");delete x[J]},qZ:function(J){x[""]=J},M1:function(){delete x[""]},YS:function(){},call:function(J,la,Na,ra){J=J||"..";var V="..";J===".."?V=Oa:J.charAt(0)=="/"&&(V=
e(window.location.href),V="/"+Oa+(V?"|"+V:""));++P;Na&&(F[P]=Na);var Ja={s:la,f:V,c:Na?P:0,a:Array.prototype.slice.call(arguments,3),t:D[J],l:!!C[J]};a:if(O==="bidir"||O==="c2p"&&J===".."||O==="p2c"&&J!==".."){var ta=window.location.href;var va="?";if(ba==="query")va="#";else if(ba==="hash")break a;va=ta.lastIndexOf(va);va=va===-1?ta.length:va;ta=ta.substring(0,va)}else ta=null;ta&&(Ja.r=ta);if(J===".."||f(J)!=null||document.getElementById(J))(ta=S[J])||f(J)===null||(ta=fb),la.indexOf("legacy__")===
0&&(ta=fb,Ja.s=la.substring(8),Ja.c=Ja.c?Ja.c:P),Ja.g=!0,Ja.r=V,ta?(C[J]&&(ta=Zf.LA),ta.call(J,V,Ja)===!1&&(S[J]=Ta,fb.call(J,V,Ja))):L[J]?L[J].push(Ja):L[J]=[Ja]},Ku:m,Pj:n,UC:p,Mw:u,Sn:function(J){return D[J]},yK:function(J){delete A[J];delete C[J];delete D[J];delete R[J];delete S[J]},TT:function(){return fb.uT()},lZ:function(J,la){J.length>4?fb.iqa(J,d):c.apply(null,J.concat(la))},mZ:function(J){J.a=Array.prototype.slice.call(J.a);window.setTimeout(function(){d(J)},0)},getOrigin:e,ho:function(J){var la=
null,Na=m(J);Na?la=Na:(Na=f(J))?la=Na.origin:J==".."?la=ea.parent:(J=document.getElementById(J))&&J.tagName.toLowerCase()==="iframe"&&(la=J.src);return e(la)},init:function(){fb.init(d,a)===!1&&(fb=Ta);oa?u(".."):_.gf.register("rpc",null,function(J){J=J.rpc||{};q(J);t(J)})},PO:h,j7:f,pia:"__ack",nna:Oa||"..",xna:0,wna:1,vna:2}}();_.$f.init()};_.$f.config({d_:function(a){throw Error("r`"+a);}});_.r("gadgets.rpc.config",_.$f.config);_.r("gadgets.rpc.register",_.$f.register);_.r("gadgets.rpc.unregister",_.$f.unregister);_.r("gadgets.rpc.registerDefault",_.$f.qZ);_.r("gadgets.rpc.unregisterDefault",_.$f.M1);_.r("gadgets.rpc.forceParentVerifiable",_.$f.YS);_.r("gadgets.rpc.call",_.$f.call);_.r("gadgets.rpc.getRelayUrl",_.$f.Ku);_.r("gadgets.rpc.setRelayUrl",_.$f.Pj);_.r("gadgets.rpc.setAuthToken",_.$f.UC);_.r("gadgets.rpc.setupReceiver",_.$f.Mw);_.r("gadgets.rpc.getAuthToken",_.$f.Sn);
_.r("gadgets.rpc.removeReceiver",_.$f.yK);_.r("gadgets.rpc.getRelayChannel",_.$f.TT);_.r("gadgets.rpc.receive",_.$f.lZ);_.r("gadgets.rpc.receiveSameDomain",_.$f.mZ);_.r("gadgets.rpc.getOrigin",_.$f.getOrigin);_.r("gadgets.rpc.getTargetOrigin",_.$f.ho);
_.Hg=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("s`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("t`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};
var Jg=function(){this.blockSize=-1},Kg=function(){this.blockSize=-1;this.blockSize=64;this.Qc=[];this.eF=[];this.b7=[];this.UB=[];this.UB[0]=128;for(var a=1;a<this.blockSize;++a)this.UB[a]=0;this.KD=this.kr=0;this.reset()};_.cb(Kg,Jg);Kg.prototype.reset=function(){this.Qc[0]=1732584193;this.Qc[1]=4023233417;this.Qc[2]=2562383102;this.Qc[3]=271733878;this.Qc[4]=3285377520;this.KD=this.kr=0};
var Lg=function(a,b,c){c||(c=0);var d=a.b7;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.Qc[0];c=a.Qc[1];e=a.Qc[2];for(var f=a.Qc[3],h=a.Qc[4],k,l,m=0;m<80;m++)m<40?m<20?(k=f^c&(e^f),l=1518500249):(k=c^e^f,l=1859775393):m<60?(k=c&e|f&(c|e),l=2400959708):(k=c^
e^f,l=3395469782),k=(b<<5|b>>>27)+k+h+l+d[m]&4294967295,h=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=k;a.Qc[0]=a.Qc[0]+b&4294967295;a.Qc[1]=a.Qc[1]+c&4294967295;a.Qc[2]=a.Qc[2]+e&4294967295;a.Qc[3]=a.Qc[3]+f&4294967295;a.Qc[4]=a.Qc[4]+h&4294967295};
Kg.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.eF,f=this.kr;d<b;){if(f==0)for(;d<=c;)Lg(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Lg(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Lg(this,e);f=0;break}}this.kr=f;this.KD+=b}};
Kg.prototype.digest=function(){var a=[],b=this.KD*8;this.kr<56?this.update(this.UB,56-this.kr):this.update(this.UB,this.blockSize-(this.kr-56));for(var c=this.blockSize-1;c>=56;c--)this.eF[c]=b&255,b/=256;Lg(this,this.eF);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Qc[c]>>d&255,++b;return a};_.Mg=function(){this.eN=new Kg};_.g=_.Mg.prototype;_.g.reset=function(){this.eN.reset()};_.g.O1=function(a){this.eN.update(a)};_.g.bR=function(){return this.eN.digest()};_.g.Bx=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.O1(b)};_.g.Ti=function(){for(var a=this.bR(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
var qh;_.ph=function(a){_.Xa.setTimeout(function(){throw a;},0)};qh=0;_.rh=function(a){return Object.prototype.hasOwnProperty.call(a,_.$a)&&a[_.$a]||(a[_.$a]=++qh)};
_.sh=function(){return _.Oc("Safari")&&!(_.Yc()||(_.Qc()?0:_.Oc("Coast"))||_.Rc()||_.Uc()||_.Vc()||_.Wc()||_.Xc()||_.Oc("Silk")||_.Oc("Android"))};_.th=function(){return _.Oc("Android")&&!(_.Yc()||_.Xc()||_.Rc()||_.Oc("Silk"))};_.uh=_.Xc();_.vh=_.ed()||_.Oc("iPod");_.wh=_.Oc("iPad");_.xh=_.th();_.yh=_.Yc();_.zh=_.sh()&&!_.fd();
_.$h=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.ai=function(a){var b=_.$h();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.bi=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.ci=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};var di;di=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.ei=function(a){var b=_.ai("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Je(a,"authuser")||null,b==null&&(b=(b=a.match(di))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
_.ri=function(){if(!_.Xa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Xa.addEventListener("test",c,b);_.Xa.removeEventListener("test",c,b)}catch(d){}return a}();
var si=function(){var a=_.Pe.ms||_.Pe.u;if(a)return(new URL(a)).origin};var ti=function(a){this.PS=a;this.count=this.count=0};ti.prototype.rb=function(a,b){a?this.count+=a:this.count++;this.PS&&(b===void 0||b)&&this.PS()};ti.prototype.get=function(){return this.count};ti.prototype.reset=function(){this.count=0};var vi,yi;vi=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Uy=new Map;this.XE=!1;var c=si();c&&(this.url=c+"/js/gen_204",c=_.ai("gen204logger")||{},this.pu=c.interval,this.QS=c.rate,this.XE=c.vqa,a&&this.url&&ui(this),document.addEventListener("visibilitychange",this.flush),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))};_.wi=function(){vi.TW||(vi.TW=new vi);return vi.TW};
yi=function(a){var b=_.Pe.dm||[];if(b&&b.length!==0){b=_.za(b);for(var c=b.next();!c.done;c=b.next())_.xi(a,c.value).rb(1,!1);delete _.Pe.dm;a.flush()}};_.xi=function(a,b){a.Uy.has(b)||a.Uy.set(b,new ti(a.XE?void 0:function(){a.flush()}));return a.Uy.get(b)};
vi.prototype.flush=function(){var a=this;if(this.url&&this.QS){yi(this);for(var b="",c=_.za(this.Uy),d=c.next();!d.done;d=c.next()){var e=_.za(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}if(b!==""&&Math.random()<this.QS){try{var h=AbortSignal.timeout(3E4)}catch(k){h=void 0}fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:h}).catch(function(){}).finally(function(){ui(a)})}}};
vi.prototype.setInterval=function(a){this.pu=a};var ui=function(a){a.pu&&a.XE&&setTimeout(function(){a.flush()},a.pu)};var Ai,zi,Gi,Hi,Bi,Ei,Ci,Ii,Di;_.Fi=function(){_.xi(_.wi(),50).rb();if(zi){var a=new _.ye.Uint32Array(1);Ai.getRandomValues(a);a=Number("0."+a[0])}else a=Bi,a+=parseInt(Ci.substr(0,20),16),Ci=Di(Ci),a/=Ei+1.2089258196146292E24;return a};Ai=_.ye.crypto;zi=!1;Gi=0;Hi=0;Bi=1;Ei=0;Ci="";Ii=function(a){a=a||_.ye.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Bi=Bi*b%Ei;Gi>0&&++Hi==Gi&&_.Ne(_.ye,"mousemove",Ii,"remove","de")};
Di=function(a){var b=new _.Mg;b.Bx(a);return b.Ti()};zi=!!Ai&&typeof Ai.getRandomValues=="function";zi||(Ei=(screen.width*screen.width+screen.height)*1E6,Ci=Di(_.Be.cookie+"|"+_.Be.location+"|"+(new Date).getTime()+"|"+Math.random()),Gi=_.ai("random/maxObserveMousemove")||0,Gi!=0&&_.Oe(_.ye,"mousemove",Ii));
_.Si=function(a){var b=window;a=(a||b.location.href).match(RegExp(".*(\\?|#|&)usegapi=([^&#]+)"))||[];return"1"===decodeURIComponent(a[a.length-1]||"")};
var Yi;_.Xi=function(a,b){b=(0,_.ob)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.Zi=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Yi.length;f++)c=Yi[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};Yi="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.$i=[];_.aj=[];_.bj=!1;
_.cj=function(a){_.$i[_.$i.length]=a;if(_.bj)for(var b=0;b<_.aj.length;b++)a((0,_.Ab)(_.aj[b].wrap,_.aj[b]))};
var Pj=function(a){this.T=a};_.g=Pj.prototype;_.g.value=function(){return this.T};_.g.Ne=function(a){this.T.width=a;return this};_.g.Rb=function(){return this.T.width};_.g.Td=function(a){this.T.height=a;return this};_.g.Mc=function(){return this.T.height};_.g.Fi=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.Qj=function(a){this.T=a||{}};_.g=_.Qj.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Fi=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Me=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.hn=function(a){this.T.rpctoken=a;return this};_.Rj=function(a,b){a.T.messageHandlers=b;return a};_.Sj=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.Qj.prototype;_.g.ds=_.ib(4);_.g.getContext=function(){return this.T.context};_.g.kd=function(){return this.T.openerIframe};_.g.Xn=function(){this.T.attributes=this.T.attributes||{};return new Pj(this.T.attributes)};_.g.Nz=_.ib(5);
var Xj;_.Tj=function(a){var b={},c;for(c in a)b[c]=a[c];return b};Xj=function(){for(var a;a=Uj.remove();){try{a.Qh.call(a.scope)}catch(b){_.ph(b)}Vj.put(a)}Wj=!1};_.Yj=function(a){if(!(a instanceof Array)){a=_.za(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};_.Zj=function(){};_.ak=function(a){a.prototype.$goog_Thenable=!0};_.bk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};
_.ck=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var dk=function(a,b){this.J8=a;this.rfa=b;this.GB=0;this.HA=null};dk.prototype.get=function(){if(this.GB>0){this.GB--;var a=this.HA;this.HA=a.next;a.next=null}else a=this.J8();return a};dk.prototype.put=function(a){this.rfa(a);this.GB<100&&(this.GB++,a.next=this.HA,this.HA=a)};_.ek=function(a){return a};_.cj(function(a){_.ek=a});var fk=function(){this.cE=this.Ts=null};fk.prototype.add=function(a,b){var c=Vj.get();c.set(a,b);this.cE?this.cE.next=c:this.Ts=c;this.cE=c};fk.prototype.remove=function(){var a=null;this.Ts&&(a=this.Ts,this.Ts=this.Ts.next,this.Ts||(this.cE=null),a.next=null);return a};var Vj=new dk(function(){return new gk},function(a){return a.reset()}),gk=function(){this.next=this.scope=this.Qh=null};gk.prototype.set=function(a,b){this.Qh=a;this.scope=b;this.next=null};
gk.prototype.reset=function(){this.next=this.scope=this.Qh=null};var hk,Wj,Uj,ik;Wj=!1;Uj=new fk;_.jk=function(a,b){hk||ik();Wj||(hk(),Wj=!0);Uj.add(a,b)};ik=function(){var a=Promise.resolve(void 0);hk=function(){a.then(Xj)}};var mk,nk,ok,Ck,Gk,Ek,Hk;_.lk=function(a,b){this.Ca=0;this.nf=void 0;this.fq=this.Dl=this.Fb=null;this.xA=this.iG=!1;if(a!=_.Zj)try{var c=this;a.call(b,function(d){kk(c,2,d)},function(d){kk(c,3,d)})}catch(d){kk(this,3,d)}};mk=function(){this.next=this.context=this.Kr=this.Rv=this.An=null;this.gy=!1};mk.prototype.reset=function(){this.context=this.Kr=this.Rv=this.An=null;this.gy=!1};nk=new dk(function(){return new mk},function(a){a.reset()});
ok=function(a,b,c){var d=nk.get();d.Rv=a;d.Kr=b;d.context=c;return d};_.pk=function(a){if(a instanceof _.lk)return a;var b=new _.lk(_.Zj);kk(b,2,a);return b};_.qk=function(a){return new _.lk(function(b,c){c(a)})};_.sk=function(a,b,c){rk(a,b,c,null)||_.jk(_.bb(b,a))};_.tk=function(a){return new _.lk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k,l=0;l<a.length;l++)k=a[l],_.sk(k,_.bb(f,l),h);else b(e)})};
_.vk=function(){var a,b,c=new _.lk(function(d,e){a=d;b=e});return new uk(c,a,b)};_.lk.prototype.then=function(a,b,c){return wk(this,(0,_.ck)(typeof a==="function"?a:null),(0,_.ck)(typeof b==="function"?b:null),c)};_.ak(_.lk);var yk=function(a,b,c,d){xk(a,ok(b||_.Zj,c||null,d))};_.lk.prototype.finally=function(a){var b=this;a=(0,_.ck)(a);return new Promise(function(c,d){yk(b,function(e){a();c(e)},function(e){a();d(e)})})};_.lk.prototype.FD=function(a,b){return wk(this,null,(0,_.ck)(a),b)};
_.lk.prototype.catch=_.lk.prototype.FD;_.lk.prototype.cancel=function(a){if(this.Ca==0){var b=new _.zk(a);_.jk(function(){Ak(this,b)},this)}};
var Ak=function(a,b){if(a.Ca==0)if(a.Fb){var c=a.Fb;if(c.Dl){for(var d=0,e=null,f=null,h=c.Dl;h&&(h.gy||(d++,h.An==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ca==0&&d==1?Ak(c,b):(f?(d=f,d.next==c.fq&&(c.fq=d),d.next=d.next.next):Bk(c),Ck(c,e,3,b)))}a.Fb=null}else kk(a,3,b)},xk=function(a,b){a.Dl||a.Ca!=2&&a.Ca!=3||Dk(a);a.fq?a.fq.next=b:a.Dl=b;a.fq=b},wk=function(a,b,c,d){var e=ok(null,null,null);e.An=new _.lk(function(f,h){e.Rv=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Kr=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.zk?h(k):f(l)}catch(m){h(m)}}:h});e.An.Fb=a;xk(a,e);return e.An};_.lk.prototype.Kha=function(a){this.Ca=0;kk(this,2,a)};_.lk.prototype.Lha=function(a){this.Ca=0;kk(this,3,a)};
var kk=function(a,b,c){a.Ca==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ca=1,rk(c,a.Kha,a.Lha,a)||(a.nf=c,a.Ca=b,a.Fb=null,Dk(a),b!=3||c instanceof _.zk||Ek(a,c)))},rk=function(a,b,c,d){if(a instanceof _.lk)return yk(a,b,c,d),!0;if(_.bk(a))return a.then(b,c,d),!0;if(_.ub(a))try{var e=a.then;if(typeof e==="function")return Fk(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Fk=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||(f=!0,
d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},Dk=function(a){a.iG||(a.iG=!0,_.jk(a.vz,a))},Bk=function(a){var b=null;a.Dl&&(b=a.Dl,a.Dl=b.next,b.next=null);a.Dl||(a.fq=null);return b};_.lk.prototype.vz=function(){for(var a;a=Bk(this);)Ck(this,a,this.Ca,this.nf);this.iG=!1};Ck=function(a,b,c,d){if(c==3&&b.Kr&&!b.gy)for(;a&&a.xA;a=a.Fb)a.xA=!1;if(b.An)b.An.Fb=null,Gk(b,c,d);else try{b.gy?b.Rv.call(b.context):Gk(b,c,d)}catch(e){Hk.call(null,e)}nk.put(b)};
Gk=function(a,b,c){b==2?a.Rv.call(a.context,c):a.Kr&&a.Kr.call(a.context,c)};Ek=function(a,b){a.xA=!0;_.jk(function(){a.xA&&Hk.call(null,b)})};Hk=_.ph;_.zk=function(a){_.jb.call(this,a);this.yZ=!1};_.cb(_.zk,_.jb);_.zk.prototype.name="cancel";var uk=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
_.Ik=function(a){return new _.lk(a)};
var Qk=function(){this.vx={sZ:Jk?"../"+Jk:null,gz:Kk,iU:Lk,ssa:Mk,jo:Nk,jta:Ok};this.Sf=_.ye;this.JY=this.Q8;this.J9=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.vx.sZ){this.Sf=this.vx.iU(this.Sf,this.vx.sZ);var a=this.Sf.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.JY=this.Sf.doPostMsg}this.fN={};this.IN={};a=(0,_.Ab)(this.FH,
this);_.Oe(this.Sf,"message",a);_.Ee(_.Pe,"RPMQ",[]).push(a);this.Sf!=this.Sf.parent&&Pk(this,this.Sf.parent,this.XI(this.Sf.name),"*")};Qk.prototype.XI=function(a){return'{"h":"'+escape(a)+'"}'};var Rk=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},Sk=function(a){if(!/^\s*{/.test(a))return!1;a=_.Qf(a);return a!==null&&typeof a==="object"&&!!a.g};
Qk.prototype.FH=function(a){var b=String(a.data);_.Vf.debug("gapix.rpc.receive("+Mk+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=Sk(b);if(!c&&!d){if(!d&&(c=Rk(b))){if(this.fN[c])this.fN[c]();else this.IN[c]=1;return}var e=a.origin,f=this.vx.gz;this.J9?_.ye.setTimeout(function(){f(b,e)},0):f(b,e)}};Qk.prototype.Hb=function(a,b){a===".."||this.IN[a]?(b(),delete this.IN[a]):this.fN[a]=b};
var Pk=function(a,b,c,d){var e=Sk(c)?"":"!_";_.Vf.debug("gapix.rpc.send("+Mk+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.JY(b,e+c,d)};Qk.prototype.Q8=function(a,b,c){a.postMessage(b,c)};Qk.prototype.send=function(a,b,c){(a=this.vx.iU(this.Sf,a))&&!a.closed&&Pk(this,a,b,c)};var Tk,Uk,Vk,Wk,Xk,Yk,Zk,Jk,Mk,$k,al,bl,Lk,Nk,dl,el,jl,kl,ml,Ok,ol,nl,fl,gl,pl,Kk,ql,rl;Tk=0;Uk=[];Vk={};Wk={};Xk=_.ye.location.href;Yk=_.Je(Xk,"rpctoken");Zk=_.Je(Xk,"parent")||_.Be.referrer;Jk=_.Je(Xk,"rly");Mk=Jk||(_.ye!==_.ye.top||_.ye.opener)&&_.ye.name||"..";$k=null;al={};bl=function(){};_.cl={send:bl,Hb:bl,XI:bl};
Lk=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=_.ye.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=_.za(e.document.getElementsByTagName("iframe"));
for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error("F`"+c+"`"+a);}}else return null}return c};Nk=function(a){return(a=Vk[a])&&a.token};dl=function(a){if(a.f in{})return!1;var b=a.t,c=Vk[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")};
el=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}};_.hl=function(a,b,c){a=fl(a);Wk[a.name]={Qh:b,Fv:a.Fv,Rs:c||dl};gl()};_.il=function(a){a=fl(a);delete Wk[a.name]};jl={};kl=function(a,b){(a=jl["_"+a])&&a[1](this)&&a[0].call(this,b)};ml=function(a){var b=a.c;if(!b)return bl;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.ll.apply(null,e)}};
Ok=function(a){$k=a};ol=function(a){al[a]||(al[a]=_.ye.setTimeout(function(){al[a]=!1;nl(a)},0))};nl=function(a){var b=Vk[a];if(b&&b.ready){var c=b.lK;for(b.lK=[];c.length;)_.cl.send(a,_.Rf(c.shift()),b.origin)}};fl=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),Fv:!0}:{name:a,Fv:!1}};
gl=function(){for(var a=_.ai("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=Uk[d];++d){var e=c.lp;if(!e||a>0&&b-c.timestamp>a)Uk.splice(d,1),--d;else{var f=e.s,h=Wk[f]||Wk["*"];if(h)if(Uk.splice(d,1),--d,e.origin=c.origin,c=ml(e),e.callback=c,h.Rs(e)){if(f!=="__cb"&&!!h.Fv!=!!e.g)break;e=h.Qh.apply(e,e.a);e!==void 0&&c(e)}else _.Vf.debug("gapix.rpc.rejected("+Mk+"): "+f)}}};pl=function(a,b,c){Uk.push({lp:a,origin:b,timestamp:(new Date).getTime()/1E3});c||gl()};
Kk=function(a,b){a=_.Qf(a);pl(a,b,!1)};ql=function(a){for(;a.length;)pl(a.shift(),this.origin,!0);gl()};rl=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=0&&(b=!0);return{id:c,origin:a[1]||"*",tI:b}};
_.sl=function(a,b,c,d){var e=rl(a);d&&(_.ye.frames[e.id]=_.ye.frames[e.id]||d);a=e.id;if(!Vk.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=_.Hg(Zk),c=c||Yk;else if(!e.tI){var f=_.Be.getElementById(a);f&&(f=f.src,d=_.Hg(f),c=c||_.Je(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);Vk[a]={token:c,lK:[],origin:d,Dfa:b,kZ:function(){var h=a;Vk[h].ready=1;nl(h)}};_.cl.Hb(a,Vk[a].kZ)}return Vk[a].kZ};
_.ll=function(a,b,c,d){a=a||"..";_.sl(a);a=a.split("|",1)[0];var e=b,f=a,h=[].slice.call(arguments,3),k=c,l=Mk,m=Yk,n=Vk[f],p=l,q=rl(f);if(n&&f!==".."){if(q.tI){if(!(m=Vk[f].Dfa)){m=$k?$k.substring(1).split("/"):[Mk];p=m.length-1;for(f=_.ye.parent;f!==_.ye.top;){var t=f.parent;if(!p--){for(var v=null,u=t.frames.length,x=0;x<u;++x)t.frames[x]==f&&(v=x);m.unshift("{"+v+"}")}f=t}m="/"+m.join("/")}p=m}else p=l="..";m=n.token}k&&q?(n=dl,q.tI&&(n=el(q)),jl["_"+ ++Tk]=[k,n],k=Tk):k=null;h={s:e,f:l,r:p,t:m,
c:k,a:h};e=fl(e);h.s=e.name;h.g=e.Fv;Vk[a].lK.push(h);ol(a)};if(typeof _.ye.postMessage==="function"||typeof _.ye.postMessage==="object")_.cl=new Qk,_.hl("__cb",kl,function(){return!0}),_.hl("_processBatch",ql,function(){return!0}),_.sl("..");
var ul,vl,wl,xl,zl,Al,Bl,Cl,Dl,Il,Jl,Kl,Ol,Pl,Ql,Rl,Sl,Tl,Ul,Vl;_.tl=function(a,b){if(!a)throw Error(b||"");};ul=/&/g;vl=/</g;wl=/>/g;xl=/"/g;zl=/'/g;Al=function(a){return String(a).replace(ul,"&amp;").replace(vl,"&lt;").replace(wl,"&gt;").replace(xl,"&quot;").replace(zl,"&#39;")};Bl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Cl=/%([a-f]|[0-9a-fA-F][a-f])/g;Dl=/^(https?|ftp|file|chrome-extension):$/i;
Il=function(a){a=String(a);a=a.replace(Bl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Le,function(e){return e.replace(/%/g,"%25")}).replace(Cl,function(e){return e.toUpperCase()});a=a.match(_.Ke)||[];var b=_.Fe(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Dl);b.base=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Yi=a[7]?[d(a[7])]:[];return b};Jl=function(a){return a.base+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Yi.length>0?"#"+a.Yi.join("&"):"")};Kl=function(a,b){var c=[];if(a)for(var d in a)if(_.Ge(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Ll=function(a,b,c,d){a=Il(a);a.query.push.apply(a.query,Kl(b,d));a.Yi.push.apply(a.Yi,Kl(c,d));return Jl(a)};
_.Ml=function(a,b){var c=Il(b);b=c.base;c.query.length&&(b+="?"+c.query.join(""));c.Yi.length&&(b+="#"+c.Yi.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Me,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Il(b);b=c.base;c.query.length&&(b+="?"+c.query.join(""));c.Yi.length&&(b+="#"+c.Yi.join(""));_.Hc(a,new _.mc(_.xe(b)));e.appendChild(a);_.Kc(e,_.ec(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Il(b+d);
b=c.base;c.query.length&&(b+="?"+c.query.join(""));c.Yi.length&&(b+="#"+c.Yi.join(""));return b};_.Nl=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;Pl=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};Ql=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
Rl=function(){var a=_.ai("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(Ql))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};Sl=function(){var a=_.Pe.onl;if(!a){a=_.Fe();_.Pe.onl=a;var b=_.Fe();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};Tl=function(a,b){b=b.onload;return typeof b==="function"?(Sl().a(a,b),b):null};
Ul=function(a){_.tl(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};Vl=function(a){Sl().r(a)};var Xl,Yl,bm;_.Wl={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};Xl={allowtransparency:!0,onload:!0};Yl=0;_.Zl=function(a,b){var c=0;do var d=b.id||["I",Yl++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.tl(c<5,"Error creating iframe id");return d};_.$l=function(a,b){return a?b+"/"+a:""};
_.am=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.He(d.queryParams||{},e);_.He(d.fragmentParams||{},f);var h=d.pfname;var k=_.Fe();_.ai("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Je(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Je(a.location.href,"_gfid","")||_.Je(a.location.href,"id",""),h=_.$l(h,_.Je(a.location.href,"pfname","")));h||(c=_.Qf(_.Je(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.$l(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Rf(k),k=h);h=_.Je(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Fi()*1E8)),k.rpctoken=h);d.rpctoken=h;_.He(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.Fe();(h=_.Je(k,"_bsh",_.Pe.bsh))&&(a._bsh=h);(k=_.Pe.dpo?_.Pe.h:_.Je(k,"jsh",_.Pe.h))&&(a.jsh=k);d.hintInFragment?_.He(a,f):_.He(a,e);return _.Ll(b,e,f,d.paramsSerializer)};
bm=function(a){_.tl(!a||_.Nl.test(a),"Illegal url for new iframe - "+a)};
_.cm=function(a,b,c,d,e){bm(c.src);var f,h=Tl(d,c),k=h?Ul(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+Al(String(c.frameborder))+'" scrolling="'+Al(String(c.scrolling))+'" '+k+' name="'+Al(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.$d(a).createElement("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},Vl(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.He(a,f.style):Xl[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||Pl(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var dm,gm;dm=/^:[\w]+$/;_.em=/:([a-zA-Z_]+):/g;_.fm=function(){var a=_.ei()||"0",b=Rl();var c=_.ei()||a;var d=Rl(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.ai("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.ai("iframes/:socialhost:"),h=_.ai("iframes/:im_socialhost:");return Ol={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};gm=function(a,b){return _.fm()[b]||""};
_.hm=function(a){return _.Ml(_.Be,a.replace(_.em,gm))};_.im=function(a){var b=a;dm.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.ai(b),_.tl(!!b,"Unknown iframe url config for - "+a));return _.hm(b)};
_.jm=function(a,b,c){c=c||{};var d=c.attributes||{};_.tl(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.im(a);d=b.ownerDocument||_.Be;var e=_.Zl(d,c);a=_.am(d,a,e,c);var f=c,h=_.Fe();_.He(_.Wl,h);_.He(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Il(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.cm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.cm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Jl(c);_.tl(_.Nl.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.wc(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.cm(d,b,h,e,f);return b};
var km;
km=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.lm=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return km();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
var mm=function(a,b){return _.bi(a,b,!0)},nm=function(a){this.T=a||{}},om=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,
d){a().Context.prototype.connectIframes.apply(this,[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};
b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=
function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},pm=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,
[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,
[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,
[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,
[c,d])};return b},qm,rm,vm,xm,Cm,Km,Lm,Nm,Rm,Sm,Vm,Xm,Ym,$m,Zm,an;_.Qj.prototype.Nz=_.lb(5,function(){return this.T.controller});_.Qj.prototype.ds=_.lb(4,function(a){this.T.apis=a;return this});qm=function(a,b){a.T.onload=b};rm=function(a){return a.T.rpctoken};_.sm=function(a,b){a.T.queryParams=b;return a};_.tm=function(a,b){a.T.relayOpen=b;return a};_.um=function(a,b){a.T.onClose=b;return a};vm=function(a,b){a.T.controllerData=b};_.wm=function(a){a.T.waitForOnload=!0};
xm=function(a){return(a=a.T.timeout)?a:null};_.ym=function(a){return!!a&&typeof a==="object"&&_.De.test(a.push)};_.zm=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.Am=function(a,b,c){if(a){_.tl(_.ym(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};
_.Bm=function(a,b,c){if(a)if(_.ym(a))_.Am(a,b,c);else{_.tl(typeof a==="object","objectForEach was called with a non object value");c=c||a;for(var d in a)_.Ge(a,d)&&a[d]!==void 0&&b.call(c,a[d],d)}};Cm=function(a){this.T=a||{}};Cm.prototype.value=function(){return this.T};Cm.prototype.getIframe=function(){return this.T.iframe};var Dm=function(a,b){a.T.role=b;return a},Em=function(a,b){a.T.data=b;return a};Cm.prototype.Tk=function(a){this.T.setRpcReady=a;return this};var Fm=function(a){return a.T.setRpcReady};
Cm.prototype.hn=function(a){this.T.rpctoken=a;return this};var Gm=function(a){a.T.selfConnect=!0;return a};nm.prototype.value=function(){return this.T};var Im=function(a){var b=new Hm;b.T.role=a;return b};nm.prototype.WT=function(){return this.T.role};nm.prototype.Dc=function(a){this.T.handler=a;return this};nm.prototype.wb=function(){return this.T.handler};var Jm=function(a,b){a.T.filter=b;return a};nm.prototype.ds=function(a){this.T.apis=a;return this};Nm=/^[\w\.\-]*$/;
_.Om=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.Pm=function(){return!0};_.Qm=function(a){for(var b=_.Fe(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Bd]}};Rm=function(a,b,c){a=Km[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.pk(a[e].call(c,b,c)));return d};Sm=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.tl(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=Rm(a,d,b);!c&&d.length>0&&_.tk(d).then(e)}}};
_.Tm=function(a,b,c){_.tl(a!="_default","Cannot update default api");Lm[a]={map:b,filter:c}};_.Um=function(a,b,c){_.tl(a!="_default","Cannot update default api");_.Ee(Lm,a,{map:{},filter:_.Om}).map[b]=c};Vm=function(a,b){_.Ee(Lm,"_default",{map:{},filter:_.Pm}).map[a]=b;_.Bm(_.Mm.Xf,function(c){c.register(a,b,_.Pm)})};_.Wm=function(){return _.Mm};Xm=/^https?:\/\/[^\/%\\?#\s]+$/i;
Ym={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};$m=function(a){this.resolve=this.reject=null;this.promise=_.Ik((0,_.Ab)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=Zm(this.promise,a))};Zm=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};an=function(a){this.dg=a;this.Context=om(a);this.Iframe=pm(a)};_.g=an.prototype;
_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.dg().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.dg().SAME_ORIGIN_IFRAMES_FILTER(a)};_.g.create=function(a,b,c){return this.dg().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.dg().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.dg().getContext()};_.g.getStyle=function(a){return this.dg().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.dg().makeWhiteListIframesFilter(a)};
_.g.registerBeforeOpenStyle=function(a,b){return this.dg().registerBeforeOpenStyle(a,b)};_.g.registerIframesApi=function(a,b,c){return this.dg().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.dg().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.dg().registerStyle(a,b)};var bn=function(){this.zi=[]};bn.prototype.dg=function(a){return this.zi.length?cn(this.zi[0],a):void 0};var cn=function(a,b){b=b===void 0?function(c){return new c}:b;return a.ctor?b(a.ctor):a.instance},dn=function(){bn.apply(this,arguments)};_.y(dn,bn);var fn=function(a){var b=en.VQ,c=a.priority,d=~mm(b.zi,function(e){return e.priority<c?-1:1});b.zi.splice(d,0,a)};var en=new function(){var a=this;this.VQ=new dn;this.instance=new an(function(){return a.VQ.dg()()})};fn({instance:function(){return window.gapi.iframes},priority:1});_.gn=en.instance;var hn,jn;hn={height:!0,width:!0};jn=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.kn=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var ln=function(){Cm.apply(this,arguments)};_.y(ln,Cm);var Hm=function(){nm.apply(this,arguments)};_.y(Hm,nm);var mn=function(){_.Qj.apply(this,arguments)};_.y(mn,_.Qj);var nn=function(a){mn.call(this,a)};_.y(nn,mn);var on=function(a,b){a.T.frameName=b;return a};nn.prototype.getFrameName=function(){return this.T.frameName};var pn=function(a,b){a.T.rpcAddr=b;return a};nn.prototype.kg=function(){return this.T.rpcAddr};var qn=function(a,b){a.T.retAddr=b;return a};_.g=nn.prototype;_.g.Yh=function(){return this.T.retAddr};_.g.Nj=function(a){this.T.origin=a;return this};_.g.getOrigin=function(){return this.T.origin};_.g.Tk=function(a){this.T.setRpcReady=a;return this};
_.g.xp=function(a){this.T.context=a};var rn=function(a,b){a.T._rpcReadyFn=b};nn.prototype.getIframeEl=function(){return this.T.iframeEl};var sn=function(a,b,c){var d=a.kg(),e=b.Yh();qn(pn(c,a.Yh()+"/"+b.kg()),e+"/"+d);on(c,b.getFrameName()).Nj(b.getOrigin())};var un=function(a,b,c){a.setTimeout(function(){b.closed||c==5?tn(b):(b.close(),c++,un(a,b,c))},1E3)},tn=function(a){a.closed||a.document&&a.document.body&&_.we(a.document.body,"Please close this window.")};_.vn=function(a,b,c,d){this.Jg=!1;this.qb=a;this.GK=b;this.vq=c;this.Ja=d;this.LZ=this.Ja.Yh();this.Bd=this.Ja.getOrigin();this.Uba=this.Ja.getIframeEl();this.B0=this.Ja.T.where;this.zi=[];this.applyIframesApi("_default");a=this.Ja.T.apis||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.qb.Xf[c]=this};_.g=_.vn.prototype;_.g.isDisposed=function(){return this.Jg};
_.g.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.zi.length;a++)this.unregister(this.zi[a]);delete _.Mm.Xf[this.getFrameName()];this.Jg=!0}};_.g.getContext=function(){return this.qb};_.g.getOptions=function(){return this.Ja};_.g.kg=function(){return this.GK};_.g.Yh=function(){return this.LZ};_.g.getFrameName=function(){return this.vq};_.g.getIframeEl=function(){return this.Uba};_.g.getSiteEl=function(){return this.B0};_.g.setSiteEl=function(a){this.B0=a};_.g.Tk=function(){(0,this.Ja.T._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Ja.value()[a]=b};_.g.getParam=function(a){return this.Ja.value()[a]};_.g.vc=function(){return this.Ja.value()};_.g.getId=function(){return this.Ja.getId()};_.g.getOrigin=function(){return this.Bd};var wn=function(a,b){var c=a.vq;a=a.qb.getFrameName();return c+":"+a+":"+b};_.g=_.vn.prototype;
_.g.register=function(a,b,c){_.tl(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.tl((c||_.Om)(this),"Rejecting untrusted message "+a);c=wn(this,a);_.Ee(Km,c,[]).push(b)==1&&(this.zi.push(a),_.hl(c,Sm(c,this,a==="_g_wasClosed")))};_.g.unregister=function(a,b){var c=wn(this,a),d=Km[c];d&&(b?(b=_.zm.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=_.zm.call(this.zi,a),b>=0&&this.zi.splice(b,1),_.il(c)))};_.g.W$=function(){return this.zi};
_.g.applyIframesApi=function(a){this.NE=this.NE||[];if(!(_.zm.call(this.NE,a)>=0)){this.NE.push(a);a=Lm[a]||{map:{}};for(var b in a.map)_.Ge(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.Om(this))return null;var a=this.Ja.T._popupWindow;if(a)return a;var b=this.GK.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var xn=function(a){var b={};if(a)for(var c in a)_.Ge(a,c)&&_.Ge(hn,c)&&jn.test(a[c])&&(b[c]=a[c]);return b};_.g=_.vn.prototype;_.g.close=function(a,b){return yn(this,"_g_close",a,b)};_.g.restyle=function(a,b){return yn(this,"_g_restyle",a,b)};_.g.Wr=function(a,b){return yn(this,"_g_restyleDone",a,b)};_.g.u8=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.zfa=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
_.g.Afa=function(a){var b=this.Ja.T.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?xn(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(_.Ge(a,"height")&&(a.height=_.kn(a.height)),_.Ge(a,"width")&&(a.width=_.kn(a.width)),_.He(a,b.style))};
_.g.v8=function(a){var b=this.Ja.T.onClose;b&&b.call(this,a,this);if(b=this.getOptions().T._popupWindow){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();_.Gd&&_.zh&&c?(c.focus(),un(c,b,0)):(b.close(),tn(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Ja.Nz())c={},c.frameName=this.getFrameName(),yn(b,"_g_disposeControl",c);b=wn(this,"_g_wasClosed");Rm(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.fia=function(){delete this.getContext().Xf[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.Ab)(function(){this.dispose()},this),0)};
_.g.send=function(a,b,c,d){_.tl(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.tl((d||_.Om)(this),"Wrong target for message "+a);c=new $m(c);a=this.qb.getFrameName()+":"+this.vq+":"+a;_.ll(this.GK,a,c.resolve,b);return c.promise};var yn=function(a,b,c,d){return a.send(b,c,d,_.Pm)};_.g=_.vn.prototype;_.g.zea=function(a){return a};_.g.ping=function(a,b){return yn(this,"_g_ping",b,a)};
_.g.E8=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.kg()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];_.tl(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.kg();a._parentRetAddr=this.Yh();this.getContext();b=new _.zn(a);this.Oda&&this.Oda(b,a.controllerData);this.zF=this.zF||[];this.zF.push(b,a.controllerData)};
_.g.W8=function(a){a=(a||{}).frameName;for(var b=this.zF||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.Sda&&this.Sda(a);return}_.tl(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.B8=function(a){var b=new nn(a);a=new ln(b.value());if(a.T.selfConnect)var c=this;else(_.tl(Xm.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().Xf[b.getFrameName()],c)?Fm(b)&&(c.Tk(),yn(c,"_g_rpcReady")):(b=on(qn(pn(new nn,b.kg()),b.Yh()).Nj(b.getOrigin()),b.getFrameName()).Tk(Fm(b)).hn(rm(b)),c=this.getContext().attach(b.value()));b=this.getContext();var d=a.T.role;a=a.T.data;An(b);d=d||"";_.Ee(b.xF,d,[]).push({iframe:c,data:a});Bn(c,a,b.zJ[d])};
_.g.lM=function(a,b){(new nn(b)).T._relayedDepth||(b={},Gm(Dm(new ln(b),"_opener")),yn(a,"_g_connect",b))};
_.g.cY=function(a){var b=this,c=a.T.messageHandlers,d=a.T.messageHandlersFilter,e=a.T.onClose;_.um(_.Sj(_.Rj(a,null),null),null);return yn(this,"_g_open",a.value()).then(function(f){var h=new nn(f[0]),k=h.getFrameName();f=new nn;var l=b.Yh(),m=h.Yh();qn(pn(f,b.kg()+"/"+h.kg()),m+"/"+l);on(f,k);f.Nj(h.getOrigin());f.ds(h.T.apis);f.hn(rm(a));_.Rj(f,c);_.Sj(f,d);_.um(f,e);(h=b.getContext().Xf[k])||(h=b.getContext().attach(f.value()));return h})};
_.g.JK=function(a){var b=a.getUrl();_.tl(!b||_.Nl.test(b),"Illegal url for new iframe - "+b);var c=a.Xn().value();b={};for(var d in c)_.Ge(c,d)&&_.Ge(Ym,d)&&(b[d]=c[d]);_.Ge(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=xn(d)));a.value().attributes=b};
_.g.kea=function(a){a=new nn(a);this.JK(a);var b=a.T._relayedDepth||0;a.T._relayedDepth=b+1;a.T.openerIframe=this;var c=rm(a);a.hn(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=(new nn(e.vc())).T.apis,h=new nn;sn(e,d,h);b==0&&Dm(new ln(h.value()),"_opener");h.Tk(!0);h.hn(c);yn(e,"_g_connect",h.value());h=new nn;on(qn(pn(h,e.kg()),e.LZ),e.getFrameName()).Nj(e.getOrigin()).ds(f);return h.value()})};
_.g.yfa=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.Pm)},null,_.Pm)};var Gn;_.Cn=_.Fe();_.Dn=_.Fe();_.En=function(a,b){_.Cn[a]=b};_.Fn=function(a){return _.Cn[a]};Gn=function(a,b){_.Ie.load("gapi.iframes.style."+a,b)};_.Hn=function(a,b){_.Dn[a]=b};_.In=function(a){return _.Dn[a]};_.zn=function(a){a=a||{};this.Jg=!1;this.xi=_.Fe();this.Xf=_.Fe();this.Sf=a._window||_.ye;this.Hd=this.Sf.location.href;this.tY=(this.TJ=Jn(this.Hd,"parent"))?Jn(this.Hd,"pfname"):"";this.Da=this.TJ?Jn(this.Hd,"_gfid")||Jn(this.Hd,"id"):"";this.vq=_.$l(this.Da,this.tY);this.Bd=_.Hg(this.Hd);if(this.Da){var b=new nn;pn(b,a._parentRpcAddr||"..");qn(b,a._parentRetAddr||this.Da);b.Nj(_.Hg(this.TJ||this.Hd));on(b,this.tY);this.Fb=this.attach(b.value())}else this.Fb=null};_.g=_.zn.prototype;
_.g.isDisposed=function(){return this.Jg};_.g.dispose=function(){if(!this.isDisposed()){for(var a=_.za(Object.values(this.Xf)),b=a.next();!b.done;b=a.next())b.value.dispose();this.Jg=!0}};_.g.getFrameName=function(){return this.vq};_.g.getOrigin=function(){return this.Bd};_.g.getWindow=function(){return this.Sf};_.g.ub=function(){return this.Sf.document};_.g.setGlobalParam=function(a,b){this.xi[a]=b};_.g.getGlobalParam=function(a){return this.xi[a]};
_.g.attach=function(a){_.tl(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new nn(a);a.kg()||pn(a,a.getId());a.Yh()||qn(a,"..");a.getOrigin()||a.Nj(_.Hg(a.getUrl()));a.getFrameName()||on(a,_.$l(a.getId(),this.vq));var b=a.getFrameName();if(this.Xf[b])return this.Xf[b];var c=a.kg(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.Yh(),f=rm(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.Je(f,"rpctoken"));rn(a,_.sl(d,e,f,a.T._popupWindow));
d=((window.gadgets||{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.vn(this,c,b,a),k=a.T.messageHandlersFilter;_.Bm(a.T.messageHandlers,function(l,m){h.register(m,l,k)});Fm(a)&&h.Tk();yn(h,"_g_rpcReady");return h};_.g.JK=function(a){on(a,null);var b=a.getId();!b||Nm.test(b)&&!this.getWindow().document.getElementById(b)||(_.Vf.log("Ignoring requested iframe ID - "+b),a.Me(null))};var Jn=function(a,b){var c=_.Je(a,b);c||(c=_.Qf(_.Je(a,"jcp",""))[b]);return c||""};
_.zn.prototype.openChild=function(a){_.tl(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new nn(a);Kn(this,b);var c=b.getFrameName();if(c&&this.Xf[c])return this.Xf[c];this.JK(b);c=b.getUrl();_.tl(c,"No url for new iframe");var d=b.T.queryParams||{};d.usegapi="1";_.sm(b,d);d=this.MU&&this.MU(c,b);d||(d=b.T.where,_.tl(!!d,"No location for new iframe"),c=_.jm(c,d,a),b.T.iframeEl=c,d=c.getAttribute("id"));pn(b,d).Me(d);b.Nj(_.Hg(b.T.eurl||""));this.YW&&this.YW(b,b.getIframeEl());
c=this.attach(a);c.lM&&c.lM(c,a);(a=b.T.onCreate)&&a(c);b.T.disableRelayOpen||c.applyIframesApi("_open");return c};
var Ln=function(a,b,c){var d=b.T.canvasUrl;if(!d)return c;_.tl(!b.T.allowPost&&!b.T.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.tl(e&&_.Hg(e)===a.Bd&&_.Hg(d)===a.Bd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.wm(b);b.T.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.im(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},Mn=function(a,b,c){var d=b.T.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.vn?
(e=d,_.tm(b,0)):Number(d)>0&&_.tm(b,Number(d)-1);if(e){_.tl(!!e.cY,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.Dn[d])b.xp(a),d(b.value()),b.xp(null);b.T.openerIframe=null;c.resolve(e.cY(b));return!0}}return!1},Nn=function(a,b,c){var d=b.getStyle();if(d)if(_.tl(!!_.Fn,"Defer style is disabled, when requesting style "+d),_.Cn[d])Kn(a,b);else return Gn(d,function(){_.tl(!!_.Cn[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.zn.prototype.open=function(a,b){_.tl(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new nn(a);b=Ln(this,c,b);var d=new $m(b);(b=c.getUrl())&&c.setUrl(_.im(b));if(Mn(this,c,d)||Nn(this,c,d)||Mn(this,c,d))return d.promise;if(xm(c)!=null){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+xm(c)+"milliseconds"})},xm(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.T.waitForOnload&&qm(c.Xn(),function(){d.resolve(h)});
var h=this.openChild(a);c.T.waitForOnload||d.resolve(h);return d.promise};_.zn.prototype.getParentIframe=function(){return this.Fb};var On=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.iframe,b.params));return _.pk(d).then(function(e){return e&&c?(b.rY&&b.rY.call(a,b.params),e=b.sender?b.sender(b.params):yn(c,b.message,b.params),b.cia?e.then(function(){return!0}):!0):!1})};_.g=_.zn.prototype;
_.g.closeSelf=function(a,b,c){a=On(this,{sender:function(d){var e=_.Mm.getParentIframe();_.Bm(_.Mm.Xf,function(f){f!==e&&yn(f,"_g_wasClosed",d)});return yn(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,iframe:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new $m(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new $m(b);b.resolve(On(this,{message:"_g_restyleMe",params:a,iframe:c,filter:this.getGlobalParam("onRestyleSelfFilter"),cia:!0,rY:this.N1}));return b.promise};
_.g.N1=function(a){a.height==="auto"&&(a.height=_.lm())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var Kn=function(a,b){var c=b.getStyle();if(c){b.Fi(null);var d=_.Cn[c];_.tl(d,"No such style: "+c);b.xp(a);d(b.value());b.xp(null)}};
_.zn.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.Bm(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.N1(h);f&&f.send("_ready",h,c,_.Pm)};
_.zn.prototype.connectIframes=function(a,b){a=new ln(a);var c=new ln(b),d=Fm(a);b=a.getIframe();var e=c.getIframe();if(e){var f=rm(a),h=new nn;sn(b,e,h);Em(Dm((new ln(h.value())).hn(f),a.T.role),a.T.data).Tk(d);var k=new nn;sn(e,b,k);Em(Dm((new ln(k.value())).hn(f),c.T.role),c.T.data).Tk(!0);yn(b,"_g_connect",h.value(),function(){d||yn(e,"_g_connect",k.value())});d&&yn(e,"_g_connect",k.value())}else c={},Em(Dm(Gm(new ln(c)),a.T.role),a.T.data),yn(b,"_g_connect",c)};
var An=function(a){a.xF||(a.xF=_.Fe(),a.zJ=_.Fe())};_.zn.prototype.addOnConnectHandler=function(a,b,c,d){An(this);typeof a==="object"?(b=new Hm(a),c=b.WT()||""):(b=Jm(Im(a).Dc(b).ds(c),d),c=a);d=this.xF[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)Bn(this.Xf[d[e].iframe.getFrameName()],d[e].data,[b]),a=b.T.runOnce;c=_.Ee(this.zJ,c,[]);a||b.T.dontWait||c.push(b)};
_.zn.prototype.removeOnConnectHandler=function(a,b){a=_.Ee(this.zJ,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].wb()===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var Bn=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.T.filter||_.Om;if(a&&f(a)){f=e.T.apis||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.wb()&&e.wb()(a,b);e.T.runOnce&&(c.splice(d,1),--d)}}}};
_.zn.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=Jm(Im("_opener").Dc(a).ds(b),c);a.T.runOnce=!0;d.call(this,a.value())};_.zn.prototype.YW=function(a,b){var c=a.Nz();if(c){_.tl(c.Bd===a.getOrigin(),"Wrong controller origin "+this.Bd+" !== "+a.getOrigin());var d=a.kg();pn(a,c.kg());qn(a,c.Yh());var e=new nn;vm(pn(e,d),a.T.controllerData);_.Oe(b,"load",function(){c.send("_g_control",e.value())})}};
var Pn=function(a,b,c){a=a.getWindow();var d=a.document,e=c.T.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("G");}else f=_.Zl(d,c);var h=f,k=c.T.rpcRelayUrl;if(k){k=_.hm(k);h=c.T.fragmentParams||{};h.rly=f;c.T.fragmentParams=h;h=c.T.where||d.body;_.tl(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.cm(d,h,l,f);h=f+"_relay"}b=_.im(b);var m=_.am(d,b,f,c.value());c.T.eurl=m;b=c.T.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.ye.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Ic(a,m,h,b);return{id:f,q2:n}};_.zn.prototype.MU=function(a,b){if(b.T.openAsWindow){a=Pn(this,a,b);var c=a.id;_.tl(!!a.q2,"Open popup window failed");b.T._popupWindow=a.q2}return c};Km=_.Fe();Lm=_.Fe();_.Mm=new _.zn;Vm("_g_rpcReady",_.vn.prototype.Tk);Vm("_g_discover",_.vn.prototype.W$);Vm("_g_ping",_.vn.prototype.zea);Vm("_g_close",_.vn.prototype.u8);Vm("_g_closeMe",_.vn.prototype.v8);Vm("_g_restyle",_.vn.prototype.zfa);Vm("_g_restyleMe",_.vn.prototype.Afa);Vm("_g_wasClosed",_.vn.prototype.fia);_.Um("control","_g_control",_.vn.prototype.E8);_.Um("control","_g_disposeControl",_.vn.prototype.W8);var Qn=_.Mm.getParentIframe();
Qn&&Qn.register("_g_restyleDone",_.vn.prototype.yfa,_.Pm);Vm("_g_connect",_.vn.prototype.B8);var Rn={};Rn._g_open=_.vn.prototype.kea;_.Tm("_open",Rn,_.Pm);var Sn={Context:_.zn,Iframe:_.vn,SAME_ORIGIN_IFRAMES_FILTER:_.Om,CROSS_ORIGIN_IFRAMES_FILTER:_.Pm,makeWhiteListIframesFilter:_.Qm,getContext:_.Wm,registerIframesApi:_.Tm,registerIframesApiHandler:_.Um,registerStyle:_.En,registerBeforeOpenStyle:_.Hn,getStyle:_.Fn,getBeforeOpenStyle:_.In,create:_.jm};fn({instance:function(){return Sn},priority:2});_.Um("gapi.load","_g_gapi.load",function(a){return new _.lk(function(b){_.Ie.load(a&&typeof a==="object"&&a.features||"",b)})});
_.co=function(a,b){a.T.where=b;return a};_.eo=function(){_.Qj.apply(this,arguments)};_.y(_.eo,_.Qj);
_.fo=_.Fe();
_.go={};window.iframer=_.go;
var io=function(a){var b=[new ho];if(b.length===0)throw Error("j");if(b.map(function(c){if(c instanceof ho)c=c.TY;else throw Error("j");return c}).every(function(c){return"data-gapiscan".indexOf(c)!==0}))throw Error("k`data-gapiscan");a.setAttribute("data-gapiscan","true")},ho=function(){this.TY=jo[0].toLowerCase()},ko,lo,mo,no,oo,so,to;ho.prototype.toString=function(){return this.TY};ko=function(a){if(_.De.test(Object.keys))return Object.keys(a);var b=[],c;for(c in a)_.Ge(a,c)&&b.push(c);return b};
lo={button:!0,div:!0,span:!0};mo=function(a){var b=_.Ee(_.Pe,"sws",[]);return _.zm.call(b,a)>=0};no=function(a){return _.Ee(_.Pe,"watt",_.Fe())[a]};oo=function(a){return function(b,c){return a?_.fm()[c]||a[c]||"":_.fm()[c]||""}};_.po={callback:1,clientid:1,cookiepolicy:1,openidrealm:-1,includegrantedscopes:-1,requestvisibleactions:1,scope:1};_.qo=!1;
_.ro=function(){if(!_.qo){for(var a=document.getElementsByTagName("meta"),b=0;b<a.length;++b){var c=a[b].name.toLowerCase();if(_.Bc(c,"google-signin-")){c=c.substring(14);var d=a[b].content;_.po[c]&&d&&(_.fo[c]=d)}}if(window.self!==window.top){a=document.location.toString();for(var e in _.po)_.po[e]>0&&(b=_.Je(a,e,""))&&(_.fo[e]=b)}_.qo=!0}e=_.Fe();_.He(_.fo,e);return e};so=function(a){var b;a.match(/^https?%3A/i)&&(b=decodeURIComponent(a));a=b?b:a;return _.Ml(document,a)};
to=function(a){a=a||"canonical";for(var b=document.getElementsByTagName("link"),c=0,d=b.length;c<d;c++){var e=b[c],f=e.getAttribute("rel");if(f&&f.toLowerCase()==a&&(e=e.getAttribute("href"))&&(e=so(e))&&e.match(/^https?:\/\/[\w\-_\.]+/i)!=null)return e}return window.location.href};_.uo=function(){return window.location.origin||window.location.protocol+"//"+window.location.host};_.vo=function(a,b,c,d){return(a=typeof a=="string"?a:void 0)?so(a):to(d)};
_.wo=function(a,b,c){a==null&&c&&(a=c.db,a==null&&(a=c.gwidget&&c.gwidget.db));return a||void 0};_.xo=function(a,b,c){a==null&&c&&(a=c.ecp,a==null&&(a=c.gwidget&&c.gwidget.ecp));return a||void 0};_.yo=function(a,b,c){return _.vo(a,b,c,b.action?void 0:"publisher")};var zo,Ao,Bo,Co,Do,Ho,Jo,Io;zo={se:"0"};Ao={post:!0};Bo={style:"position:absolute;top:-10000px;width:450px;margin:0px;border-style:none"};Co="onPlusOne _ready _close _open _resizeMe _renderstart oncircled drefresh erefresh".split(" ");Do=_.Ee(_.Pe,"WI",_.Fe());Ho=["style","data-gapiscan"];
Jo=function(a){for(var b=_.Fe(),c=a.nodeName.toLowerCase().indexOf("g:")!=0,d=a.attributes.length,e=0;e<d;e++){var f=a.attributes[e],h=f.name,k=f.value;_.zm.call(Ho,h)>=0||c&&h.indexOf("data-")!=0||k==="null"||"specified"in f&&!f.specified||(c&&(h=h.substr(5)),b[h.toLowerCase()]=k)}a=a.style;(c=Io(a&&a.height))&&(b.height=String(c));(a=Io(a&&a.width))&&(b.width=String(a));return b};
_.Lo=function(a,b,c,d,e,f){if(c.rd)var h=b;else h=document.createElement("div"),b.dataset.gapistub=!0,h.style.cssText="position:absolute;width:450px;left:-10000px;",b.parentNode.insertBefore(h,b);f.siteElement=h;h.id||(h.id=_.Ko(a));b=_.Fe();b[">type"]=a;_.He(c,b);a=_.jm(d,h,e);f.iframeNode=a;f.id=a.getAttribute("id")};_.Ko=function(a){_.Ee(Do,a,0);return"___"+a+"_"+Do[a]++};Io=function(a){var b=void 0;typeof a==="number"?b=a:typeof a==="string"&&(b=parseInt(a,10));return b};var jo=_.ld(["data-"]),Mo,No,Oo,Po,Qo=/(?:^|\s)g-((\S)*)(?:$|\s)/,Ro={plusone:!0,autocomplete:!0,profile:!0,signin:!0,signin2:!0};Mo=_.Ee(_.Pe,"SW",_.Fe());No=_.Ee(_.Pe,"SA",_.Fe());Oo=_.Ee(_.Pe,"SM",_.Fe());Po=_.Ee(_.Pe,"FW",[]);
var So=function(a,b){return(typeof a==="string"?document.getElementById(a):a)||b},Wo=function(a,b){var c;To.ps0=(new Date).getTime();Uo("ps0");a=So(a,_.Be);var d=_.Be.documentMode;if(a.querySelectorAll&&(!d||d>8)){d=b?[b]:ko(Mo).concat(ko(No)).concat(ko(Oo));for(var e=[],f=0;f<d.length;f++){var h=d[f];e.push(".g-"+h,"g\\:"+h)}d=a.querySelectorAll(e.join(","))}else d=a.getElementsByTagName("*");a=_.Fe();for(e=0;e<d.length;e++){f=d[e];var k=f;h=b;var l=k.nodeName.toLowerCase(),m=void 0;if(k.hasAttribute("data-gapiscan"))h=
null;else{var n=l.indexOf("g:");n==0?m=l.substr(2):(n=(n=String(k.className||k.getAttribute("class")))&&Qo.exec(n))&&(m=n[1]);h=!m||!(Mo[m]||No[m]||Oo[m])||h&&m!==h?null:m}h&&(Ro[h]||f.nodeName.toLowerCase().indexOf("g:")==0||ko(Jo(f)).length!=0)&&(io(f),_.Ee(a,h,[]).push(f))}for(p in a)Po.push(p);To.ps1=(new Date).getTime();Uo("ps1");if(b=Po.join(":"))try{_.Ie.load(b,void 0)}catch(q){_.Vf.log(q);return}e=[];for(c in a){d=a[c];var p=0;for(b=d.length;p<b;p++)f=d[p],Vo(c,f,Jo(f),e,b)}};var Xo=function(a,b){var c=no(a);b&&c?(c(b),(c=b.iframeNode)&&c.setAttribute("data-gapiattached",!0)):_.Ie.load(a,function(){var d=no(a),e=b&&b.iframeNode,f=b&&b.userParams;e&&d?(d(b),e.setAttribute("data-gapiattached",!0)):(d=_.Ie[a].go,a=="signin2"?d(e,f):d(e&&e.parentNode,f))})},Vo=function(a,b,c,d,e,f,h){switch(Yo(b,a,f)){case 0:a=Oo[a]?a+"_annotation":a;d={};d.iframeNode=b;d.userParams=c;Xo(a,d);break;case 1:if(b.parentNode){for(var k in c){if(f=_.Ge(c,k))f=c[k],f=!!f&&typeof f==="object"&&(!f.toString||
f.toString===Object.prototype.toString||f.toString===Array.prototype.toString);if(f)try{c[k]=_.Rf(c[k])}catch(x){delete c[k]}}k=!0;c.dontclear&&(k=!1);delete c.dontclear;var l;f={};var m=l=a;a=="plus"&&c.action&&(l=a+"_"+c.action,m=a+"/"+c.action);(l=_.Ze("iframes/"+l+"/url"))||(l=":im_socialhost:/:session_prefix::im_prefix:_/widget/render/"+m+"?usegapi=1");for(n in zo)f[n]=n+"/"+(c[n]||zo[n])+"/";var n=_.Ml(_.Be,l.replace(_.em,oo(f)));m="iframes/"+a+"/params/";f={};_.He(c,f);(l=_.Ze("lang")||_.Ze("gwidget/lang"))&&
(f.hl=l);Ao[a]||(f.origin=_.uo());f.exp=_.Ze(m+"exp");if(m=_.Ze(m+"location"))for(l=0;l<m.length;l++){var p=m[l];f[p]=_.ye.location[p]}switch(a){case "plus":case "follow":f.url=_.yo(f.href,c,null);delete f.href;break;case "plusone":m=(m=c.href)?so(m):to();f.url=m;f.db=_.wo(c.db,void 0,_.Ze());f.ecp=_.xo(c.ecp,void 0,_.Ze());delete f.href;break;case "signin":f.url=to()}_.Pe.ILI&&(f.iloader="1");delete f["data-onload"];delete f.rd;for(var q in zo)f[q]&&delete f[q];f.gsrc=_.Ze("iframes/:source:");q=
_.Ze("inline/css");typeof q!=="undefined"&&e>0&&q>=e&&(f.ic="1");q=/^#|^fr-/;e={};for(var t in f)_.Ge(f,t)&&q.test(t)&&(e[t.replace(q,"")]=f[t],delete f[t]);t=_.Ze("iframes/"+a+"/params/si")=="q"?f:e;q=_.ro();for(var v in q)!_.Ge(q,v)||_.Ge(f,v)||_.Ge(e,v)||(t[v]=q[v]);v=[].concat(Co);t=_.Ze("iframes/"+a+"/methods");_.ym(t)&&(v=v.concat(t));for(u in c)_.Ge(c,u)&&/^on/.test(u)&&(a!="plus"||u!="onconnect")&&(v.push(u),delete f[u]);delete f.callback;e._methods=v.join(",");var u=_.Ll(n,f,e);v=h||{};v.allowPost=
1;v.attributes=Bo;v.dontclear=!k;h={};h.userParams=c;h.url=u;h.type=a;_.Lo(a,b,c,u,v,h);b=h.id;c=_.Fe();c.id=b;c.userParams=h.userParams;c.url=h.url;c.type=h.type;c.state=1;_.Vn[b]=c;b=h}else b=null;b&&((c=b.id)&&d.push(c),Xo(a,b))}},Yo=function(a,b,c){if(a&&a.nodeType===1&&b){if(c)return 1;if(Oo[b]){if(lo[a.nodeName.toLowerCase()])return(a=a.innerHTML)&&a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")?0:1}else{if(No[b])return 0;if(Mo[b])return 1}}return null};_.Ee(_.Ie,"platform",{}).go=function(a,b){Wo(a,b)};var Zo=_.Ee(_.Pe,"perf",_.Fe()),To=_.Ee(Zo,"g",_.Fe()),$o=_.Ee(Zo,"i",_.Fe()),ap,bp,cp,Uo,ep,fp,gp;_.Ee(Zo,"r",[]);ap=_.Fe();bp=_.Fe();cp=function(a,b,c,d){ap[c]=ap[c]||!!d;_.Ee(bp,c,[]);bp[c].push([a,b])};Uo=function(a,b,c){var d=Zo.r;typeof d==="function"?d(a,b,c):d.push([a,b,c])};ep=function(a,b,c,d){if(b=="_p")throw Error("H");_.dp(a,b,c,d)};_.dp=function(a,b,c,d){fp(b,c)[a]=d||(new Date).getTime();Uo(a,b,c)};fp=function(a,b){a=_.Ee($o,a,_.Fe());return _.Ee(a,b,_.Fe())};
gp=function(a,b,c){var d=null;b&&c&&(d=fp(b,c)[a]);return d||To[a]};(function(){function a(h){this.t={};this.tick=function(k,l,m){this.t[k]=[m!=void 0?m:(new Date).getTime(),l];if(m==void 0)try{window.console.timeStamp("CSI/"+k)}catch(n){}};this.getStartTickTime=function(){return this.t.start[0]};this.tick("start",null,h)}var b;if(window.performance)var c=(b=window.performance.timing)&&b.responseStart;var d=c>0?new a(c):new a;window.__gapi_jstiming__={Timer:a,load:d};if(b){var e=b.navigationStart;e>0&&c>=e&&(window.__gapi_jstiming__.srt=c-e)}if(b){var f=window.__gapi_jstiming__.load;
e>0&&c>=e&&(f.tick("_wtsrt",void 0,e),f.tick("wtsrt_","_wtsrt",c),f.tick("tbsd_","wtsrt_"))}try{b=null,window.chrome&&window.chrome.csi&&(b=Math.floor(window.chrome.csi().pageT),f&&e>0&&(f.tick("_tbnd",void 0,window.chrome.csi().startE),f.tick("tbnd_","_tbnd",e))),b==null&&window.gtbExternal&&(b=window.gtbExternal.pageT()),b==null&&window.external&&(b=window.external.pageT,f&&e>0&&(f.tick("_tbnd",void 0,window.external.startE),f.tick("tbnd_","_tbnd",e))),b&&(window.__gapi_jstiming__.pt=b)}catch(h){}})();if(window.__gapi_jstiming__){window.__gapi_jstiming__.IP={};window.__gapi_jstiming__.hfa=1;var hp=function(a,b,c){var d=a.t[b],e=a.t.start;if(d&&(e||c))return d=a.t[b][0],e=c!=void 0?c:e[0],Math.round(d-e)},ip=function(a,b,c){var d="";window.__gapi_jstiming__.srt&&(d+="&srt="+window.__gapi_jstiming__.srt,delete window.__gapi_jstiming__.srt);window.__gapi_jstiming__.pt&&(d+="&tbsrt="+window.__gapi_jstiming__.pt,delete window.__gapi_jstiming__.pt);try{window.external&&window.external.tran?d+="&tran="+
window.external.tran:window.gtbExternal&&window.gtbExternal.tran?d+="&tran="+window.gtbExternal.tran():window.chrome&&window.chrome.csi&&(d+="&tran="+window.chrome.csi().tran)}catch(p){}var e=window.chrome;if(e&&(e=e.loadTimes)&&typeof e==="function"&&(e=e())){e.wasFetchedViaSpdy&&(d+="&p=s");if(e.wasNpnNegotiated){d+="&npn=1";var f=e.npnNegotiatedProtocol;f&&(d+="&npnv="+(encodeURIComponent||escape)(f))}e.wasAlternateProtocolAvailable&&(d+="&apa=1")}var h=a.t,k=h.start;e=[];f=[];for(var l in h)if(l!=
"start"&&l.indexOf("_")!=0){var m=h[l][1];m?h[m]&&f.push(l+"."+hp(a,l,h[m][0])):k&&e.push(l+"."+hp(a,l))}delete h.start;if(b)for(var n in b)d+="&"+n+"="+b[n];(b=c)||(b="https:"==document.location.protocol?"https://csi.gstatic.com/csi":"http://csi.gstatic.com/csi");return[b,"?v=3","&s="+(window.__gapi_jstiming__.sn||"gwidget")+"&action=",a.name,f.length?"&it="+f.join(","):"",d,"&rt=",e.join(",")].join("")},jp=function(a,b,c){a=ip(a,b,c);if(!a)return"";b=new Image;var d=window.__gapi_jstiming__.hfa++;
window.__gapi_jstiming__.IP[d]=b;b.onload=b.onerror=function(){window.__gapi_jstiming__&&delete window.__gapi_jstiming__.IP[d]};b.src=a;b=null;return a};window.__gapi_jstiming__.report=function(a,b,c){var d=document.visibilityState,e="visibilitychange";d||(d=document.webkitVisibilityState,e="webkitvisibilitychange");if(d=="prerender"){var f=!1,h=function(){if(!f){b?b.prerender="1":b={prerender:"1"};if((document.visibilityState||document.webkitVisibilityState)=="prerender")var k=!1;else jp(a,b,c),
k=!0;k&&(f=!0,document.removeEventListener(e,h,!1))}};document.addEventListener(e,h,!1);return""}return jp(a,b,c)}};var kp={g:"gapi_global",m:"gapi_module",w:"gwidget"},lp=function(a,b){this.type=a?a=="_p"?"m":"w":"g";this.name=a;this.Hs=b};lp.prototype.key=function(){switch(this.type){case "g":return this.type;case "m":return this.type+"."+this.Hs;case "w":return this.type+"."+this.name+this.Hs}};
var mp=new lp,np=navigator.userAgent.match(/iPhone|iPad|Android|PalmWebOS|Maemo|Bada/),op=_.Ee(Zo,"_c",_.Fe()),pp=Math.random()<(_.Ze("csi/rate")||0),rp=function(a,b,c){for(var d=new lp(b,c),e=_.Ee(op,d.key(),_.Fe()),f=bp[a]||[],h=0;h<f.length;++h){var k=f[h],l=k[0],m=a,n=b,p=c;k=gp(k[1],n,p);m=gp(m,n,p);e[l]=k&&m?m-k:null}ap[a]&&pp&&(qp(mp),qp(d))},sp=function(a,b){b=b||[];for(var c=[],d=0;d<b.length;d++)c.push(a+b[d]);return c},qp=function(a){var b=_.ye.__gapi_jstiming__;b.sn=kp[a.type];var c=new b.Timer(0);
a:{switch(a.type){case "g":var d="global";break a;case "m":d=a.Hs;break a;case "w":d=a.name;break a}d=void 0}c.name=d;d=!1;var e=a.key(),f=op[e];c.tick("_start",null,0);for(var h in f)c.tick(h,"_start",f[h]),d=!0;op[e]=_.Fe();d&&(h=[],h.push("l"+(_.Ze("isPlusUser")?"1":"0")),d="m"+(np?"1":"0"),h.push(d),a.type=="m"?h.push("p"+a.Hs):a.type=="w"&&(e="n"+a.Hs,h.push(e),a.Hs=="0"&&h.push(d+e)),h.push("u"+(_.Ze("isLoggedIn")?"1":"0")),a=sp("",h),a=sp("abc_",a).join(","),b.report(c,{e:a}))};
cp("blt","bs0","bs1");cp("psi","ps0","ps1");cp("rpcqi","rqe","rqd");cp("bsprt","bsrt0","bsrt1");cp("bsrqt","bsrt1","bsrt2");cp("bsrst","bsrt2","bsrt3");cp("mli","ml0","ml1");cp("mei","me0","me1",!0);cp("wcdi","wrs","wcdi");cp("wci","wrs","wdc");cp("wdi","wrs","wrdi");cp("wdt","bs0","wrdt");cp("wri","wrs","wrri",!0);cp("wrt","bs0","wrrt");cp("wji","wje0","wje1",!0);cp("wjli","wjl0","wjl1");cp("whi","wh0","wh1",!0);cp("wai","waaf0","waaf1",!0);cp("wadi","wrs","waaf1",!0);cp("wadt","bs0","waaf1",!0);
cp("wprt","wrt0","wrt1");cp("wrqt","wrt1","wrt2");cp("wrst","wrt2","wrt3",!0);cp("fbprt","fsrt0","fsrt1");cp("fbrqt","fsrt1","fsrt2");cp("fbrst","fsrt2","fsrt3",!0);cp("fdns","fdns0","fdns1");cp("fcon","fcon0","fcon1");cp("freq","freq0","freq1");cp("frsp","frsp0","frsp1");cp("fttfb","fttfb0","fttfb1");cp("ftot","ftot0","ftot1",!0);var tp=Zo.r;if(typeof tp!=="function"){for(var up;up=tp.shift();)rp.apply(null,up);Zo.r=rp};var vp=["div"],wp="onload",xp=!0,yp=!0,zp=function(a){return a},Ap=null,Bp=function(a){var b=_.Ze(a);return typeof b!=="undefined"?b:_.Ze("gwidget/"+a)},Fp,Gp,Hp,Ip,Jp,Kp,Lp,Rp,Mp,Sp,Tp,Up,Vp,Wp,Np,Pp,Xp,Op,Yp,Zp,$p,aq,bq,cq;Ap=_.Ze();_.Ze("gwidget");var Cp=Bp("parsetags");wp=Cp==="explicit"||Cp==="onload"?Cp:wp;var Dp=Bp("google_analytics");typeof Dp!=="undefined"&&(xp=!!Dp);var Ep=Bp("data_layer");typeof Ep!=="undefined"&&(yp=!!Ep);Fp=function(){var a=this&&this.getId();a&&(_.Pe.drw=a)};
Gp=function(){_.Pe.drw=null};Hp=function(a){return function(b){var c=a;typeof b==="number"?c=b:typeof b==="string"&&(c=b.indexOf("px"),c!=-1&&(b=b.substring(0,c)),c=parseInt(b,10));return c}};Ip=function(a){typeof a==="string"&&(a=window[a]);return typeof a==="function"?a:null};Jp=function(){return Bp("lang")||"en-US"};
Kp=function(a){if(!_.Sa.wb("attach")){var b={},c=_.Sa.wb("inline"),d;for(d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);b.open=function(e){var f=e.vc().renderData.id;f=document.getElementById(f);if(!f)throw Error("I");return c.attach(e,f)};_.Sa.Dc("attach",b)}a.style="attach"};Lp=function(){var a={};a.width=[Hp(450)];a.height=[Hp(24)];a.onready=[Ip];a.lang=[Jp,"hl"];a.iloader=[function(){return _.Pe.ILI},"iloader"];return a}();
Rp=function(a){var b={};b.Qe=a[0];b.Pp=-1;b.Ota="___"+b.Qe+"_";b.gia="g:"+b.Qe;b.Wra="g-"+b.Qe;b.wZ=[];b.config={};b.Ey=[];b.Y1={};b.LD={};var c=function(e){for(var f in e)if(_.Ge(e,f)){b.config[f]=[Ip];b.Ey.push(f);var h=e[f],k=null,l=null,m=null;typeof h==="function"?k=h:h&&typeof h==="object"&&(k=h.Lra,l=h.MD,m=h.QN);m&&(b.Ey.push(m),b.config[m]=[Ip],b.Y1[f]=m);k&&(b.config[f]=[k]);l&&(b.LD[f]=l)}},d=function(e){for(var f={},h=0;h<e.length;++h)f[e[h].toLowerCase()]=1;f[b.gia]=1;b.nda=f};a[1]&&
(b.parameters=a[1]);(function(e){b.config=e;for(var f in Lp)Lp.hasOwnProperty(f)&&!b.config.hasOwnProperty(f)&&(b.config[f]=Lp[f])})(a[2]||{});a[3]&&c(a[3]);a[4]&&d(a[4]);a[5]&&(b.Rm=a[5]);b.Hta=a[6]===!0;b.Hea=a[7];b.Tha=a[8];b.nda||d(vp);b.HJ=function(e){b.Pp++;ep("wrs",b.Qe,String(b.Pp));var f=[],h=e.element,k=e.config,l=":"+b.Qe;l==":plus"&&e.Dd&&e.Dd.action&&(l+="_"+e.Dd.action);var m=Mp(b,k),n={};_.He(_.ro(),n);for(var p in e.Dd)e.Dd[p]!=null&&(n[p]=e.Dd[p]);p={container:h.id,renderData:e.cfa,
style:"inline",height:k.height,width:k.width};Kp(p);b.Rm&&(f[2]=p,f[3]=n,f[4]=m,b.Rm("i",f));l=_.Sa.open(l,p,n,m);e=e.K8;Np(l,k);Op(l,h);Pp(b,l,e);Qp(b.Qe,b.Pp.toString(),l);f[5]=l;b.Rm&&b.Rm("e",f)};return b};
Mp=function(a,b){for(var c={},d=a.Ey.length-1;d>=0;--d){var e=a.Ey[d],f=b[a.Y1[e]||e]||b[e],h=b[e];h&&f!==h&&(f=function(l,m){return function(n){m.apply(this,arguments);l.apply(this,arguments)}}(f,h));f&&(c[e]=f)}for(var k in a.LD)a.LD.hasOwnProperty(k)&&(c[k]=Sp(c[k]||function(){},a.LD[k]));c.drefresh=Fp;c.erefresh=Gp;return c};
Sp=function(a,b){return function(c){var d=b(c);if(d){var e=c.href||null;if(xp){if(window._gat)try{var f=window._gat._getTrackerByName("~0");f&&f._getAccount()!="UA-XXXXX-X"?f._trackSocial("Google",d,e):window._gaq&&window._gaq.push(["_trackSocial","Google",d,e])}catch(k){}if(window.ga&&window.ga.getAll)try{var h=window.ga.getAll();for(f=0;f<h.length;f++)h[f].send("social","Google",d,e)}catch(k){}}if(yp&&window.dataLayer)try{window.dataLayer.push({event:"social",socialNetwork:"Google",socialAction:d,
socialTarget:e})}catch(k){}}a.call(this,c)}};Tp=function(a){return _.vn&&a instanceof _.vn};Up=function(a){return Tp(a)?"_renderstart":"renderstart"};Vp=function(a){return Tp(a)?"_ready":"ready"};Wp=function(){return!0};Np=function(a,b){if(b.onready){var c=!1,d=function(){c||(c=!0,b.onready.call(null))};a.register(Vp(a),d,Wp);a.register(Up(a),d,Wp)}};
Pp=function(a,b,c){var d=a.Qe,e=String(a.Pp),f=!1,h=function(){f||(f=!0,b.getIframeEl(),c&&ep("wrdt",d,e),ep("wrdi",d,e))};b.register(Up(b),h,Wp);var k=!1;a=function(){k||(k=!0,h(),c&&ep("wrrt",d,e),ep("wrri",d,e))};b.register(Vp(b),a,Wp);Tp(b)?b.register("widget-interactive-"+b.id,a,Wp):_.$f.register("widget-interactive-"+b.id,a);_.$f.register("widget-csi-tick-"+b.id,function(l,m,n){l==="wdc"?ep("wdc",d,e,n):l==="wje0"?ep("wje0",d,e,n):l==="wje1"?ep("wje1",d,e,n):l=="wh0"?_.dp("wh0",d,e,n):l=="wh1"?
_.dp("wh1",d,e,n):l=="wcdi"&&_.dp("wcdi",d,e,n)})};Xp=function(a){return typeof a=="number"?a+"px":a=="100%"?a:null};Op=function(a,b){var c=function(d){d=d||a;var e=Xp(d.width);e&&b.style.width!=e&&(b.style.width=e);(d=Xp(d.height))&&b.style.height!=d&&(b.style.height=d)};Tp(a)?a.setParam("onRestyle",c):(a.register("ready",c,Wp),a.register("renderstart",c,Wp),a.register("resize",c,Wp))};Yp=function(a,b){for(var c in Lp)if(Lp.hasOwnProperty(c)){var d=Lp[c][1];d&&!b.hasOwnProperty(d)&&(b[d]=a[d])}return b};
Zp=function(a,b){var c={},d;for(d in a)a.hasOwnProperty(d)&&(c[a[d][1]||d]=(a[d]&&a[d][0]||zp)(b[d.toLowerCase()],b,Ap));return c};$p=function(a){if(a=a.Hea)for(var b=0;b<a.length;b++)(new Image).src=a[b]};aq=function(a,b){var c=b.userParams,d=b.siteElement;d||(d=(d=b.iframeNode)&&d.parentNode);if(d&&d.nodeType===1){var e=Zp(a.config,c);a.wZ.push({element:d,config:e,Dd:Yp(e,Zp(a.parameters,c)),Rsa:3,K8:!!c["data-onload"],cfa:b})}b=a.wZ;for(a=a.HJ;b.length>0;)a(b.shift())};
bq=function(a,b){a.Pp++;ep("wrs",a.Qe,String(a.Pp));var c=b.userParams,d=Zp(a.config,c),e=[],f=b.iframeNode,h=b.siteElement,k=Mp(a,d),l=Zp(a.parameters,c);_.He(_.ro(),l);l=Yp(d,l);c=!!c["data-onload"];var m=_.Mm,n=_.Fe();n.renderData=b;n.height=d.height;n.width=d.width;n.id=b.id;n.url=b.url;n.iframeEl=f;n.where=n.container=h;n.apis=["_open"];n.messageHandlers=k;n.messageHandlersFilter=_.Pm;_.bo(n);f=l;a.Rm&&(e[2]=n,e[3]=f,e[4]=k,a.Rm("i",e));k=m.attach(n);k.id=b.id;k.lM(k,n);Np(k,d);Op(k,h);Pp(a,
k,c);Qp(a.Qe,a.Pp.toString(),k);e[5]=k;a.Rm&&a.Rm("e",e)};cq=function(a,b){var c=b.url;a.Tha||_.Si(c)?bq(a,b):_.Sa.open?aq(a,b):(0,_.Eg)("iframes",function(){aq(a,b)})};
_.dq=function(a){var b=Rp(a);$p(b);_.Yf(b.Qe,function(d){cq(b,d)});Mo[b.Qe]=!0;var c={va:function(d,e,f){var h=e||{};h.type=b.Qe;e=h.type;delete h.type;var k=So(d);if(k){d={};for(var l in h)_.Ge(h,l)&&(d[l.toLowerCase()]=h[l]);d.rd=1;(l=!!d.ri)&&delete d.ri;Vo(e,k,d,[],0,l,f)}else _.Vf.log("gapi."+e+".render: missing element "+typeof d==="string"?d:"")},go:function(d){Wo(d,b.Qe)},Tsa:function(){var d=_.Ee(_.Pe,"WI",_.Fe()),e;for(e in d)delete d[e]}};a=function(){wp==="onload"&&c.go()};if(!mo(b.Qe)){if(!_.Wf())try{a()}catch(d){}_.Xf(a)}_.r("gapi."+
b.Qe+".go",c.go);_.r("gapi."+b.Qe+".render",c.va);return c};var eq=function(){var a=window;return!!a.performance&&!!a.performance.getEntries},Qp=function(a,b,c){if(eq()){var d=function(){var f=!1;return function(){if(f)return!0;f=!0;return!1}}(),e=function(){d()||window.setTimeout(function(){var f=c.getIframeEl().src;var h=f.indexOf("#");h!=-1&&(f=f.substring(0,h));f=window.performance.getEntriesByName(f);f.length<1?f=null:(f=f[0],f=f.responseStart==0?null:f);if(f){h=Math.round(f.requestStart);var k=Math.round(f.responseStart),l=Math.round(f.responseEnd);
ep("wrt0",a,b,Math.round(f.startTime));ep("wrt1",a,b,h);ep("wrt2",a,b,k);ep("wrt3",a,b,l)}},1E3)};c.register(Up(c),e,Wp);c.register(Vp(c),e,Wp)}};_.r("gapi.widget.make",_.dq);
_.cf=_.cf||{};_.cf.Kv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.cf.sB=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
_.cf=_.cf||{};_.cf.E7=function(a){var b=window;typeof b.addEventListener!="undefined"?b.addEventListener("mousemove",a,!1):typeof b.attachEvent!="undefined"?b.attachEvent("onmousemove",a):_.df("cannot attachBrowserEvent: mousemove")};_.cf.afa=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.df("cannot removeBrowserEvent: mousemove")};
_.cf=_.cf||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.cf.escape=function(c,d){if(c){if(typeof c==="string")return _.cf.dG(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.cf.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.cf.dG(e)]=_.cf.escape(c[e],!0));return d}}return c};_.cf.dG=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.cf.Ita=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.Ng=function(){function a(m){var n=new _.Mg;n.Bx(m);return n.Ti()}var b=window.crypto;if(b&&typeof b.getRandomValues=="function")return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Ze("random/maxObserveMousemove");c==null&&(c=-1);var d=0,e=Math.random(),f=1,h=(screen.width*screen.width+screen.height)*1E6,k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;c>0&&++d==c&&_.cf.afa(k)};
c!=0&&_.cf.E7(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+1.2089258196146292E24)}}();_.r("shindig.random",_.Ng);
_.Sa.Ma={};_.Sa.Ma.Mi={};_.Sa.Ma.Mi.W7=function(a){try{return!!a.document}catch(b){}return!1};_.Sa.Ma.Mi.fU=function(a){var b=a.parent;return a!=b&&_.Sa.Ma.Mi.W7(b)?_.Sa.Ma.Mi.fU(b):a};_.Sa.Ma.Mi.Mra=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Sa.Ma.Mi.Kv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var lq,mq,nq,oq,rq,sq,tq,uq,vq,wq,xq,yq,zq;lq=function(){_.$f.register("_noop_echo",function(){this.callback(_.Sa.R$(_.Sa.wm[this.f]))})};mq=function(){window.setTimeout(function(){_.$f.call("..","_noop_echo",_.Sa.rea)},0)};nq=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.$f.call.apply(_.$f,f)};d._iframe_wrapped_rpc_=!0;return d};
oq=function(a){_.Sa.jC[a]||(_.Sa.jC[a]={},_.$f.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Sa.jC[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Sa.Sq,a)&&(h=_.Sa.Sq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Vf.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Sa.jC[a]};_.pq=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=b.length;for(var e=0;e<d;++e){var f=b[e].indexOf("=");if(f!==-1){var h=b[e].substring(0,f);f=b[e].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.qq=function(){return _.ye.location.origin||_.ye.location.protocol+"//"+_.ye.location.host};
rq=function(a){_.Pe.h=a};sq=function(a){_.Pe.bsh=a};tq=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};uq=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
vq=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!uq(a[d])&&!uq(b[d])?vq(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=uq(b[d])?[]:{},vq(a[d],b[d])):a[d]=b[d])};
wq=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
xq=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
yq=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=tq("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&wq())if(e=xq(c),d.push(25),typeof e===
"object")return e;return{}}};zq=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());vq(c,b);a.push(c)};
_.Aq=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.$h(!0);d=window.___gcfg;b=tq("cu");a=window.___gu;d&&d!==a&&(zq(b,d),window.___gu=d);d=tq("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,tq("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=yq(f,h))&&d.push(f));c&&zq(b,c);a=tq("cd");c=0;for(d=a.length;c<d;++c)vq(_.$h(),a[c],!0);a=tq("ci");c=0;for(d=a.length;c<d;++c)vq(_.$h(),a[c],!0);c=0;for(d=b.length;c<d;++c)vq(_.$h(),b[c],!0)};var Bq,Cq=window.location.href,Dq=Cq.indexOf("?"),Eq=Cq.indexOf("#");
Bq=(Eq===-1?Cq.substr(Dq+1):[Cq.substr(Dq+1,Eq-Dq-1),"&",Cq.substr(Eq+1)].join("")).split("&");for(var Fq=window.decodeURIComponent?decodeURIComponent:unescape,Gq=0,Hq=Bq.length;Gq<Hq;++Gq){var Iq=Bq[Gq].indexOf("=");if(Iq!==-1){Bq[Gq].substring(0,Iq);var Jq=Bq[Gq].substring(Iq+1);Jq=Jq.replace(/\+/g," ");try{Fq(Jq)}catch(a){}}};if(window.ToolbarApi)Kq=window.ToolbarApi,Kq.La=window.ToolbarApi.getInstance,Kq.prototype=window.ToolbarApi.prototype,_.g=Kq.prototype,_.g.openWindow=Kq.prototype.openWindow,_.g.qQ=Kq.prototype.closeWindow,_.g.J_=Kq.prototype.setOnCloseHandler,_.g.ZP=Kq.prototype.canClosePopup,_.g.GZ=Kq.prototype.resizeWindow;else{var Kq=function(){};Kq.La=function(){!Lq&&window.external&&window.external.GTB_IsToolbar&&(Lq=new Kq);return Lq};_.g=Kq.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.qQ=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.J_=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.ZP=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.GZ=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var Lq=null;window.ToolbarApi=Kq;window.ToolbarApi.getInstance=Kq.La};var Mq=/^[-_.0-9A-Za-z]+$/,Nq={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},Oq={onBeforeParentOpen:"beforeparentopen"},Pq={onOpen:function(a){var b=a.vc();a.Zg(b.container||b.element);return a},onClose:function(a){a.remove()}},Qq=function(){_.Sa.mV++;return["I",_.Sa.mV,"_",(new Date).getTime()].join("")},Rq,Sq,Tq,Wq,Xq,Yq,Zq,ar,$q;_.Sa.Xn=function(a){var b=_.Fe();_.He(_.Wl,b);_.He(a,b);return b};
Rq=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Rf(a):a};Sq=function(a){var b=_.ai("googleapis.config/elog");if(b)try{b(a)}catch(c){}};Tq=function(a){a&&a.match(Mq)&&_.Aq("googleapis.config/gcv",a)};_.Uq=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.Vq=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=oq(h);p[k]=p[k]||{};n=_.Sa.Ma.Mi.Kv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Sa.Sq)_.Sa.Sq.hasOwnProperty(q)&&f.push(q);return f.join(",")};Wq=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=nq(f,b,c)}}return d};
Xq=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.go&&_.go._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};Yq=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
Zq=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.ai(a);a={};_.He(b,a);(b=a.url)&&(a.url=_.hm(b));a.params||(a.params={});return a}return{url:_.hm(a)}};ar=function(a){function b(){}b.prototype=$q.prototype;a.prototype=new b};
$q=function(a,b,c,d,e,f,h,k){this.config=Zq(a);this.openParams=this.MB=b||{};this.params=c||{};this.methods=d;this.AD=!1;br(this,b.style);this.callbacks={};cr(this,function(){var l;(l=this.MB.style)&&_.Sa.Ww[l]?l=_.Sa.Ww[l]:l?(_.Vf.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=Pq;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Sa.Ma.Mi.Kv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&dr(this,e[q],_.Sa.Ma.Mi.Kv(m,l))}f&&dr(this,"close",f)});this.Gk=this.ac=h;this.NJ=(k||[]).slice();h&&this.NJ.unshift(h.getId())};$q.prototype.vc=function(){return this.MB};$q.prototype.aH=function(){return this.params};$q.prototype.dA=function(){return this.methods};$q.prototype.kd=function(){return this.Gk};
var br=function(a,b){a.AD||((b=b&&!_.Sa.Ww[b]&&_.Sa.PF[b])?(a.OF=[],b(function(){a.AD=!0;for(var c=a.OF.length,d=0;d<c;++d)a.OF[d].call(a)})):a.AD=!0)},cr=function(a,b){a.AD?b.call(a):a.OF.push(b)};$q.prototype.ye=function(a,b){cr(this,function(){dr(this,a,b)})};var dr=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};$q.prototype.hp=function(a,b){cr(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
$q.prototype.ei=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Vf.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),Sq(k)}return h};var er=function(a){return typeof a=="number"?{value:a,vG:a+"px"}:a=="100%"?{value:100,vG:"100%",dW:!0}:null};$q.prototype.send=function(a,b,c){_.Sa.f_(this,a,b,c)};
$q.prototype.register=function(a,b){var c=this;c.ye(a,function(d){b.call(c,d)})};var fr=function(a,b,c,d,e,f,h){var k=this;$q.call(this,a,b,c,d,Nq,e,f,h);this.id=b.id||Qq();this.ww=b.rpctoken&&String(b.rpctoken)||Math.round(_.Fi()*1E9);this.Gba=Yq(this.params,this.config);this.jG={};cr(this,function(){k.ei("open");_.Uq(k.jG,k)})};ar(fr);_.g=fr.prototype;
_.g.Zg=function(a,b){if(!this.config.url)return _.Vf.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Sa.wm[c]=this;var d=_.Uq(this.methods);d._ready=this.LB;d._close=this.close;d._open=this.dY;d._resizeMe=this.HZ;d._renderstart=this.XX;var e=this.Gba;this.ww&&(e.rpctoken=this.ww);e._methods=_.Vq(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Vf.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.vc().allowPost&&(d.allowPost=!0);this.vc().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=Rq;this.ii=_.jm(this.config.url,a,d);a=this.ii.getAttribute("data-postorigin")||this.ii.src;_.Sa.wm[c]=this;
_.$f.UC(this.id,this.ww);_.$f.Pj(this.id,a);return this};_.g.Oh=function(a,b){this.jG[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.ii};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.LB=function(a){var b=Wq(a,this.id,"");this.Gk&&typeof this.methods._ready=="function"&&(a._methods=_.Vq(b,this.Gk.getId(),this.id,this,!1),this.methods._ready(a));_.Uq(a,this);_.Uq(b,this);this.ei("ready",a)};
_.g.XX=function(a){this.ei("renderstart",a)};_.g.close=function(a){a=this.ei("close",a);delete _.Sa.wm[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.dY=function(a){var b=Wq(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(Xq(a.openParams))new gr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new fr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;cr(c,function(){var e={childId:c.getId()},f=c.jG;f._toclose=c.close;e._methods=_.Vq(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.HZ=function(a){if(this.ei("resize",a)===void 0&&this.ii){var b=er(a.width);b!=null&&(this.ii.style.width=b.vG);a=er(a.height);a!=null&&(this.ii.style.height=a.vG);this.ii.parentElement&&(b!=null&&b.dW||a!=null&&a.dW)&&(this.ii.parentElement.style.display="block")}};
var gr=function(a,b,c,d,e,f,h){var k=this;$q.call(this,a,b,c,d,Oq,e,f,h);this.url=a;this.Ip=null;this.hK=Qq();cr(this,function(){k.ei("beforeparentopen");var l=_.Uq(k.methods);l._onopen=k.jea;l._ready=k.LB;l._onclose=k.hea;k.params._methods=_.Vq(l,"..",k.hK,k,!0);l={};for(var m in k.params)l[m]=Rq(k.params[m]);_.go._open({url:k.config.url,openParams:k.MB,params:l,proxyId:k.hK,openedByProxyChain:k.NJ})})};ar(gr);gr.prototype.caa=function(){return this.Ip};
gr.prototype.jea=function(a){this.Ip=a.childId;var b=Wq(a,"..",this.Ip);_.Uq(b,this);this.close=b._toclose;_.Sa.wm[this.Ip]=this;this.Gk&&this.methods._onopen&&(a._methods=_.Vq(b,this.Gk.getId(),this.Ip,this,!1),this.methods._onopen(a))};gr.prototype.LB=function(a){var b=String(this.Ip),c=Wq(a,"..",b);_.Uq(a,this);_.Uq(c,this);this.ei("ready",a);this.Gk&&this.methods._ready&&(a._methods=_.Vq(c,this.Gk.getId(),b,this,!1),this.methods._ready(a))};
gr.prototype.hea=function(a){if(this.Gk&&this.methods._onclose)this.methods._onclose(a);else return a=this.ei("close",a),delete _.Sa.wm[this.Ip],a};
var hr=function(a,b,c,d,e,f,h){$q.call(this,a,b,c,d,Oq,f,h);this.id=b.id||Qq();this.Gha=e;d._close=this.close;this.onClosed=this.QX;this.s2=0;cr(this,function(){this.ei("beforeparentopen");var k=_.Uq(this.methods);this.params._methods=_.Vq(k,"..",this.hK,this,!0);k={};k.queryParams=this.params;a=_.am(_.Be,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.ZP(l))};e.J_(l,this);this.s2=l})};ar(hr);
hr.prototype.close=function(a){a=this.ei("close",a);this.Gha.qQ(this.s2);return a};hr.prototype.QX=function(){this.ei("close")};_.go.send=function(a,b,c){_.Sa.f_(_.go,a,b,c)};
(function(){function a(h){return _.Sa.Ww[h]}function b(h,k){_.Sa.Ww[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.lm());var k=window&&Kq&&Kq.La();k?k.GZ(h.width||0,h.height||0):_.go&&_.go._resizeMe&&_.go._resizeMe(h)}function d(h){Tq(h)}_.Sa.wm={};_.Sa.Ww={};_.Sa.PF={};_.Sa.mV=0;_.Sa.jC={};_.Sa.Sq={};_.Sa.WB=null;_.Sa.VB=[];_.Sa.rea=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.go.id)}}catch(m){}try{_.Sa.WB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Sa.VB.length;++h)_.Sa.VB[h](_.Sa.WB);_.Sa.VB=[]}catch(m){Sq(m)}};_.Sa.R$=function(h){var k=h&&h.Gk,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.NJ);return l};lq();if(window.parent!=window){var e=_.pq();e.gcv&&Tq(e.gcv);var f=e.jsh;f&&rq(f);_.Uq(Wq(e,"..",""),_.go);_.Uq(e,_.go);mq()}_.Sa.wb=a;_.Sa.Dc=b;_.Sa.Cga=d;_.Sa.resize=c;_.Sa.p$=function(h){return _.Sa.PF[h]};_.Sa.sL=function(h,
k){_.Sa.PF[h]=k};_.Sa.FZ=c;_.Sa.Xga=d;_.Sa.BA={};_.Sa.BA.get=a;_.Sa.BA.set=b;_.Sa.allow=function(h,k){oq(h);_.Sa.Sq[h]=k||window[h]};_.Sa.Oqa=function(h){delete _.Sa.Sq[h]};_.Sa.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&Kq?Kq.La():null;return q?new hr(h,k,l,m,q,n,p):Xq(k)?new gr(h,k,l,m,n,p):new fr(h,k,l,m,n,p)};_.Sa.close=function(h,k){_.go&&_.go._close&&_.go._close(h,k)};_.Sa.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.lm());m._methods=_.Vq(k||{},"..","",_.go,!0);_.go&&_.go._ready&&_.go._ready(m,l)};_.Sa.QT=function(h){_.Sa.WB?h(_.Sa.WB):_.Sa.VB.push(h)};_.Sa.lea=function(h){return!!_.Sa.wm[h]};_.Sa.y$=function(){return["https://ssl.gstatic.com/gb/js/",_.ai("googleapis.config/gcv")].join("")};_.Sa.YY=function(h){var k={mouseover:1,mouseout:1};if(_.go._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.go._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Sa.f_=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Sa.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Sa.V7=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Sa.QT(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.$f.call.apply(_.$f,m))})};_.Sa.Wea=function(h,k){_.$f.register(h,k)};_.Sa.Jga=rq;_.Sa.n_=
sq;_.Sa.SW=Sq;_.Sa.nV=_.go})();_.r("iframes.allow",_.Sa.allow);_.r("iframes.callSiblingOpener",_.Sa.V7);_.r("iframes.registerForOpenedSibling",_.Sa.Wea);_.r("iframes.close",_.Sa.close);_.r("iframes.getGoogleConnectJsUri",_.Sa.y$);_.r("iframes.getHandler",_.Sa.wb);_.r("iframes.getDeferredHandler",_.Sa.p$);_.r("iframes.getParentInfo",_.Sa.QT);_.r("iframes.iframer",_.Sa.nV);_.r("iframes.open",_.Sa.open);_.r("iframes.openedId_",_.Sa.lea);_.r("iframes.propagate",_.Sa.YY);_.r("iframes.ready",_.Sa.ready);_.r("iframes.resize",_.Sa.resize);
_.r("iframes.setGoogleConnectJsVersion",_.Sa.Cga);_.r("iframes.setBootstrapHint",_.Sa.n_);_.r("iframes.setJsHint",_.Sa.Jga);_.r("iframes.setHandler",_.Sa.Dc);_.r("iframes.setDeferredHandler",_.Sa.sL);_.r("IframeBase",$q);_.r("IframeBase.prototype.addCallback",$q.prototype.ye);_.r("IframeBase.prototype.getMethods",$q.prototype.dA);_.r("IframeBase.prototype.getOpenerIframe",$q.prototype.kd);_.r("IframeBase.prototype.getOpenParams",$q.prototype.vc);_.r("IframeBase.prototype.getParams",$q.prototype.aH);
_.r("IframeBase.prototype.removeCallback",$q.prototype.hp);_.r("Iframe",fr);_.r("Iframe.prototype.close",fr.prototype.close);_.r("Iframe.prototype.exposeMethod",fr.prototype.Oh);_.r("Iframe.prototype.getId",fr.prototype.getId);_.r("Iframe.prototype.getIframeEl",fr.prototype.getIframeEl);_.r("Iframe.prototype.getSiteEl",fr.prototype.getSiteEl);_.r("Iframe.prototype.openInto",fr.prototype.Zg);_.r("Iframe.prototype.remove",fr.prototype.remove);_.r("Iframe.prototype.setSiteEl",fr.prototype.setSiteEl);
_.r("Iframe.prototype.addCallback",fr.prototype.ye);_.r("Iframe.prototype.getMethods",fr.prototype.dA);_.r("Iframe.prototype.getOpenerIframe",fr.prototype.kd);_.r("Iframe.prototype.getOpenParams",fr.prototype.vc);_.r("Iframe.prototype.getParams",fr.prototype.aH);_.r("Iframe.prototype.removeCallback",fr.prototype.hp);_.r("IframeProxy",gr);_.r("IframeProxy.prototype.getTargetIframeId",gr.prototype.caa);_.r("IframeProxy.prototype.addCallback",gr.prototype.ye);_.r("IframeProxy.prototype.getMethods",gr.prototype.dA);
_.r("IframeProxy.prototype.getOpenerIframe",gr.prototype.kd);_.r("IframeProxy.prototype.getOpenParams",gr.prototype.vc);_.r("IframeProxy.prototype.getParams",gr.prototype.aH);_.r("IframeProxy.prototype.removeCallback",gr.prototype.hp);_.r("IframeWindow",hr);_.r("IframeWindow.prototype.close",hr.prototype.close);_.r("IframeWindow.prototype.onClosed",hr.prototype.QX);_.r("iframes.util.getTopMostAccessibleWindow",_.Sa.Ma.Mi.fU);_.r("iframes.handlers.get",_.Sa.BA.get);_.r("iframes.handlers.set",_.Sa.BA.set);
_.r("iframes.resizeMe",_.Sa.FZ);_.r("iframes.setVersionOverride",_.Sa.Xga);_.r("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Sa.CROSS_ORIGIN_IFRAMES_FILTER);_.r("IframeBase.prototype.send",$q.prototype.send);_.r("IframeBase.prototype.register",$q.prototype.register);_.r("Iframe.prototype.send",fr.prototype.send);_.r("Iframe.prototype.register",fr.prototype.register);_.r("IframeProxy.prototype.send",gr.prototype.send);_.r("IframeProxy.prototype.register",gr.prototype.register);
_.r("IframeWindow.prototype.send",hr.prototype.send);_.r("IframeWindow.prototype.register",hr.prototype.register);_.r("iframes.iframer.send",_.Sa.nV.send);
var lt=_.Sa.Dc,mt={open:function(a){var b=_.Yn(a.vc());return a.Zg(b,{style:_.Zn(b)})},attach:function(a,b){var c=_.Yn(a.vc()),d=b.id,e=b.getAttribute("data-postorigin")||b.src,f=/#(?:.*&)?rpctoken=(\d+)/.exec(e);f=f&&f[1];a.id=d;a.ww=f;a.el=c;a.ii=b;_.Sa.wm[d]=a;b=_.Uq(a.methods);b._ready=a.LB;b._close=a.close;b._open=a.dY;b._resizeMe=a.HZ;b._renderstart=a.XX;_.Vq(b,d,"",a,!0);_.$f.UC(a.id,a.ww);_.$f.Pj(a.id,e);c=_.Sa.Xn({style:_.Zn(c)});for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&
(h=="style"?a.ii.style.cssText=c[h]:a.ii.setAttribute(h,c[h]))}};mt.onready=_.$n;mt.onRenderStart=_.$n;mt.close=_.ao;lt("inline",mt);
_.Ah=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.od(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Bh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.ub(f)?"o"+_.rh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Ch=function(a){for(var b in a)return!1;return!0};
_.Dh=function(a,b){a.src=_.lc(b);(b=_.Jc("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Eh=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Fh,Gh,Ih;Fh={};Gh=null;_.Hh=_.Ed||_.Fd||!_.zh&&typeof _.Xa.atob=="function";_.Jh=function(a,b){b===void 0&&(b=0);Ih();b=Fh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Kh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Gh[m];if(n!=null)return n;if(!_.Cc(m))throw Error("w`"+m);}return l}Ih();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Ih=function(){if(!Gh){Gh={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Fh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Gh[f]===void 0&&(Gh[f]=e)}}}};
var gi;_.fi=function(a){this.zc=a||{cookie:""}};_.g=_.fi.prototype;_.g.isEnabled=function(){if(!_.Xa.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{aJ:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.g.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.ata;d=c.secure||!1;var f=c.domain||void 0;var h=c.path||void 0;var k=c.aJ}if(/[;=\s]/.test(a))throw Error("z`"+a);if(/[;\r\n]/.test(b))throw Error("A`"+b);k===void 0&&(k=-1);this.zc.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(k<0?"":k==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+k*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.g.get=function(a,b){for(var c=a+"=",d=(this.zc.cookie||"").split(";"),e=0,f;e<d.length;e++){f=_.Ec(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.Hl(a);this.set(a,"",{aJ:0,path:b,domain:c});return d};_.g.ig=function(){return gi(this).keys};_.g.Ye=function(){return gi(this).values};_.g.isEmpty=function(){return!this.zc.cookie};_.g.Yb=function(){return this.zc.cookie?(this.zc.cookie||"").split(";").length:0};
_.g.Hl=function(a){return this.get(a)!==void 0};_.g.clear=function(){for(var a=gi(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};gi=function(a){a=(a.zc.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=_.Ec(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.hi=new _.fi(typeof document=="undefined"?null:document);
_.pi={};_.qi=function(a){return _.pi[a||"token"]||null};
_.Ti=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.Ui=function(){this.Jg=this.Jg;this.So=this.So};_.Ui.prototype.Jg=!1;_.Ui.prototype.isDisposed=function(){return this.Jg};_.Ui.prototype.dispose=function(){this.Jg||(this.Jg=!0,this.ua())};_.Ui.prototype[Symbol.dispose]=function(){this.dispose()};_.Wi=function(a,b){_.Vi(a,_.bb(_.Ti,b))};_.Vi=function(a,b){a.Jg?b():(a.So||(a.So=[]),a.So.push(b))};_.Ui.prototype.ua=function(){if(this.So)for(;this.So.length;)this.So.shift()()};
var dj;dj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.ej=function(a){this.src=a;this.je={};this.wx=0};_.gj=function(a,b){this.type="function"==typeof _.fj&&a instanceof _.fj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.gw=!1};_.gj.prototype.stopPropagation=function(){this.gw=!0};_.gj.prototype.preventDefault=function(){this.defaultPrevented=!0};_.hj=function(a,b){_.gj.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.aK=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Af=null;a&&this.init(a,b)};_.cb(_.hj,_.gj);
_.hj.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.Fd||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.Fd||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.aK=_.Hd?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.Af=a;a.defaultPrevented&&_.hj.N.preventDefault.call(this)};_.hj.prototype.stopPropagation=function(){_.hj.N.stopPropagation.call(this);this.Af.stopPropagation?this.Af.stopPropagation():this.Af.cancelBubble=!0};_.hj.prototype.preventDefault=function(){_.hj.N.preventDefault.call(this);var a=this.Af;a.preventDefault?a.preventDefault():a.returnValue=!1};_.ij="closure_listenable_"+(Math.random()*1E6|0);_.jj=function(a){return!(!a||!a[_.ij])};var kj=0;var lj=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Gf=e;this.key=++kj;this.mw=this.Cy=!1},mj=function(a){a.mw=!0;a.listener=null;a.proxy=null;a.src=null;a.Gf=null};_.ej.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.je[f];a||(a=this.je[f]=[],this.wx++);var h=nj(a,b,d,e);h>-1?(b=a[h],c||(b.Cy=!1)):(b=new lj(b,this.src,f,!!d,e),b.Cy=c,a.push(b));return b};_.ej.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.je))return!1;var e=this.je[a];b=nj(e,b,c,d);return b>-1?(mj(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.je[a],this.wx--),!0):!1};
_.oj=function(a,b){var c=b.type;if(!(c in a.je))return!1;var d=_.Xi(a.je[c],b);d&&(mj(b),a.je[c].length==0&&(delete a.je[c],a.wx--));return d};_.ej.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.je)if(!a||c==a){for(var d=this.je[c],e=0;e<d.length;e++)++b,mj(d[e]);delete this.je[c];this.wx--}return b};_.ej.prototype.Jq=function(a,b,c,d){a=this.je[a.toString()];var e=-1;a&&(e=nj(a,b,c,d));return e>-1?a[e]:null};
_.ej.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return dj(this.je,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var nj=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.mw&&f.listener==b&&f.capture==!!c&&f.Gf==d)return e}return-1};var pj,qj,rj,vj,xj,yj,zj,Bj;pj="closure_lm_"+(Math.random()*1E6|0);qj={};rj=0;_.tj=function(a,b,c,d,e){if(d&&d.once)return _.sj(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.tj(a,b[f],c,d,e);return null}c=_.uj(c);return _.jj(a)?a.ta(b,c,_.ub(d)?!!d.capture:!!d,e):vj(a,b,c,!1,d,e)};
vj=function(a,b,c,d,e,f){if(!b)throw Error("B");var h=_.ub(e)?!!e.capture:!!e,k=_.wj(a);k||(a[pj]=k=new _.ej(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=xj();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)_.ri||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(yj(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("C");rj++;return c};
xj=function(){var a=zj,b=function(c){return a.call(b.src,b.listener,c)};return b};_.sj=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.sj(a,b[f],c,d,e);return null}c=_.uj(c);return _.jj(a)?a.zr(b,c,_.ub(d)?!!d.capture:!!d,e):vj(a,b,c,!0,d,e)};
_.Aj=function(a){if(typeof a==="number"||!a||a.mw)return!1;var b=a.src;if(_.jj(b))return b.GN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(yj(c),d):b.addListener&&b.removeListener&&b.removeListener(d);rj--;(c=_.wj(b))?(_.oj(c,a),c.wx==0&&(c.src=null,b[pj]=null)):mj(a);return!0};yj=function(a){return a in qj?qj[a]:qj[a]="on"+a};
zj=function(a,b){if(a.mw)a=!0;else{b=new _.hj(b,this);var c=a.listener,d=a.Gf||a.src;a.Cy&&_.Aj(a);a=c.call(d,b)}return a};_.wj=function(a){a=a[pj];return a instanceof _.ej?a:null};Bj="__closure_events_fn_"+(Math.random()*1E9>>>0);_.uj=function(a){if(typeof a==="function")return a;a[Bj]||(a[Bj]=function(b){return a.handleEvent(b)});return a[Bj]};_.cj(function(a){zj=a(zj)});
_.Cj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Yd.prototype.O=_.lb(1,function(a){return _.ae(this.zc,a)});_.Dj=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Dj(a,b[f],c,d,e);else d=_.ub(d)?!!d.capture:!!d,c=_.uj(c),_.jj(a)?a.yc(b,c,d,e):a&&(a=_.wj(a))&&(b=a.Jq(b,c,d,e))&&_.Aj(b)};_.Ej=function(){_.Ui.call(this);this.nk=new _.ej(this);this.q7=this;this.SJ=null};_.cb(_.Ej,_.Ui);_.Ej.prototype[_.ij]=!0;_.g=_.Ej.prototype;_.g.bo=function(){return this.SJ};
_.g.hD=function(a){this.SJ=a};_.g.addEventListener=function(a,b,c,d){_.tj(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.Dj(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.bo();if(c)for(b=[];c;c=c.bo())b.push(c);c=this.q7;var d=a.type||a;if(typeof a==="string")a=new _.gj(a,c);else if(a instanceof _.gj)a.target=a.target||c;else{var e=a;a=new _.gj(d,c);_.Zi(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.gw&&f>=0;f--){var h=a.currentTarget=b[f];e=h.ou(d,!0,a)&&e}a.gw||(h=a.currentTarget=c,e=h.ou(d,!0,a)&&e,a.gw||(e=h.ou(d,!1,a)&&e));if(b)for(f=0;!a.gw&&f<b.length;f++)h=a.currentTarget=b[f],e=h.ou(d,!1,a)&&e;return e};
_.g.ua=function(){_.Ej.N.ua.call(this);this.wK();this.SJ=null};_.g.ta=function(a,b,c,d){return this.nk.add(String(a),b,!1,c,d)};_.g.zr=function(a,b,c,d){return this.nk.add(String(a),b,!0,c,d)};_.g.yc=function(a,b,c,d){return this.nk.remove(String(a),b,c,d)};_.g.GN=function(a){return _.oj(this.nk,a)};_.g.wK=function(){this.nk&&this.nk.removeAll(void 0)};
_.g.ou=function(a,b,c){a=this.nk.je[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.mw&&f.capture==b){var h=f.listener,k=f.Gf||f.src;f.Cy&&this.GN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Jq=function(a,b,c,d){return this.nk.Jq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.nk.hasListener(a!==void 0?String(a):void 0,b)};
var ir;ir=function(){var a=_.Lc();if(_.Tc())return _.$c(a);a=_.Pc(a);var b=_.Zc(a);return _.Rc()?b(["Version","Opera"]):_.Uc()?b(["Edge"]):_.Vc()?b(["Edg"]):_.Oc("Silk")?b(["Silk"]):_.Yc()?b(["Chrome","CriOS","HeadlessChrome"]):(a=a[2])&&a[1]||""};_.jr=function(a){return _.Gc(ir(),a)>=0};_.lr=function(){return _.Sb&&_.Mc?_.Mc.mobile:!_.kr()&&(_.Oc("iPod")||_.Oc("iPhone")||_.Oc("Android")||_.Oc("IEMobile"))};
_.kr=function(){return _.Sb&&_.Mc?!_.Mc.mobile&&(_.Oc("iPad")||_.Oc("Android")||_.Oc("Silk")):_.Oc("iPad")||_.Oc("Android")&&!_.Oc("Mobile")||_.Oc("Silk")};_.mr=function(){return!_.lr()&&!_.kr()};
var ws;ws=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.xs=function(a,b,c,d){return Array.prototype.splice.apply(a,ws(arguments,1))};_.ys=function(a,b,c){if(a!==null&&b in a)throw Error("h`"+b);a[b]=c};_.zs=function(a,b){var c=b||document;c.getElementsByClassName?a=c.getElementsByClassName(a)[0]:(c=document,a=a?(b||c).querySelector(a?"."+a:""):_.be(c,"*",a,b)[0]||null);return a||null};
_.As=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.Bs=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.Es=function(a){_.Ui.call(this);this.mg=a;this.lc={}};_.cb(_.Es,_.Ui);var Fs=[];_.Es.prototype.ta=function(a,b,c,d){return this.Gv(a,b,c,d)};
_.Es.prototype.Gv=function(a,b,c,d,e){Array.isArray(b)||(b&&(Fs[0]=b.toString()),b=Fs);for(var f=0;f<b.length;f++){var h=_.tj(a,b[f],c||this.handleEvent,d||!1,e||this.mg||this);if(!h)break;this.lc[h.key]=h}return this};_.Es.prototype.zr=function(a,b,c,d){return Gs(this,a,b,c,d)};var Gs=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)Gs(a,b,c[h],d,e,f);else{b=_.sj(b,c,d||a.handleEvent,e,f||a.mg||a);if(!b)return a;a.lc[b.key]=b}return a};
_.Es.prototype.yc=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.yc(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.ub(d)?!!d.capture:!!d,e=e||this.mg||this,c=_.uj(c),d=!!d,b=_.jj(a)?a.Jq(b,c,d,e):a?(a=_.wj(a))?a.Jq(b,c,d,e):null:null,b&&(_.Aj(b),delete this.lc[b.key]);return this};_.Es.prototype.removeAll=function(){_.Zb(this.lc,function(a,b){this.lc.hasOwnProperty(b)&&_.Aj(a)},this);this.lc={}};_.Es.prototype.ua=function(){_.Es.N.ua.call(this);this.removeAll()};
_.Es.prototype.handleEvent=function(){throw Error("K");};
var qu,ru,su,tu,uu,wu,xu,yu,zu,Bu;_.ou=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};_.pu=!1;qu=function(a){try{_.pu&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};ru=function(a){try{window.console&&window.console.warn&&window.console.warn(a)}catch(b){}};su=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
tu=function(a,b){function c(){}if(!a)throw Error("N");if(!b)throw Error("O");c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};uu=function(a){return Object.prototype.toString.call(a)==="[object Function]"};_.vu=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};wu=function(a){var b=location.hash;a=new RegExp("[&#]"+a+"=([^&]*)");b=decodeURIComponent(b);b=a.exec(b);return b==null?"":b[1].replace(/\+/g," ")};
xu=function(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else throw Error("P`"+b);};yu={token:1,id_token:1};zu=function(){var a=navigator.userAgent.toLowerCase();return a.indexOf("msie")!=-1&&parseInt(a.split("msie")[1],10)==8};_.Au=window.JSON;Bu=function(a){this.SN=a||[];this.mc={}};
Bu.prototype.addEventListener=function(a,b){if(!(su(this.SN,a)>=0))throw Error("R`"+a);if(!uu(b))throw Error("S`"+a);this.mc[a]||(this.mc[a]=[]);su(this.mc[a],b)<0&&this.mc[a].push(b)};Bu.prototype.removeEventListener=function(a,b){if(!(su(this.SN,a)>=0))throw Error("R`"+a);uu(b)&&this.mc[a]&&this.mc[a].length&&(b=su(this.mc[a],b),b>=0&&this.mc[a].splice(b,1))};
Bu.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&su(this.SN,b)>=0))throw Error("T`"+b);if(this.mc[b]&&this.mc[b].length)for(var c=this.mc[b].length,d=0;d<c;d++)this.mc[b][d](a)};var Cu,Du,Fu,Ju,Ku,av,bv,dv,ev,gv,kv,lv,mv,qv;Cu={};Du={};_.Eu=function(){if(_.id()&&!_.jr("118"))return!1;var a=_.Yc()&&!_.Vc()&&!_.Wc(),b=_.dd()||_.mr();return"IdentityCredential"in window&&a&&b&&_.jr("132")&&(_.mr()||_.dd())};Fu={google:{fedcmConfigUrl:"https://accounts.google.com/o/fedcm/config.json",authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};_.Gu=function(a,b){if(a=Fu[a])return a[b]};
_.Hu=function(a,b){if(!a)throw Error("U");if(!b.authServerUrl)throw Error("V");if(!b.idpIFrameUrl)throw Error("W");Fu[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl};b.fedcmConfigUrl?Fu[a].fedcmConfigUrl=b.fedcmConfigUrl:a==="google"&&(Fu[a].fedcmConfigUrl="https://accounts.google.com/o/fedcm/config.json")};_.Iu=void 0;
Ju=function(a){a.style.position="absolute";a.style.width="1px";a.style.height="1px";a.style.left="-9999px";a.style.top="-9999px";a.style.right="-9999px";a.style.bottom="-9999px";a.style.display="none";a.setAttribute("aria-hidden","true")};Ku=function(){this.Ki=window;this.Zy=this.Dn=this.dw=this.yi=null};
Ku.prototype.open=function(a,b,c,d){Lu(this);this.dw?(this.Dn&&(this.Dn(),this.Dn=null),Mu(this)):this.dw="authPopup"+Math.floor(Math.random()*1E6+1);a:{this.yi=this.Ki.open(a,this.dw,b);try{this.yi.focus();if(this.yi.closed||typeof this.yi.closed=="undefined")throw Error("Y");_.Iu=this.yi}catch(e){d&&setTimeout(d,0);this.yi=null;break a}c&&(this.Dn=c,Nu(this))}};
var Lu=function(a){try{if(a.yi==null||a.yi.closed)a.yi=null,a.dw=null,Mu(a),a.Dn&&(a.Dn(),a.Dn=null)}catch(b){a.yi=null,a.dw=null,Mu(a)}},Nu=function(a){a.Zy=window.setInterval(function(){Lu(a)},300)},Mu=function(a){a.Zy&&(window.clearInterval(a.Zy),a.Zy=null)};Du=Du||{};var Ou=function(a,b){this.Xb=a;this.nI=b;this.Pc=null;this.wo=!1};Ou.prototype.start=function(){if(!this.wo&&!this.Pc){var a=this;this.Pc=window.setTimeout(function(){a.clear();a.wo||(a.Xb(),a.wo=!0)},Du.dU(this.nI))}};
Ou.prototype.clear=function(){this.Pc&&(window.clearTimeout(this.Pc),this.Pc=null)};var Pu=function(a,b){var c=Du.lt;this.Hba=Du.Ys;this.j2=c;this.Xb=a;this.nI=b;this.Pc=null;this.wo=!1;var d=this;this.l2=function(){document[d.Hba]||(d.clear(),d.start())}};Pu.prototype.start=function(){if(!this.wo&&!this.Pc){xu(document,this.j2,this.l2);var a=this;this.Pc=window.setTimeout(function(){a.clear();a.wo||(a.Xb(),a.wo=!0)},Du.dU(this.nI))}};
Pu.prototype.clear=function(){var a=this.j2,b=this.l2,c=document;if(c.removeEventListener)c.removeEventListener(a,b,!1);else if(c.detachEvent)c.detachEvent("on"+a,b);else throw Error("Q`"+a);this.Pc&&(window.clearTimeout(this.Pc),this.Pc=null)};Du.Ys=null;Du.lt=null;
Du.jca=function(){var a=document;typeof a.hidden!=="undefined"?(Du.Ys="hidden",Du.lt="visibilitychange"):typeof a.msHidden!=="undefined"?(Du.Ys="msHidden",Du.lt="msvisibilitychange"):typeof a.webkitHidden!=="undefined"&&(Du.Ys="webkitHidden",Du.lt="webkitvisibilitychange")};Du.jca();Du.I8=function(a,b){return Du.Ys&&Du.lt?new Pu(a,b):new Ou(a,b)};Du.dU=function(a){return Math.max(1,a-(new Date).getTime())};
var Qu=function(a,b){document.cookie="G_ENABLED_IDPS="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"},Ru=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,t=0;t<64;t+=4)q[t/4]=p[t]<<24|p[t+1]<<16|p[t+2]<<8|p[t+3];for(t=16;t<80;t++)p=q[t-3]^q[t-8]^q[t-14]^q[t-16],q[t]=(p<<1|p>>>31)&4294967295;p=e[0];var v=e[1],u=e[2],x=e[3],A=e[4];for(t=0;t<80;t++){if(t<40)if(t<20){var C=x^v&(u^x);var D=1518500249}else C=
v^u^x,D=1859775393;else t<60?(C=v&u|x&(v|u),D=2400959708):(C=v^u^x,D=3395469782);C=((p<<5|p>>>27)&4294967295)+C+A+D+q[t]&4294967295;A=x;x=u;u=(v<<30|v>>>2)&4294967295;v=p;p=C}e[0]=e[0]+p&4294967295;e[1]=e[1]+v&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var t=[],v=0,u=p.length;v<u;++v)t.push(p.charCodeAt(v));p=t}q||(q=p.length);t=0;if(m==0)for(;t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64;for(;t<
q;)if(f[m++]=p[t++],n++,m==64)for(m=0,b(f);t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64}function d(){var p=[],q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var t=63;t>=56;t--)f[t]=q&255,q>>>=8;b(f);for(t=q=0;t<5;t++)for(var v=24;v>=0;v-=8)p[q++]=e[t]>>v&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ti:function(){for(var p=d(),q="",t=0;t<p.length;t++)q+="0123456789ABCDEF".charAt(Math.floor(p[t]/16))+"0123456789ABCDEF".charAt(p[t]%16);return q}}},Su=
window.crypto,Tu=!1,Uu=0,Vu=1,Wu=0,Xu="",Yu=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Vu=Vu*b%Wu;if(++Uu==3)if(a=window,b=Yu,a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("Q`mousemove");},Zu=function(a){var b=Ru();b.update(a);return b.Ti()};Tu=!!Su&&typeof Su.getRandomValues=="function";
Tu||(Wu=(screen.width*screen.width+screen.height)*1E6,Xu=Zu(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+Math.random()),xu(window,"mousemove",Yu));Cu=Cu||{};Cu.A4="ssIFrame_";
_.$u=function(a,b,c){c=c===void 0?!1:c;this.Bb=a;if(!this.Bb)throw Error("Z");a=_.Gu(a,"idpIFrameUrl");if(!a)throw Error("$");this.kV=a;if(!b)throw Error("aa");this.Wm=b;a=this.kV;b=document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];b.protocol=="http:"&&b.port!=""&&b.port!="0"&&b.port!="80"?(a.push(":"),a.push(b.port)):b.protocol=="https:"&&b.port!=""&&b.port!="0"&&b.port!="443"&&(a.push(":"),a.push(b.port));this.WH=a.join("");this.Gfa=[location.protocol,"//",location.host].join("");
this.ix=this.VH=this.Ao=!1;this.gV=null;this.KB=[];this.Sr=[];this.dk={};this.Bo=void 0;this.Ks=c};_.g=_.$u.prototype;_.g.show=function(){var a=this.Bo;a.style.position="fixed";a.style.width="100%";a.style.height="100%";a.style.left="0px";a.style.top="0px";a.style.right="0px";a.style.bottom="0px";a.style.display="block";a.style.zIndex="9999999";a.style.overflow="hidden";a.setAttribute("aria-hidden","false")};_.g.hide=function(){Ju(this.Bo)};
_.g.qB=function(a){if(this.Ao)a&&a(this);else{if(!this.Bo){var b=Cu.A4+this.Bb;var c=this.Bb;var d=location.hostname;var e,f=document.cookie.match("(^|;) ?G_ENABLED_IDPS=([^;]*)(;|$)");f&&f.length>2&&(e=f[2]);(f=e&&su(e.split("|"),c)>=0)?Qu(e,d):Qu(e?e+"|"+c:c,d);c=!f;var h=this.kV,k=this.Gfa;d=this.Wm;e=this.Ks;e=e===void 0?!1:e;f=document.createElement("iframe");f.setAttribute("id",b);b=f.setAttribute;var l="allow-scripts allow-same-origin";document.requestStorageAccess&&uu(document.requestStorageAccess)&&
(l+=" allow-storage-access-by-user-activation");b.call(f,"sandbox",l);f.setAttribute("allow","identity-credentials-get");Ju(f);f.setAttribute("frame-border","0");b=[h,"#origin=",encodeURIComponent(k)];b.push("&rpcToken=");b.push(encodeURIComponent(d));c&&b.push("&clearCache=1");_.pu&&b.push("&debug=1");e&&b.push("&supportBlocked3PCookies=1");document.body.appendChild(f);f.setAttribute("src",b.join(""));this.Bo=f}a&&this.KB.push(a)}};_.g.nW=function(){return this.Ao&&this.ix};_.g.Yn=function(){return this.gV};
av=function(a){for(var b=0;b<a.KB.length;b++)a.KB[b](a);a.KB=[]};_.cv=function(a,b,c,d){if(a.Ao){if(a.Ao&&a.VH)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.Yn(),qu(a),Error(a);bv(a,{method:b,params:c},d)}else a.Sr.push({lp:{method:b,params:c},callback:d}),a.qB()};bv=function(a,b,c){if(c){for(var d=b.id;!d||a.dk[d];)d=(new Date).getMilliseconds()+"-"+(Math.random()*1E6+1);b.id=d;a.dk[d]=c}b.rpcToken=a.Wm;a.Bo.contentWindow.postMessage(_.Au.stringify(b),a.WH)};
dv=function(a){if(a&&a.indexOf("::")>=0)throw Error("ba");};_.$u.prototype.Aj=function(a,b,c,d,e,f,h,k,l){l=l===void 0?!1:l;dv(f);b=_.vu(b);_.cv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f,userInteracted:l},e)};_.$u.prototype.oB=function(a,b,c,d,e){b=_.vu(b);_.cv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};ev=function(a,b,c){dv(b.identifier);_.cv(a,"getSessionSelector",b,c)};
_.fv=function(a,b,c,d,e){dv(b.identifier);_.cv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};gv=function(a,b,c,d,e,f,h){b={clientId:b};c&&(b.pluginName=c);d&&(b.ackExtensionDate=d);b.useFedCm=e;f&&(b.fedCmEnabled=f);_.cv(a,"monitorClient",b,h)};_.$u.prototype.revoke=_.ib(8);_.$u.prototype.Dt=_.ib(10);Cu.KA={};Cu.SG=function(a){return Cu.KA[a]};
Cu.qB=function(a,b,c){c=c===void 0?!1:c;var d=Cu.SG(a);if(!d){d=String;if(Tu){var e=new window.Uint32Array(1);Su.getRandomValues(e);e=Number("0."+e[0])}else e=Vu,e+=parseInt(Xu.substr(0,20),16),Xu=Zu(Xu),e/=Wu+1.2089258196146292E24;d=new _.$u(a,d(2147483647*e),c);Cu.KA[a]=d}d.qB(b)};Cu.r$=function(a){for(var b in Cu.KA){var c=Cu.SG(b);if(c&&c.Bo&&c.Bo.contentWindow==a.source&&c.WH==a.origin)return c}};Cu.T$=function(a){for(var b in Cu.KA){var c=Cu.SG(b);if(c&&c.WH==a)return c}};Cu=Cu||{};
var iv=function(){var a=[],b;for(b in _.hv)a.push(_.hv[b]);Bu.call(this,a);this.xm={};qu("EventBus is ready.")};tu(iv,Bu);_.hv={j6:"sessionSelectorChanged",AE:"sessionStateChanged",Ws:"authResult",q3:"displayIFrame"};kv=function(a,b){var c=jv;a&&b&&(c.xm[a]||(c.xm[a]=[]),su(c.xm[a],b)<0&&c.xm[a].push(b))};lv=function(a){var b=jv;a&&(b.xm[a]||(b.xm[a]=[]))};mv=function(a,b,c){return b&&a.xm[b]&&su(a.xm[b],c)>=0};_.g=iv.prototype;
_.g.Mea=function(a){var b,c=!!a.source&&(a.source===_.Iu||a.source.opener===window);if(b=c?Cu.T$(a.origin):Cu.r$(a)){try{var d=_.Au.parse(a.data)}catch(e){qu("Bad event, an error happened when parsing data.");return}if(!c){if(!d||!d.rpcToken||d.rpcToken!=b.Wm){qu("Bad event, no RPC token.");return}if(d.id&&!d.method){c=d;if(a=b.dk[c.id])delete b.dk[c.id],a(c.result,c.error);return}}d.method!="fireIdpEvent"?qu("Bad IDP event, method unknown."):(a=d.params)&&a.type&&this.jV[a.type]?(d=this.jV[a.type],
c&&!d.v7?qu("Bad IDP event. Source window cannot be a popup."):d.Rs&&!d.Rs.call(this,b,a)?qu("Bad IDP event."):d.Gf.call(this,b,a)):qu("Bad IDP event.")}else qu("Bad event, no corresponding Idp Stub.")};_.g.mga=function(a,b){return mv(this,a.Bb,b.clientId)};_.g.lga=function(a,b){a=a.Bb;b=b.clientId;return!b||mv(this,a,b)};_.g.I7=function(a,b){return mv(this,a.Bb,b.clientId)};
_.g.Wda=function(a,b){a.Ao=!0;a.ix=!!b.cookieDisabled;av(a);for(b=0;b<a.Sr.length;b++)bv(a,a.Sr[b].lp,a.Sr[b].callback);a.Sr=[]};_.g.Vda=function(a,b){b={error:b.error};a.Ao=!0;a.VH=!0;a.gV=b;a.Sr=[];av(a)};_.g.kC=function(a,b){b.originIdp=a.Bb;this.dispatchEvent(b)};var jv=new iv,nv=jv,ov={};ov.idpReady={Gf:nv.Wda};ov.idpError={Gf:nv.Vda};ov.sessionStateChanged={Gf:nv.kC,Rs:nv.mga};ov.sessionSelectorChanged={Gf:nv.kC,Rs:nv.lga};ov.authResult={Gf:nv.kC,Rs:nv.I7,v7:!0};ov.displayIFrame={Gf:nv.kC};
jv.jV=ov||{};xu(window,"message",function(a){jv.Mea.call(jv,a)});
_.pv=function(a,b){this.Oe=!1;if(!a)throw Error("ca");var c=[],d;for(d in a)c.push(a[d]);Bu.call(this,c);this.Bd=[location.protocol,"//",location.host].join("");this.Xd=b.crossSubDomains?b.domain||this.Bd:this.Bd;if(!b)throw Error("da");if(!b.idpId)throw Error("ea");if(!_.Gu(b.idpId,"authServerUrl")||!_.Gu(b.idpId,"idpIFrameUrl"))throw Error("fa`"+b.idpId);this.Bb=b.idpId;this.Pb=void 0;this.T8=!!b.disableTokenRefresh;this.P9=!!b.forceTokenRefresh;this.iha=!!b.skipTokenCache;this.Ks=!!b.supportBlocked3PCookies;
b.pluginName&&(this.Bea=b.pluginName);b.ackExtensionDate&&(this.m7=b.ackExtensionDate);this.T1=b.useFedCm;this.y9=this.Ks&&_.Eu();this.setOptions(b);this.Qt=[];this.ix=this.Ak=this.ZV=!1;this.tj=void 0;this.rZ();this.Od=void 0;var e=this,f=function(){qu("Token Manager is ready.");if(e.Qt.length)for(var h=0;h<e.Qt.length;h++)e.Qt[h].call(e);e.ZV=!0;e.Qt=[]};Cu.qB(this.Bb,function(h){e.Od=h;h.Ao&&h.VH?(e.Ak=!0,e.tj=h.Yn(),e.Jr(e.tj)):(e.ix=h.nW(),e.Pb?gv(e.Od,e.Pb,e.Bea,e.m7,e.T1,e.y9,function(k){var l=
!!k.validOrigin,m=!!k.blocked,n=!!k.suppressed;k.invalidExtension?(e.tj={error:"Invalid value for ack_extension_date. Please refer to [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Ak=!0,e.Jr(e.tj)):l?m?n?(ru("You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
kv(e.Bb,e.Pb),f()):(e.tj={error:"You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Ak=!0,e.Jr(e.tj)):(ru("Your client application uses libraries for user authentication or authorization that are deprecated. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
kv(e.Bb,e.Pb),f()):(e.tj={error:"Not a valid origin for the client: "+e.Bd+" has not been registered for client ID "+e.Pb+". Please go to https://console.developers.google.com/ and register this origin for your project's client ID."},e.Ak=!0,e.Jr(e.tj))}):(lv(e.Bb),f()))},this.Ks)};tu(_.pv,Bu);_.g=_.pv.prototype;_.g.setOptions=function(){};_.g.rZ=function(){};_.g.Jr=function(){};_.g.nW=function(){return this.ix};_.g.Yn=function(){return this.tj};qv=function(a,b,c){return function(){b.apply(a,c)}};
_.rv=function(a,b,c){if(a.ZV)b.apply(a,c);else{if(a.Ak)throw a.tj;a.Qt.push(qv(a,b,c))}};_.pv.prototype.hQ=_.ib(11);_.pv.prototype.Dt=_.ib(9);_.tv=function(a,b){_.pv.call(this,a,b);this.IY=new Ku;this.Kk=this.Wo=null;sv(this)};tu(_.tv,_.pv);_.tv.prototype.setOptions=function(){};
var uv=function(a,b){a.Le={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.Xd};b.crossSubDomains&&(a.Le.policy=b.policy)},vv=function(a,b){if(!b.authParameters)throw Error("ga");if(!b.authParameters.scope)throw Error("ha");if(!b.authParameters.response_type)throw Error("ia");a.vn=b.authParameters;a.vn.redirect_uri||(a.vn.redirect_uri=[location.protocol,"//",location.host,location.pathname].join(""));a.Jj=_.vu(b.rpcAuthParameters||a.vn);if(!a.Jj.scope)throw Error("ja");if(!a.Jj.response_type)throw Error("ka");
a:{var c=a.Jj.response_type.split(" ");for(var d=0,e=c.length;d<e;d++)if(c[d]&&!yu[c[d]]){c=!0;break a}c=!1}if(c)throw Error("la");if(b.enableSerialConsent||b.enableGranularConsent)a.vn.enable_granular_consent=!0,a.Jj.enable_serial_consent=!0;b.authResultIdentifier&&(a.J7=b.authResultIdentifier);b.spec_compliant&&(a.Jj.spec_compliant=b.spec_compliant)};
_.tv.prototype.rZ=function(){var a=this;jv.addEventListener(_.hv.j6,function(b){a.Oe&&a.Le&&b.originIdp==a.Bb&&!b.crossSubDomains==!a.Le.crossSubDomains&&b.domain==a.Le.domain&&b.id==a.Le.id&&a.ZX(b)});jv.addEventListener(_.hv.AE,function(b){a.Oe&&b.originIdp==a.Bb&&b.clientId==a.Pb&&a.aY(b)});jv.addEventListener(_.hv.Ws,function(b){_.Iu=void 0;a.Oe&&b.originIdp==a.Bb&&b.clientId==a.Pb&&b.id==a.Hk&&(a.Wo&&(window.clearTimeout(a.Wo),a.Wo=null),a.Hk=void 0,a.Ro(b))});jv.addEventListener(_.hv.q3,function(b){a.Oe&&
b.originIdp==a.Bb&&(b.hide?a.Od.hide():a.Od.show())})};_.tv.prototype.ZX=function(){};_.tv.prototype.aY=function(){};_.tv.prototype.Ro=function(){};var xv=function(a,b){wv(a);a.T8||(a.Kk=Du.I8(function(){a.Aj(!0)},b-3E5),navigator.onLine&&a.Kk.start())},wv=function(a){a.Kk&&(a.Kk.clear(),a.Kk=null)},sv=function(a){var b=window;zu()&&(b=document.body);xu(b,"online",function(){a.Kk&&a.Kk.start()});xu(b,"offline",function(){a.Kk&&a.Kk.clear()})};_.tv.prototype.Aj=function(){};_.tv.prototype.AX=_.ib(12);
_.tv.prototype.Tca=function(a,b){if(!this.Pb)throw Error("pa");this.Od.oB(this.Pb,this.Jj,this.Le,a,b)};_.tv.prototype.oB=function(a,b){_.rv(this,this.Tca,[a,b])};_.zv=function(a){this.Fe=void 0;this.Lh=!1;this.cs=void 0;_.tv.call(this,yv,a)};tu(_.zv,_.tv);var yv={xO:"noSessionBound",kt:"userLoggedOut",D2:"activeSessionChanged",AE:"sessionStateChanged",P6:"tokenReady",O6:"tokenFailed",Ws:"authResult",ERROR:"error"};
_.zv.prototype.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Pb=a.clientId;this.Da=a.id;uv(this,a);vv(this,a)};_.zv.prototype.Jr=function(a){this.dispatchEvent({type:yv.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.Bb})};var Av=function(a){wv(a);a.cs=void 0;a.KI=void 0};_.g=_.zv.prototype;
_.g.ZX=function(a){var b=a.newValue||{};if(this.Fe!=b.hint||this.Lh!=!!b.disabled){a=this.Fe;var c=!this.Fe||this.Lh;Av(this);this.Fe=b.hint;this.Lh=!!b.disabled;(b=!this.Fe||this.Lh)&&!c?this.dispatchEvent({type:yv.kt,idpId:this.Bb}):b||(a!=this.Fe&&this.dispatchEvent({type:yv.D2,idpId:this.Bb}),this.Fe&&this.Aj())}};
_.g.aY=function(a){this.Lh||(this.Fe?a.user||this.cs?a.user==this.Fe&&(this.cs?a.sessionState?this.cs=a.sessionState:(Av(this),this.dispatchEvent({type:yv.kt,idpId:this.Bb})):a.sessionState&&(this.cs=a.sessionState,this.Aj())):this.Aj():this.dispatchEvent({type:yv.AE,idpId:this.Bb}))};_.g.Ro=function(a){this.dispatchEvent({type:yv.Ws,authResult:a.authResult})};_.g.Bu=_.ib(14);_.g.vu=function(a){_.rv(this,this.FG,[a])};_.g.FG=function(a){ev(this.Od,this.Le,a)};
_.g.zD=function(a,b,c,d){d=d===void 0?!1:d;if(!a)throw Error("ra");Av(this);this.Fe=a;this.Lh=!1;b&&_.fv(this.Od,this.Le,!1,this.Fe);this.Oe=!0;this.Aj(c,!0,d)};_.g.start=function(){_.rv(this,this.Uw,[])};
_.g.Uw=function(){var a=this.Pb==wu("client_id")?wu("login_hint"):void 0;var b=this.Pb==wu("client_id")?wu("state"):void 0;this.uJ=b;if(a)window.history.replaceState?window.history.replaceState(null,document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.zD(a,!0,!0,!0);else{var c=this;this.vu(function(d){c.Oe=!0;d&&d.hint?(Av(c),c.Fe=d.hint,c.Lh=!!d.disabled,c.Lh?c.dispatchEvent({type:yv.kt,idpId:c.Bb}):c.zD(d.hint)):(Av(c),c.Fe=void 0,c.Lh=!(!d||!d.disabled),c.dispatchEvent({type:yv.xO,
autoOpenAuthUrl:!c.Lh,idpId:c.Bb}))})}};_.g.L9=function(){var a=this;this.vu(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:yv.kt,idpId:a.Bb}):a.Aj(!0):a.dispatchEvent({type:yv.xO,idpId:a.Bb})})};_.g.ZS=function(){_.rv(this,this.L9,[])};
_.g.Aj=function(a,b,c){var d=this;this.Od.Aj(this.Pb,this.Jj,this.Fe,this.Le,function(e,f){(f=f||e.error)?f=="user_logged_out"?(Av(d),d.dispatchEvent({type:yv.kt,idpId:d.Bb})):(d.KI=null,d.dispatchEvent({type:yv.O6,idpId:d.Bb,error:f})):(d.KI=e,d.cs=e.session_state,xv(d,e.expires_at),e.idpId=d.Bb,b&&d.uJ&&(e.state=d.uJ,d.uJ=void 0),d.dispatchEvent({type:yv.P6,idpId:d.Bb,response:e}))},this.Da,a,!1,c===void 0?!1:c)};_.g.revoke=_.ib(7);_.g.OZ=_.ib(15);
_.Bv=function(a){this.wn=null;_.tv.call(this,{},a);this.Oe=!0};tu(_.Bv,_.tv);_.g=_.Bv.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Pb=a.clientId;this.Da=a.id;uv(this,a);vv(this,a)};_.g.Jr=function(a){this.wn&&(this.wn({authResult:{error:"idpiframe_initialization_failed",details:a.error}}),this.wn=null)};_.g.Ro=function(a){if(this.wn){var b=this.wn;this.wn=null;b(a)}};_.g.Bu=_.ib(13);_.g.vu=function(a){this.Ak?a(this.Yn()):_.rv(this,this.FG,[a])};
_.g.FG=function(a){ev(this.Od,this.Le,a)};_.Cv=function(a,b,c){a.Ak?c(a.Yn()):_.rv(a,a.iea,[b,c])};_.Bv.prototype.iea=function(a,b){this.Od.Aj(this.Pb,this.Jj,a,this.Le,function(c,d){d?b({error:d}):b(c)},this.Da,this.P9,this.iha)};_.Bv.prototype.NW=_.ib(16);
var Dv=function(a){var b=window.location;a=_.wc(a);a!==void 0&&b.assign(a)},Ev=function(a){return Array.prototype.concat.apply([],arguments)},Fv=function(){try{var a=Array.from((window.crypto||window.msCrypto).getRandomValues(new Uint8Array(64)))}catch(c){a=[];for(var b=0;b<64;b++)a[b]=Math.floor(Math.random()*256)}return _.Jh(a,3).substring(0,64)},Gv=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d===null||d===void 0)d="";b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))}return b.join("&")},
Hv=function(a,b){(b===void 0?0:b)||window.addEventListener("hashchange",function(){location.hash.includes("client_id")&&window.location.reload()});Dv(a)},Iv=function(a,b,c){if(!a.Oe)throw Error("ma");b?_.fv(a.Od,a.Le,!0,void 0,c):_.fv(a.Od,a.Le,!0,a.Fe,c)},Jv=function(a){if(!a.Oe)throw Error("ma");return a.KI},Kv,Lv,Mv,Nv,Ov,Pv,Qv,Rv,Sv,Tv,Uv,Vv,Wv,Xv,$v,cw,dw;
_.Bv.prototype.NW=_.lb(16,function(a,b){var c=this.Od,d=this.Pb,e=this.Le,f=_.vu(this.Jj);delete f.response_type;_.cv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.zv.prototype.OZ=_.lb(15,function(a){Jv(this)&&Jv(this).access_token&&(this.Od.revoke(this.Pb,Jv(this).access_token,a),Iv(this,!0))});
_.zv.prototype.Bu=_.lb(14,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&(a.gC?(b.authResult.client_id=a.Pb,Hv(a.gC+"#"+Gv(b.authResult))):a.zD(b.authResult.login_hint,a.Lh||b.authResult.login_hint!=a.Fe,!0,!0))}});
_.Bv.prototype.Bu=_.lb(13,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.vu(function(d){_.fv(b.Od,b.Le,d&&d.disabled,c.authResult.login_hint,function(){_.Cv(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});_.tv.prototype.AX=_.lb(12,function(){this.Pb&&_.cv(this.Od,"startPolling",{clientId:this.Pb,origin:this.Bd,id:this.Hk})});
_.$u.prototype.revoke=_.lb(8,function(a,b,c){_.cv(this,"revoke",{clientId:a,token:b},c)});_.zv.prototype.revoke=_.lb(7,function(a){_.rv(this,this.OZ,[a])});Kv="openid email profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/plus.me https://www.googleapis.com/auth/plus.login".split(" ");
Lv=function(){var a=navigator.userAgent,b;if(b=!!a&&a.indexOf("CriOS")!=-1)b=-1,(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=parseInt(a[1],10)||-1),b=b<48;return b};
Mv=function(){var a=navigator.userAgent.toLowerCase();if(!(a.indexOf("safari/")>-1&&a.indexOf("chrome/")<0&&a.indexOf("crios/")<0&&a.indexOf("android")<0))return!1;var b=RegExp("version/(\\d+)\\.(\\d+)[\\.0-9]*").exec(navigator.userAgent.toLowerCase());if(!b||b.length<3)return!1;a=parseInt(b[1],10);b=parseInt(b[2],10);return a>12||a==12&&b>=1};Nv=function(a){return a.length>0&&a.every(function(b){return Kv.includes(b)})};
Ov=function(a,b,c,d,e,f,h){var k=_.Gu(a,"authServerUrl");if(!k)throw Error("X`"+a);a=_.vu(d);a.response_type=h||"permission";a.client_id=c;a.ss_domain=b;if(f&&f.extraQueryParams)for(var l in f.extraQueryParams)a[l]=f.extraQueryParams[l];(b=e)&&!(b=Mv())&&(b=navigator.userAgent.toLowerCase(),b.indexOf("ipad;")>-1||b.indexOf("iphone;")>-1?(b=RegExp("os (\\d+)_\\d+(_\\d+)? like mac os x").exec(navigator.userAgent.toLowerCase()),b=!b||b.length<2?!1:parseInt(b[1],10)>=14):b=!1);b&&!a.prompt&&(a.prompt=
"select_account");return k+(k.indexOf("?")<0?"?":"&")+Gv(a)};Pv=function(a,b,c,d){if(!a.Pb)throw Error("na");a.Hk=c||a.J7||"auth"+Math.floor(Math.random()*1E6+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};if(!b.extraQueryParams.redirect_uri){var e=a.Bd.split("//");c=b.extraQueryParams;var f=e[0],h=e[1];e=a.Hk;var k=f.indexOf(":");k>0&&(f=f.substring(0,k));f=["storagerelay://",f,"/",h,"?"];f.push("id="+e);c.redirect_uri=f.join("")}return Ov(a.Bb,a.Xd,a.Pb,a.vn,!0,b,d)};
Qv=function(a,b,c){if(!a.Pb)throw Error("na");return Ov(a.Bb,a.Xd,a.Pb,a.vn,!1,b,c)};Rv=function(a,b){a.Wo&&window.clearTimeout(a.Wo);a.Wo=window.setTimeout(function(){a.Hk==b&&(_.Iu=void 0,a.Wo=null,a.Hk=void 0,a.Ro({authResult:{error:"popup_closed_by_user"}}))},1E3)};
Sv=function(a,b,c){if(!a.Pb)throw Error("oa");c=c||{};c=Pv(a,c.sessionMeta,c.oneTimeId,c.responseType);(Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject||Lv())&&_.rv(a,a.AX,[]);var d=a.Hk;a.IY.open(c,b,function(){a.Hk==d&&Rv(a,d)},function(){a.Hk=void 0;a.Ro({authResult:{error:"popup_blocked_by_browser"}})})};
Tv=function(a,b){var c=b||{};b=_.vu(a.vn);if(c.sessionMeta&&c.sessionMeta.extraQueryParams)for(var d in c.sessionMeta.extraQueryParams)b[d]=c.sessionMeta.extraQueryParams[d];var e;c.sessionMeta.extraQueryParams.scope&&(e=c.sessionMeta.extraQueryParams.scope.split(" "));!e&&b.scope&&(e=b.scope.split(" "));delete b.redirect_uri;delete b.origin;delete b.client_id;delete b.scope;b.prompt=="select_account"&&delete b.prompt;b.gsiwebsdk="fedcm";b.ss_domain=a.Xd;d=_.Gu(a.Bb,"fedcmConfigUrl");c=c.responseType;
b.response_type=c;b.scope=e.join(" ");!b.nonce&&c.includes("id_token")&&(b.nonce="notprovided");c=navigator.userActivation.isActive?"active":"passive";e=Nv(e)?["name","email","picture"]:[];return{identity:{providers:[{configURL:d,clientId:a.Pb,fields:e,params:b}],mode:c},mediation:"required"}};
Uv=function(a,b,c){if(!a.Pb)throw Error("oa");b=Tv(a,b);navigator.credentials.get(b).then(function(d){d=JSON.parse(d.token);var e={client_id:d.client_id,login_hint:d.login_hint,expires_in:3600,scope:d.scope};d.code&&(e.code=d.code);d.id_token&&(e.id_token=d.id_token);a.Ro({type:_.hv.Ws,idpId:a.Bb,authResult:e})},function(d){d.message.indexOf("identity-credentials-get")>=0||d.message.indexOf("Content Security Policy")>=0?c():a.Ro({type:_.hv.Ws,idpId:a.Bb,authResult:{error:d}})})};
Vv=function(a,b,c){a.Ks&&_.Eu()?Uv(a,c,function(){return Sv(a,b,c)}):Sv(a,b,c)};Wv=function(a,b){b=b||{};var c=Qv(a,b.sessionMeta,b.responseType);a.Ks&&_.Eu()&&a.T1?(a.gC=b.sessionMeta.extraQueryParams.redirect_uri,Uv(a,b,function(){return Hv(c,!0)})):Hv(c,!0)};Xv=function(a,b,c){a.Ak?c(a.Yn()):_.rv(a,a.NW,[b,c])};_.Yv=function(a){_.Ee(_.Pe,"le",[]).push(a)};
_.Zv=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
$v=function(a){var b=[];_.Kh(a,function(c){b.push(c)});return b};_.aw=function(a,b){_.pi[b||"token"]=a};_.bw=function(a){delete _.pi[a||"token"]};dw=function(){if(typeof MessageChannel!=="undefined"){var a=new MessageChannel,b={},c=b;a.port1.onmessage=function(){if(b.next!==void 0){b=b.next;var d=b.cb;b.cb=null;d()}};return function(d){c.next={cb:d};c=c.next;a.port2.postMessage(0)}}return function(d){_.Xa.setTimeout(d,0)}};_.Au={parse:function(a){a=_.Qf("["+String(a)+"]");if(a===!1||a.length!==1)throw new SyntaxError("JSON parsing failed.");return a[0]},stringify:function(a){return _.Rf(a)}};_.Bv.prototype.rG=function(a,b){_.rv(this,this.A9,[a,b])};_.Bv.prototype.A9=function(a,b){this.Od.rG(this.Pb,a,this.Jj,this.Le,b)};_.$u.prototype.rG=function(a,b,c,d,e){c=_.vu(c);_.cv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var ew,fw=["client_id","cookie_policy","scope"],gw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint ux_mode redirect_uri state prompt oidc_spec_compliant nonce enable_serial_consent enable_granular_consent include_granted_scopes response_type session_selection plugin_name ack_extension_date use_fedcm gsiwebsdk".split(" "),hw=["authuser","after_redirect","access_type","hl"],iw=["login_hint","prompt"],jw={clientid:"client_id",cookiepolicy:"cookie_policy"},
kw=["approval_prompt","authuser","login_hint","prompt","hd"],lw=["login_hint","g-oauth-window","status"],mw=Math.min(_.Ze("oauth-flow/authWindowWidth",599),screen.width-20),nw=Math.min(_.Ze("oauth-flow/authWindowHeight",600),screen.height-30);var ow=function(a){_.jb.call(this,a)};_.y(ow,_.jb);ow.prototype.name="gapi.auth2.ExternallyVisibleError";var pw=function(){};pw.prototype.select=function(a,b){if(a.sessions&&a.sessions.length==1&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var qw=function(){};qw.prototype.select=function(a,b){if(a.sessions&&a.sessions.length)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.login_hint){b(d);return}}b()};var rw=function(a){this.K7=a};
rw.prototype.select=function(a,b){if(a.sessions)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.session_state&&d.session_state.extraQueryParams&&d.session_state.extraQueryParams.authuser==this.K7){d.login_hint?b(d):b();return}}b()};var sw=function(a){this.ue=a;this.SC=[]};sw.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.SC[b];f?(b++,c.ue.oB(function(h){h?f.select(h,d):d()})):a()}};d()};var tw=function(a){a=new sw(a);a.SC.push(new pw);return a},uw=function(a){a=new sw(a);a.SC.push(new qw);return a},vw=function(a,b){b===void 0||b===null?b=tw(a):(a=new sw(a),a.SC.push(new rw(b)),b=a);return b};var ww=function(a){this.Gf=a;this.isActive=!0};ww.prototype.remove=function(){this.isActive=!1};ww.prototype.trigger=function(){};var xw=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},yw=function(){this.mc=[]};yw.prototype.add=function(a){this.mc.push(a)};yw.prototype.notify=function(a){for(var b=this.mc,c=[],d=0;d<b.length;d++){var e=b[d];e.isActive&&(c.push(e),e=zw(e.Gf,a),e=(0,_.ek)(e),e=(0,_.ck)(e),cw||(cw=dw()),cw(e))}this.mc=c};var zw=function(a,b){return function(){a(b)}};var Bw=function(a){this.Ka=null;this.aia=new Aw(this);this.mc=new yw;a!=void 0&&this.set(a)};Bw.prototype.set=function(a){a!=this.Ka&&(this.Ka=a,this.aia.value=a,this.mc.notify(this.Ka))};Bw.prototype.get=function(){return this.Ka};Bw.prototype.ta=function(a){a=new Cw(this,a);this.mc.add(a);return a};Bw.prototype.get=Bw.prototype.get;var Cw=function(a,b){ww.call(this,b);this.Xca=a};_.y(Cw,ww);Cw.prototype.trigger=function(){var a=this.Gf;a(this.Xca.get())};
var Aw=function(a){this.value=null;this.ta=function(b){return new xw(a.ta(b))}};var Dw={tka:"fetch_basic_profile",vla:"login_hint",Tma:"prompt",Zma:"redirect_uri",rna:"scope",Ooa:"ux_mode",doa:"state"},Ew=function(a){this.Ja={};if(a&&!_.Ch(a))if(typeof a.get=="function")this.Ja=a.get();else for(var b in Dw){var c=Dw[b];c in a&&(this.Ja[c]=a[c])}};Ew.prototype.get=function(){return this.Ja};Ew.prototype.R_=function(a){this.Ja.scope=a;return this};Ew.prototype.Mu=function(){return this.Ja.scope};
var Fw=function(a,b){var c=a.Ja.scope;b=Ev(b.split(" "),c?c.split(" "):[]);_.Bh(b);a.Ja.scope=b.join(" ")};_.g=Ew.prototype;_.g.Mga=function(a){this.Ja.prompt=a;return this};_.g.V$=function(){return this.Ja.prompt};_.g.pga=function(){_.Vf.warn("Property app_package_name no longer supported and was not set");return this};_.g.Z9=function(){_.Vf.warn("Property app_package_name no longer supported")};_.g.qf=function(a){this.Ja.state=a};_.g.getState=function(){return this.Ja.state};var Gw=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+mw,"height="+nw,"top="+(screen.height-nw)/2,"left="+(screen.width-mw)/2].join()},Hw=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;var b=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");a=JSON;var c=a.parse;b=$v(b);return c.call(a,_.Zv(b))},Iw=function(){ew=_.Ze("auth2/idpValue","google");var a=_.Ze("oauth-flow/authUrl",
"https://accounts.google.com/o/oauth2/auth"),b=_.Ze("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");a={fedcmConfigUrl:_.Ze("oauth-flow/fedcmConfigUrl","https://accounts.google.com/o/fedcm/config.json"),authServerUrl:a,idpIFrameUrl:b};_.Hu(ew,a)},Jw=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.ub(a[e])||(a[e]={});a=a[e]}},Kw=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a},
Mw=function(){var a=Lw();a.storage_path&&window.sessionStorage.setItem(a.storage_path,Kw()+window.location.pathname);if(a.status.toLowerCase()=="enforced")throw new ow("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href);a.status.toLowerCase()=="informational"&&_.Vf.warn("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href)},Nw=function(a){return _.hi.get("GSI_ALLOW_3PCD")==="1"?!0:a===!1?!1:a===!0||(_.Pe.le||[]).includes("fedcm_migration_mod")?
!0:!1};var Ow=function(a){var b=a?(b=Hw(a))?b.sub:null:null;this.Da=b;this.Gc=a?_.Tj(a):null};_.g=Ow.prototype;_.g.getId=function(){return this.Da};_.g.OG=function(){var a=Hw(this.Gc);return a?a.hd:null};_.g.qg=function(){return!!this.Gc};_.g.Vl=function(a){if(a)return this.Gc;a=Pw;var b=_.Tj(this.Gc);!a.WA||a.OH||a.wba||(delete b.access_token,delete b.scope);return b};_.g.uK=function(){return Pw.uK()};_.g.Yk=function(){this.Gc=null};_.g.z$=function(){return this.Gc?this.Gc.scope:null};
_.g.update=function(a){this.Da=a.Da;this.Gc=a.Gc;this.Gc.id_token?this.ty=new Qw(this.Gc):this.ty&&(this.ty=null)};var Rw=function(a){return a.Gc&&typeof a.Gc.session_state=="object"?_.Tj(a.Gc.session_state.extraQueryParams||{}):{}};_.g=Ow.prototype;_.g.DG=function(){var a=Rw(this);return a&&a.authuser!==void 0&&a.authuser!==null?a.authuser:null};
_.g.Xk=function(a){var b=Pw,c=new Ew(a);b.OH=c.Mu()?!0:!1;Pw.WA&&Fw(c,"openid profile email");return new _.lk(function(d,e){var f=Rw(this);f.login_hint=this.getId();f.scope=c.Mu();Sw(b,d,e,f)},this)};_.g.Tu=function(a){return new _.lk(function(b,c){var d=a||{},e=Pw;d.login_hint=this.getId();e.Tu(d).then(b,c)},this)};_.g.laa=function(a){return this.Xk(a)};_.g.disconnect=function(){return Pw.disconnect()};_.g.b$=function(){return this.ty};
_.g.EA=function(a){if(!this.qg())return!1;var b=this.Gc&&this.Gc.scope?this.Gc.scope.split(" "):"";return _.Jb(a?a.split(" "):[],function(c){return _.qb(b,c)})};var Qw=function(a){a=Hw(a);this.U9=a.sub;this.Yg=a.name;this.haa=a.given_name;this.x9=a.family_name;this.oV=a.picture;this.nz=a.email};_.g=Qw.prototype;_.g.getId=function(){return this.U9};_.g.getName=function(){return this.Yg};_.g.x$=function(){return this.haa};_.g.t$=function(){return this.x9};_.g.F$=function(){return this.oV};_.g.Tn=function(){return this.nz};var Lw,Tw;Lw=function(){var a=_.hi.get("G_AUTH2_MIGRATION");if(!a)return{status:"none"};a=/(enforced|informational)(?::(.*))?/i.exec(a);return a?{status:a[1].toLowerCase(),storage_path:a[2]}:(_.Vf.warn("The G_AUTH2_MIGRATION cookie value is not valid."),{status:"none"})};Tw=function(a){var b=location;if(a&&a!="none")return a=="single_host_origin"?b.protocol+"//"+b.host:a};
_.Uw=function(a){if(!a)throw new ow("No cookiePolicy");var b=window.location.hostname;a=="single_host_origin"&&(a=window.location.protocol+"//"+b);if(a=="none")return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new ow("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=c.indexOf("https")!=-1;d.domain=a;if(!_.Cj(b,"."+a)&&!_.Cj(b,a))throw new ow("Invalid cookiePolicy domain");return d};var Ww=function(a){var b=a||{},c=Vw();_.Cb(gw,function(d){typeof b[d]==="undefined"&&typeof c[d]!=="undefined"&&(b[d]=c[d])});return b},Vw=function(){for(var a={},b=document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(d.indexOf("google-signin-")==0){d=d.substring(14);var e=b[c].content;jw[d]&&(d=jw[d]);_.qb(gw,d)&&e&&(a[d]=e=="true"?!0:e=="false"?!1:e)}}return a},Xw=function(a){return String(a).replace(/_([a-z])/g,function(b,c){return c.toUpperCase()})},Yw=function(a){_.Cb(gw,
function(b){var c=Xw(b);typeof a[c]!=="undefined"&&typeof a[b]==="undefined"&&(a[b]=a[c],delete a[c])})},Zw=function(a){a=Ww(a);Yw(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=gw+hw,c;for(c in a)b.indexOf(c)<0&&delete a[c];return a},$w=function(a,b){if(!a)throw new ow("Empty initial options.");for(var c=0;c<fw.length;++c)if(!(b&&fw[c]=="scope"||a[fw[c]]))throw new ow("Missing required parameter '"+fw[c]+"'");_.Uw(a.cookie_policy)},bx=function(a){var b={authParameters:{redirect_uri:void 0,
response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm,include_granted_scopes:!0},clientId:a.client_id,crossSubDomains:!0,domain:Tw(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:ew};ax(b,a);_.Cb(iw,function(d){a[d]&&(b.authParameters[d]=a[d])});typeof a.enable_serial_consent=="boolean"&&(b.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent=="boolean"&&(b.enableGranularConsent=a.enable_granular_consent);if(a.plugin_name)b.pluginName=
a.plugin_name;else{var c=_.Ze("auth2/pluginName");c&&(b.pluginName=c)}a.ack_extension_date&&(b.authParameters.ack_extension_date=a.ack_extension_date,b.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(b.useFedCm=a.use_fedcm);return b},ax=function(a,b){var c=b.oidc_spec_compliant;b=b.nonce;c&&(a.spec_compliant=c,b=b||Fv());b&&(a.authParameters.nonce=b,a.forceTokenRefresh=!0,a.skipTokenCache=!0)},gx=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=
a.hosted_domain,h=a.oidc_spec_compliant,k=a.nonce,l=cx(a),m={authParameters:{response_type:l,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:l,scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:Tw(c),idpId:ew};f&&(m.authParameters.hd=f,m.rpcAuthParameters.hd=f);h&&(m.rpcAuthParameters.spec_compliant=h,k=k||Fv());k&&(m.authParameters.nonce=k,m.rpcAuthParameters.nonce=k,m.forceTokenRefresh=!0,m.skipTokenCache=!0);_.Cb(iw.concat(hw),function(n){a[n]&&(m.authParameters[n]=a[n])});
a.authuser!==void 0&&a.authuser!==null&&(m.authParameters.authuser=a.authuser);typeof a.include_granted_scopes=="boolean"&&(b=new dx(a.response_type||"token"),ex(b)&&(m.authParameters.include_granted_scopes=a.include_granted_scopes),fx(b)&&(m.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,a.include_granted_scopes===!1&&(m.forceTokenRefresh=!0,m.skipTokenCache=!0)));typeof a.enable_serial_consent=="boolean"&&(m.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent==
"boolean"&&(m.enableGranularConsent=a.enable_granular_consent);a.plugin_name?m.pluginName=a.plugin_name:(b=_.Ze("auth2/pluginName"))&&(m.pluginName=b);a.ack_extension_date&&(m.authParameters.ack_extension_date=a.ack_extension_date,m.rpcAuthParameters.ack_extension_date=a.ack_extension_date,m.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(m.useFedCm=a.use_fedcm);return m},cx=function(a){a=new dx(a.response_type||"token");var b=[];fx(a)&&b.push("token");hx(a,"id_token")&&b.push("id_token");
b.length==0&&(b=["token","id_token"]);return b.join(" ")},ix=["permission","id_token"],jx=/(^|[^_])token/,dx=function(a){this.Vr=[];this.kI(a)};dx.prototype.kI=function(a){a?((a.indexOf("permission")>=0||a.match(jx))&&this.Vr.push("permission"),a.indexOf("id_token")>=0&&this.Vr.push("id_token"),a.indexOf("code")>=0&&this.Vr.push("code")):this.Vr=ix};var ex=function(a){return hx(a,"code")},fx=function(a){return hx(a,"permission")};dx.prototype.toString=function(){return this.Vr.join(" ")};
var hx=function(a,b){var c=!1;_.Cb(a.Vr,function(d){d==b&&(c=!0)});return c};var lx=function(a,b,c){this.wJ=b;this.Kda=a;for(var d in a)a.hasOwnProperty(d)&&kx(this,d);if(c&&c.length)for(a=0;a<c.length;a++)this[c[a]]=this.wJ[c[a]]},kx=function(a,b){a[b]=function(){return a.Kda[b].apply(a.wJ,arguments)}};lx.prototype.then=function(a,b,c){var d=this;return _.pk().then(function(){return mx(d.wJ,a,b,c)})};_.ak(lx);var Pw,nx,px;Pw=null;_.ox=function(){return Pw?nx():null};nx=function(){return new lx(px.prototype,Pw,["currentUser","isSignedIn"])};px=function(a){delete a.include_granted_scopes;this.Ja=bx(a);this.G8=a.cookie_policy;this.wba=!!a.scope;(this.WA=a.fetch_basic_profile!==!1)&&(this.Ja.authParameters.scope=qx(this,"openid profile email"));this.Ja.supportBlocked3PCookies=Nw(a.use_fedcm);this.lv=a.hosted_domain;this.Yha=a.ux_mode||"popup";this.gC=a.redirect_uri||null;this.hI()};
px.prototype.hI=function(){this.currentUser=new Bw(new Ow(null));this.isSignedIn=new Bw(!1);this.ue=new _.zv(this.Ja);this.cB=this.nr=null;this.Ica=new _.lk(function(a,b){this.nr=a;this.cB=b},this);this.JB={};this.yv=!0;rx(this);this.ue.start()};
var rx=function(a){a.ue.addEventListener("error",function(b){a.yv&&a.nr&&(a.yv=!1,a.cB({error:b.error,details:b.details}),a.nr=null,a.cB=null)});a.ue.addEventListener("authResult",function(b){b&&b.authResult&&a.zf(b);a.ue.Bu()(b)});a.ue.addEventListener("tokenReady",function(b){var c=new Ow(b.response);if(a.lv&&a.lv!=c.OG())a.zf({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.OG(),expectedDomain:a.lv});else{a.currentUser.get().update(c);
var d=a.currentUser;d.mc.notify(d.Ka);a.isSignedIn.set(!0);c=c.DG();(d=_.Uw(a.G8))&&c&&_.hi.set(["G_AUTHUSER_",window.location.protocol==="https:"&&d.ef?"S":"H",d.Ui].join(""),c,{domain:d.domain,secure:d.isSecure});_.aw(b.response);a.zf(b)}});a.ue.addEventListener("noSessionBound",function(b){a.yv&&b.autoOpenAuthUrl?(a.yv=!1,tw(a.ue).select(function(c){if(c&&c.login_hint){var d=a.ue;_.rv(d,d.zD,[c.login_hint,!0])}else a.currentUser.set(new Ow(null)),a.isSignedIn.set(!1),_.bw(),a.zf(b)})):(a.currentUser.set(new Ow(null)),
a.isSignedIn.set(!1),_.bw(),a.zf(b))});a.ue.addEventListener("tokenFailed",function(b){a.zf(b)});a.ue.addEventListener("userLoggedOut",function(b){a.currentUser.get().Yk();var c=a.currentUser;c.mc.notify(c.Ka);a.isSignedIn.set(!1);_.bw();a.zf(b)})},mx=function(a,b,c,d){return a.Ica.then(function(e){if(b)return b(e.jaa)},c,d)};px.prototype.zf=function(a){if(a){this.yv=!1;var b=a.type||"";if(this.JB[b])this.JB[b](a);this.nr&&(this.nr({jaa:this}),this.cB=this.nr=null)}};
var sx=function(a,b){_.Zb(b,function(c,d){a.JB[d]=function(e){a.JB={};c(e)}})},Sw=function(a,b,c,d){d=_.Tj(d);a.lv&&(d.hd=a.lv);var e=d.ux_mode||a.Yha;delete d.ux_mode;delete d.app_package_name;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};e=="redirect"?(d.redirect_uri||(d.redirect_uri=a.gC||Kw()+window.location.pathname),tx(a,f)):(delete d.redirect_uri,ux(a,f),sx(a,{authResult:function(h){h.authResult&&h.authResult.error?c(h.authResult):sx(a,{tokenReady:function(){b(a.currentUser.get())},
tokenFailed:c})}}))};px.prototype.Xk=function(a){return new _.lk(function(b,c){var d=new Ew(a);this.OH=d.Mu()?!0:!1;this.WA?(d.Ja.fetch_basic_profile=!0,Fw(d,"email profile openid")):d.Ja.fetch_basic_profile=!1;var e=qx(this,d.Mu());d.R_(e);Sw(this,b,c,d.get())},this)};
px.prototype.Tu=function(a){var b=a||{};this.OH=!!b.scope;a=qx(this,b.scope);if(a=="")return _.qk({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};_.Cb(kw,function(d){b[d]!=null&&(c[d]=b[d])});c.hasOwnProperty("prompt")||c.hasOwnProperty("approval_prompt")||(c.prompt="consent");b.redirect_uri=="postmessage"||b.redirect_uri==void 0?a=vx(this,c):(c.redirect_uri=b.redirect_uri,tx(this,{sessionMeta:{extraQueryParams:c},responseType:"code id_token"}),
a=_.pk({message:"Redirecting to IDP."}));return a};
var vx=function(a,b){b.origin=Kw();delete b.redirect_uri;ux(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.lk(function(c,d){sx(this,{authResult:function(e){(e=e&&e.authResult)&&e.code?c({code:e.code}):d(e&&e.error?e:{error:"unknown_error"})}})},a)},ux=function(a,b){Jw(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Vv(a.ue,Gw(),b)},tx=function(a,b){Jw(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Wv(a.ue,b)};
px.prototype.Yk=function(a){var b=a||!1;return new _.lk(function(c){Iv(this.ue,b,function(){c()})},this)};px.prototype.KT=function(){return this.Ja.authParameters.scope};var qx=function(a,b){a=a.KT();b=Ev(b?b.split(" "):[],a?a.split(" "):[]);_.Bh(b);return b.join(" ")};px.prototype.uK=function(){var a=this;return new _.lk(function(b,c){sx(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(d){b(d.response)}});a.ue.ZS()})};
px.prototype.DP=function(a,b,c,d){if(a=typeof a==="string"?document.getElementById(a):a){var e=this;_.tj(a,"click",function(){var f=b;typeof b=="function"&&(f=b());e.Xk(f).then(function(h){c&&c(h)},function(h){d&&d(h)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})};px.prototype.disconnect=function(){return new _.lk(function(a){this.ue.revoke(function(){a()})},this)};px.prototype.attachClickHandler=px.prototype.DP;var wx;_.lk.prototype["catch"]=_.lk.prototype.FD;wx=null;_.xx=function(a){Mw();a=Zw(a);if(Pw){if(_.ou(a,wx||{}))return nx();throw new ow("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}$w(a,a.fetch_basic_profile!==!1);Iw();wx=a;Pw=new px(a);_.Pe.ga=1;return nx()};var zx,Bx,yx,Dx,Cx,Ex;
_.Ax=function(a,b){Mw();Iw();a=Zw(a);$w(a);var c=gx(a);c.supportBlocked3PCookies=Nw(a.use_fedcm);var d=new _.Bv(c);a.prompt=="none"?yx(d,a,function(e){e.status=e.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(e)}):zx(d,a,function(e){if(e.error)e.status={signed_in:!1,method:null,google_logged_in:!1};else{var f=e.access_token||e.id_token;e.status={signed_in:!!f,method:"PROMPT",google_logged_in:!!f}}e["g-oauth-window"]=d.IY.yi;b(e)})};
zx=function(a,b,c){var d=new dx(b.response_type);c=Bx(a,d,c);var e={responseType:d.toString()};Jw(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");ex(d)&&Jw(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&Jw(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&Jw(e,["sessionMeta","extraQueryParams","state"],b.state);b=Gw();a.Ak?c({authResult:{error:"idpiframe_initialization_failed",details:a.Yn().error}}):(a.wn=
c,Vv(a,b,e))};Bx=function(a,b,c){if(fx(b)){var d=Cx(c);return function(e){e&&e.authResult&&!e.authResult.error?a.Bu(function(f){f&&!f.error?(f=_.Tj(f),ex(b)&&(f.code=e.authResult.code),d(f)):d(f?f:{error:"unknown_error"})})(e):d(e&&e.authResult?e.authResult:{error:"unknown_error"})}}return function(e){e&&e.authResult&&!e.authResult.error?c(_.Tj(e.authResult)):c(e&&e.authResult?e.authResult:{error:"unknown_error"})}};
yx=function(a,b,c){if(ex(new dx(b.response_type))&&b.access_type=="offline")c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=Cx(c);b.login_hint?a.rG(b.login_hint,function(e){e?Dx(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):b.authuser!==void 0&&b.authuser!==null?vw(a,b.authuser).select(function(e){e&&e.login_hint?Dx(a,b,e.login_hint,d):d({error:"immediate_failed",error_subtype:"access_denied"})}):a.vu(function(e){e&&e.hint?Dx(a,b,e.hint,d):e&&e.disabled?
d({error:"immediate_failed",error_subtype:"no_user_bound"}):(b.session_selection=="first_valid"?uw(a):tw(a)).select(function(f){f&&f.login_hint?Dx(a,b,f.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};Dx=function(a,b,c,d){b=new dx(b.response_type);var e=0,f={},h=function(k){!k||k.error?d(k):(e--,_.Zi(f,k),e==0&&d(f))};(fx(b)||hx(b,"id_token"))&&e++;ex(b)&&e++;(fx(b)||hx(b,"id_token"))&&_.Cv(a,c,h);ex(b)&&Xv(a,c,h)};
Cx=function(a){return function(b){if(!b||b.error)_.bw(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.Tj(b);Ex(c);delete c.id_token;delete c.code;_.aw(c)}a(b)}}};Ex=function(a){_.Cb(lw,function(b){delete a[b]})};_.r("gapi.auth2.init",_.xx);_.r("gapi.auth2.authorize",function(a,b){if(Pw!=null)throw new ow("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.Ax(a,function(c){Ex(c);b(c)})});_.r("gapi.auth2._gt",function(){return _.qi()});_.r("gapi.auth2.enableDebugLogs",function(a){a=a!==!1;_.pu=a!="0"&&!!a});_.r("gapi.auth2.getAuthInstance",_.ox);
_.r("gapi.auth2.BasicProfile",Qw);_.r("gapi.auth2.BasicProfile.prototype.getId",Qw.prototype.getId);_.r("gapi.auth2.BasicProfile.prototype.getName",Qw.prototype.getName);_.r("gapi.auth2.BasicProfile.prototype.getGivenName",Qw.prototype.x$);_.r("gapi.auth2.BasicProfile.prototype.getFamilyName",Qw.prototype.t$);_.r("gapi.auth2.BasicProfile.prototype.getImageUrl",Qw.prototype.F$);_.r("gapi.auth2.BasicProfile.prototype.getEmail",Qw.prototype.Tn);_.r("gapi.auth2.GoogleAuth",px);
_.r("gapi.auth2.GoogleAuth.prototype.attachClickHandler",px.prototype.DP);_.r("gapi.auth2.GoogleAuth.prototype.disconnect",px.prototype.disconnect);_.r("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",px.prototype.Tu);_.r("gapi.auth2.GoogleAuth.prototype.signIn",px.prototype.Xk);_.r("gapi.auth2.GoogleAuth.prototype.signOut",px.prototype.Yk);_.r("gapi.auth2.GoogleAuth.prototype.getInitialScopes",px.prototype.KT);_.r("gapi.auth2.GoogleUser",Ow);_.r("gapi.auth2.GoogleUser.prototype.grant",Ow.prototype.laa);
_.r("gapi.auth2.GoogleUser.prototype.getId",Ow.prototype.getId);_.r("gapi.auth2.GoogleUser.prototype.isSignedIn",Ow.prototype.qg);_.r("gapi.auth2.GoogleUser.prototype.getAuthResponse",Ow.prototype.Vl);_.r("gapi.auth2.GoogleUser.prototype.getBasicProfile",Ow.prototype.b$);_.r("gapi.auth2.GoogleUser.prototype.getGrantedScopes",Ow.prototype.z$);_.r("gapi.auth2.GoogleUser.prototype.getHostedDomain",Ow.prototype.OG);_.r("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",Ow.prototype.Tu);
_.r("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",Ow.prototype.EA);_.r("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",Ow.prototype.uK);_.r("gapi.auth2.LiveValue",Bw);_.r("gapi.auth2.LiveValue.prototype.listen",Bw.prototype.ta);_.r("gapi.auth2.LiveValue.prototype.get",Bw.prototype.get);_.r("gapi.auth2.SigninOptionsBuilder",Ew);_.r("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",Ew.prototype.Z9);_.r("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",Ew.prototype.pga);
_.r("gapi.auth2.SigninOptionsBuilder.prototype.getScope",Ew.prototype.Mu);_.r("gapi.auth2.SigninOptionsBuilder.prototype.setScope",Ew.prototype.R_);_.r("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",Ew.prototype.V$);_.r("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",Ew.prototype.Mga);_.r("gapi.auth2.SigninOptionsBuilder.prototype.get",Ew.prototype.get);
_.cf=_.cf||{};
(function(){function a(b){var c="";if(b.nodeType==3||b.nodeType==4)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.cf.createElement=function(b){if(!document.body||document.body.namespaceURI)try{var c=document.createElementNS("http://www.w3.org/1999/xhtml",b)}catch(d){}return c||document.createElement(b)};_.cf.EQ=function(b){var c=_.cf.createElement("iframe");try{var d=
["<","iframe"],e=b||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.cf.dG(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.cf.createElement(d.join(""));h&&(!c||h.tagName==c.tagName&&h.namespaceURI==c.namespaceURI)&&(c=h)}catch(l){}d=c;b=b||{};for(var k in b)b.hasOwnProperty(k)&&(d[k]=b[k]);return c};_.cf.nT=function(){if(document.body)return document.body;try{var b=document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml","body");if(b&&
b.length==1)return b[0]}catch(c){}return document.documentElement||document};_.cf.qra=function(b){return a(b)}})();
_.Fg=window.gapi&&window.gapi.util||{};
_.Fg=_.Fg={};_.Fg.getOrigin=function(a){return _.Hg(a)};
_.$x=function(a){if(a.indexOf("GCSC")!==0)return null;var b={yj:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(d==-1)return b;var e=_.Yx(a.substr(d+1));if(e==null)return b;a=a.substring(0,d);if(a.charAt(0)!=="_")return b;d=c==="E"&&e.ef;return!d&&(c!=="U"||e.ef)||d&&!_.Zx?b:{yj:!0,ef:d,p8:a.substr(1),domain:e.domain,Ui:e.Ui}};_.ay=function(a,b){this.Yg=a;a=b||{};this.pda=Number(a.maxAge)||0;this.Xd=a.domain;this.Om=a.path;this.Vfa=!!a.secure};_.ay.prototype.read=function(){for(var a=this.Yg+"=",b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(d.indexOf(a)==0)return d.substr(a.length)}};
_.ay.prototype.write=function(a,b){if(!by.test(this.Yg))throw"Invalid cookie name";if(!cy.test(a))throw"Invalid cookie value";a=this.Yg+"="+a;this.Xd&&(a+=";domain="+this.Xd);this.Om&&(a+=";path="+this.Om);b=typeof b==="number"?b:this.pda;if(b>=0){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.Vfa&&(a+=";secure");document.cookie=a;return!0};_.ay.prototype.clear=function(){this.write("",0)};var cy=/^[-+/_=.:|%&a-zA-Z0-9@]*$/,by=/^[A-Z_][A-Z0-9_]{0,63}$/;
_.ay.iterate=function(a){for(var b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};_.dy=function(a){this.Kf=a};_.dy.prototype.read=function(){if(ey.hasOwnProperty(this.Kf))return ey[this.Kf]};_.dy.prototype.write=function(a){ey[this.Kf]=a;return!0};_.dy.prototype.clear=function(){delete ey[this.Kf]};var ey={};_.dy.iterate=function(a){for(var b in ey)ey.hasOwnProperty(b)&&a(b,ey[b])};var fy=function(){this.Ka=null;this.key=function(){return null};this.getItem=function(){return this.Ka};this.setItem=function(a,b){this.Ka=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.Ka=null;this.length=0};this.length=0},gy=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}},hy=function(a,b){this.Yg=a;this.uN=
gy(b)?b||window.sessionStorage:new fy};hy.prototype.read=function(){return this.uN.getItem(this.Yg)};hy.prototype.write=function(a){try{this.uN.setItem(this.Yg,a)}catch(b){return!1}return!0};hy.prototype.clear=function(){this.uN.removeItem(this.Yg)};hy.iterate=function(a){if(gy())for(var b=window.sessionStorage.length,c=0;c<b;++c){var d=window.sessionStorage.key(c);a(d,window.sessionStorage[d])}};_.Zx=window.location.protocol==="https:";_.iy=_.Zx||window.location.protocol==="http:"?_.ay:_.dy;_.Yx=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(b!==""){c=parseInt(b,10);if(isNaN(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{ef:a.charAt(0)=="S",domain:d,Ui:c}};var jy,ky,ny,oy;jy=_.Fe();ky=_.Fe();_.ly=_.Fe();_.my=_.Fe();ny="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");oy=function(a){this.CY=a;this.cJ=null};
oy.prototype.write=function(a){var b=_.Fe(),c=_.Fe(),d=window.decodeURIComponent?decodeURIComponent:unescape,e;for(e in a)if(_.Ge(a,e)){var f=a[e];f=f.replace(/\+/g," ");c[e]=d(f);b[e]=a[e]}d=ny.length;for(e=0;e<d;++e)delete c[ny[e]];a=String(a.authuser||0);d=_.Fe();d[a]=c;c=_.Rf(d);this.CY.write(c);this.cJ=b};oy.prototype.read=function(){return this.cJ};oy.prototype.clear=function(){this.CY.clear();this.cJ=_.Fe()};_.py=function(a){return a?{domain:a.domain,path:"/",secure:a.ef}:null};
hy.iterate(function(a){var b=_.$x(a);b&&b.yj&&(jy[a]=new oy(new hy(a)))});_.iy.iterate(function(a){jy[a]&&(ky[a]=new _.iy(a,_.py(_.$x(a))))});
_.ii=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,t=0;t<64;t+=4)q[t/4]=p[t]<<24|p[t+1]<<16|p[t+2]<<8|p[t+3];for(t=16;t<80;t++)p=q[t-3]^q[t-8]^q[t-14]^q[t-16],q[t]=(p<<1|p>>>31)&4294967295;p=e[0];var v=e[1],u=e[2],x=e[3],A=e[4];for(t=0;t<80;t++){if(t<40)if(t<20){var C=x^v&(u^x);var D=1518500249}else C=v^u^x,D=1859775393;else t<60?(C=v&u|x&(v|u),D=2400959708):(C=v^u^x,D=3395469782);C=((p<<5|p>>>27)&4294967295)+
C+A+D+q[t]&4294967295;A=x;x=u;u=(v<<30|v>>>2)&4294967295;v=p;p=C}e[0]=e[0]+p&4294967295;e[1]=e[1]+v&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var t=[],v=0,u=p.length;v<u;++v)t.push(p.charCodeAt(v));p=t}q||(q=p.length);t=0;if(m==0)for(;t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64;for(;t<q;)if(f[m++]=p[t++],n++,m==64)for(m=0,b(f);t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var t=63;t>=56;t--)f[t]=q&255,q>>>=8;b(f);for(t=q=0;t<5;t++)for(var v=24;v>=0;v-=8)p[q++]=e[t]>>v&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ti:function(){for(var p=d(),q="",t=0;t<p.length;t++)q+="0123456789ABCDEF".charAt(Math.floor(p[t]/16))+"0123456789ABCDEF".charAt(p[t]%16);return q}}};var ki=function(a,b,c){var d=String(_.Xa.location.href);return d&&a&&b?[b,ji(_.Hg(d),a,c||null)].join(" "):null},ji=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.Cb(d,function(k){e.push(k)}),li(e.join(" "));var f=[],h=[];_.Cb(c,function(k){h.push(k.key);f.push(k.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.Cb(d,function(k){e.push(k)});a=li(e.join(" "));a=[c,a];h.length==0||a.push(h.join(""));return a.join("_")},li=function(a){var b=
_.ii();b.update(a);return b.Ti().toLowerCase()};var ni;_.mi=function(){var a=_.Xa.__SAPISID||_.Xa.__APISID||_.Xa.__3PSAPISID||_.Xa.__1PSAPISID||_.Xa.__OVERRIDE_SID;if(a)return!0;typeof document!=="undefined"&&(a=new _.fi(document),a=a.get("SAPISID")||a.get("APISID")||a.get("__Secure-3PAPISID")||a.get("__Secure-1PAPISID"));return!!a};ni=function(a,b,c,d){(a=_.Xa[a])||typeof document==="undefined"||(a=(new _.fi(document)).get(b));return a?ki(a,c,d):null};
_.oi=function(a){var b=_.Hg(_.Xa==null?void 0:_.Xa.location.href),c=[];if(_.mi()){b=b.indexOf("https:")==0||b.indexOf("chrome-extension:")==0||b.indexOf("chrome-untrusted://new-tab-page")==0||b.indexOf("moz-extension:")==0;var d=b?_.Xa.__SAPISID:_.Xa.__APISID;d||typeof document==="undefined"||(d=new _.fi(document),d=d.get(b?"SAPISID":"APISID")||d.get("__Secure-3PAPISID"));(d=d?ki(d,b?"SAPISIDHASH":"APISIDHASH",a):null)&&c.push(d);b&&((b=ni("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&c.push(b),
(a=ni("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&c.push(a))}return c.length==0?null:c.join(" ")};
var Nr,Or;_.Fr=function(a){if(a instanceof _.hc)return a.WY;throw Error("j");};_.Gr=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.Hr=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.Ir=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.Ir.prototype;_.g.clone=function(){return new _.Ir(this.x,this.y)};_.g.equals=function(a){return a instanceof _.Ir&&_.Hr(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};
_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.Ir?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.Jr=function(a){return a.scrollingElement?a.scrollingElement:!_.Fd&&_.ge(a)?a.documentElement:a.body||a.documentElement};
_.Kr=function(a){var b=_.Jr(a);a=a.defaultView;return new _.Ir(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.Lr=function(a,b,c,d){return _.be(a.zc,b,c,d)};_.Mr=function(a){return _.Kr(a.zc)};Nr=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};Or=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.Pr=function(a){return _.ae(document,a)};_.g=_.Gr.prototype;_.g.Rb=function(){return this.right-this.left};_.g.Mc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.Gr(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.Gr?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.ub(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.Ir?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var Sr,Qr,Wr,Yr;_.Rr=function(a,b,c){if(typeof b==="string")(b=Qr(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=Qr(c,d);f&&(c.style[f]=e)}};Sr={};Qr=function(a,b){var c=Sr[b];if(!c){var d=Nr(b);c=d;a.style[d]===void 0&&(d=(_.Fd?"Webkit":_.Ed?"Moz":null)+Or(d),a.style[d]!==void 0&&(c=d));Sr[b]=c}return c};_.Tr=function(a,b){var c=a.style[Nr(b)];return typeof c!=="undefined"?c:a.style[Qr(a,b)]||""};
_.Ur=function(a,b){var c=_.Zd(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Vr=function(a,b){return _.Ur(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Wr=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Zr=function(a,b){b=b||_.Jr(document);var c=b||_.Jr(document);var d=_.Xr(a),e=_.Xr(c),f=_.Ur(c,"borderLeftWidth");var h=_.Ur(c,"borderRightWidth");var k=_.Ur(c,"borderTopWidth"),l=_.Ur(c,"borderBottomWidth");h=new _.Gr(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.Jr(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Yr(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.Ir(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Xr=function(a){var b=_.Zd(a),c=new _.Ir(0,0);if(a==(b?_.Zd(b):document).documentElement)return c;a=Wr(a);b=_.Mr(_.$d(b));c.x=a.left+b.x;c.y=a.top+b.y;return c};_.as=function(a,b){var c=new _.Ir(0,0),d=_.he(_.Zd(a));a:{try{_.Xb(d.parent);var e=!0;break a}catch(f){}e=!1}if(!e)return c;do e=d==b?_.Xr(a):_.$r(a),c.x+=e.x,c.y+=e.y;while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};
_.$r=function(a){a=Wr(a);return new _.Ir(a.left,a.top)};_.cs=function(a,b,c){if(b instanceof _.vd)c=b.height,b=b.width;else if(c==void 0)throw Error("J");a.style.width=_.bs(b,!0);a.style.height=_.bs(c,!0)};_.bs=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};
_.ds=function(a){var b=Yr;if(_.Vr(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};Yr=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.Fd&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Wr(a),new _.vd(a.right-a.left,a.bottom-a.top)):new _.vd(b,c)};_.es=function(a,b){a.style.display=b?"":"none"};
_.gs=function(a){var b=_.$d(void 0),c=_.Lr(b,"HEAD")[0];if(!c){var d=_.Lr(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Jc("style",document))&&d.setAttribute("nonce",e);_.fs(d,a);b.appendChild(c,d)};_.fs=function(a,b){b=_.Fr(b);_.Xa.trustedTypes?_.we(a,b):a.innerHTML=b};_.hs=_.Ed?"MozUserSelect":_.Fd||_.Cd?"WebkitUserSelect":null;
_.qy=function(a){_.Ui.call(this);this.Kf=1;this.aC=[];this.fC=0;this.Qf=[];this.Sj={};this.D7=!!a};_.cb(_.qy,_.Ui);_.g=_.qy.prototype;_.g.subscribe=function(a,b,c){var d=this.Sj[a];d||(d=this.Sj[a]=[]);var e=this.Kf;this.Qf[e]=a;this.Qf[e+1]=b;this.Qf[e+2]=c;this.Kf=e+3;d.push(e);return e};_.g.Xw=_.ib(18);_.g.unsubscribe=function(a,b,c){if(a=this.Sj[a]){var d=this.Qf;if(a=a.find(function(e){return d[e+1]==b&&d[e+2]==c}))return this.ql(a)}return!1};
_.g.ql=function(a){var b=this.Qf[a];if(b){var c=this.Sj[b];this.fC!=0?(this.aC.push(a),this.Qf[a+1]=function(){}):(c&&_.Xi(c,a),delete this.Qf[a],delete this.Qf[a+1],delete this.Qf[a+2])}return!!b};
_.g.ep=function(a,b){var c=this.Sj[a];if(c){var d=Array(arguments.length-1),e=arguments.length,f;for(f=1;f<e;f++)d[f-1]=arguments[f];if(this.D7)for(f=0;f<c.length;f++)e=c[f],ry(this.Qf[e+1],this.Qf[e+2],d);else{this.fC++;try{for(f=0,e=c.length;f<e&&!this.isDisposed();f++){var h=c[f];this.Qf[h+1].apply(this.Qf[h+2],d)}}finally{if(this.fC--,this.aC.length>0&&this.fC==0)for(;c=this.aC.pop();)this.ql(c)}}return f!=0}return!1};var ry=function(a,b,c){_.jk(function(){a.apply(b,c)})};
_.qy.prototype.clear=function(a){if(a){var b=this.Sj[a];b&&(b.forEach(this.ql,this),delete this.Sj[a])}else this.Qf.length=0,this.Sj={}};_.qy.prototype.Yb=function(a){if(a){var b=this.Sj[a];return b?b.length:0}a=0;for(b in this.Sj)a+=this.Yb(b);return a};_.qy.prototype.ua=function(){_.qy.N.ua.call(this);this.clear();this.aC.length=0};
_.sy=function(a){this.Hha=a};_.ty=function(a){_.Ui.call(this);this.ke=new _.qy(a);_.Wi(this,this.ke)};_.sy.prototype.toString=function(){return this.Hha};_.cb(_.ty,_.Ui);_.g=_.ty.prototype;_.g.subscribe=function(a,b,c){return this.ke.subscribe(a.toString(),b,c)};_.g.Xw=_.ib(17);_.g.unsubscribe=function(a,b,c){return this.ke.unsubscribe(a.toString(),b,c)};_.g.ql=function(a){return this.ke.ql(a)};_.g.ep=function(a,b){return this.ke.ep(a.toString(),b)};_.g.clear=function(a){this.ke.clear(a!==void 0?a.toString():void 0)};_.g.Yb=function(a){return this.ke.Yb(a!==void 0?a.toString():void 0)};
var uy,vy,yy,By,wy,zy,Ay,xy;uy=function(a){var b=_.xc("");return _.ec(a.map(function(c){return _.fc(_.xc(c))}).join(_.fc(b).toString()))};vy=function(a){return uy(a)};yy=function(a){for(var b="",c=Object.keys(a),d=0;d<c.length;d++){var e=c[d],f=a[e];if(!wy.test(e))throw Error("j");if(f!==void 0&&f!==null){if(/^on./i.test(e))throw Error("j");xy.indexOf(e.toLowerCase())!==-1&&(f=_.oc(f)?f.toString():_.uc(String(f))||"about:invalid#zClosurez");f=e+'="'+_.xc(String(f))+'"';b+=" "+f}}return b};
By=function(a){var b;if(!wy.test("div"))throw Error("j");if(zy.indexOf("DIV")!==-1)throw Error("j");var c="<div";a&&(c+=yy(a));Array.isArray(b)||(b=b===void 0?[]:[b]);Ay.indexOf("DIV")!==-1?c+=">":(a=vy(b.map(function(d){return d instanceof _.dc?d:_.xc(String(d))})),c+=">"+a.toString()+"</div>");return _.ec(c)};wy=/^[a-z][a-z\d-]*$/i;zy="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");Ay="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" ");
xy=["action","formaction","href"];_.Cy=function(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.property+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});_.Rr(a,"transition",b.join(","))};_.Dy=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=_.ne("DIV"),b=_.Fd?"-webkit":_.Ed?"-moz":null,c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");_.Kc(a,By({style:c}));return _.Tr(a.firstChild,"transition")!=""});
_.Ey=function(a,b){_.Ej.call(this);this.Em=a||1;this.mx=b||_.Xa;this.QP=(0,_.Ab)(this.Eha,this);this.EW=_.pd()};_.cb(_.Ey,_.Ej);_.g=_.Ey.prototype;_.g.enabled=!1;_.g.Fc=null;_.g.setInterval=function(a){this.Em=a;this.Fc&&this.enabled?(this.stop(),this.start()):this.Fc&&this.stop()};
_.g.Eha=function(){if(this.enabled){var a=_.pd()-this.EW;a>0&&a<this.Em*.8?this.Fc=this.mx.setTimeout(this.QP,this.Em-a):(this.Fc&&(this.mx.clearTimeout(this.Fc),this.Fc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Fc||(this.Fc=this.mx.setTimeout(this.QP,this.Em),this.EW=_.pd())};_.g.stop=function(){this.enabled=!1;this.Fc&&(this.mx.clearTimeout(this.Fc),this.Fc=null)};_.g.ua=function(){_.Ey.N.ua.call(this);this.stop();delete this.mx};
_.Fy=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.Ab)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.Ab)(a.handleEvent,a);else throw Error("wa");return Number(b)>2147483647?-1:_.Xa.setTimeout(a,b||0)};_.Gy=function(a){_.Xa.clearTimeout(a)};
_.Iy=function(){_.Hy="oauth2relay"+String(2147483647*(0,_.Ng)()|0)};_.Jy=new _.ty;_.Ky=new _.sy("oauth");_.Iy();_.Ze("oauth-flow/client_id");var Ly=String(_.Ze("oauth-flow/redirectUri"));if(Ly)Ly.replace(/[#][\s\S]*/,"");else{var My=_.Fg.getOrigin(window.location.href);_.Ze("oauth-flow/callbackUrl");encodeURIComponent(My)}_.Fg.getOrigin(window.location.href);
var Oy,Py,Qy,Ry,Sy,Ty,Uy,Vy,Wy,Xy,Yy,$y,az,bz,cz,dz,nz,oz,pz,qz,rz,sz,tz,uz,vz,wz,xz,yz,zz,Az,Bz,Cz,Dz,Ez,Fz,Gz,Hz,Iz,Jz,Kz,Lz,Oz,Nz,Pz,Qz,Rz,Sz,Tz,Uz,Vz;_.Ny=function(a,b){if(_.Hh&&!b)return _.Xa.atob(a);var c="";_.Kh(a,function(d){c+=String.fromCharCode(d)});return c};Oy=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return b==="true"||a==="none"};Py=function(a){return _.ai("enableMultilogin")&&a("cookie_policy")&&!Oy(a)?!0:!1};
Sy=function(){var a,b=null;_.iy.iterate(function(c,d){c.indexOf("G_AUTHUSER_")===0&&(c=c.substring(11),c=_.Yx(c),!a||c.ef&&!a.ef||c.ef==a.ef&&c.Ui>a.Ui)&&(a=c,b=d)});return{Q7:a,authuser:b}};Ty=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];Uy=function(a){a=a.toUpperCase();for(var b=Ty.length,c=0;c<b;++c){var d=a.split(Ty[c]);d.length==2&&d[1]===""&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();a.length>40&&(b=new _.Mg,b.Bx(a),a=b.Ti().toUpperCase());return a};
Vy=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};Wy=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],nga:Vy(a[1]),fsa:Vy(a[2]),Xqa:Vy(a[3])}};Xy=function(a){var b=Sy(),c=b.Q7;b=b.authuser;var d=a&&Uy(a);if(b!==null){var e;_.iy.iterate(function(h,k){(h=_.$x(h))&&h.yj&&(d&&h.p8!=d||h.ef==c.ef&&h.Ui==c.Ui&&(e=k))});if(e){var f=Wy(e);a=f&&f.nga[Number(b)];f=f&&f.clientId;if(a)return{authuser:b,dta:a,clientId:f}}}return null};
Yy=function(a,b){a=_.qi(a);if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.Zy=function(a,b){if(b){var c=b;var d=a}else typeof a==="string"?d=a:c=a;c?_.aw(c,d):_.bw(d)};
$y=function(a){if(!a)return null;a!=="single_host_origin"&&(a=_.Hg(a));var b=window.location.hostname,c=b,d=_.Zx;if(a!=="single_host_origin"){c=a.split("://");if(c.length==2)d=c.shift()==="https";else return _.Vf.log("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(c.indexOf(":")!==-1)c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Vf.log("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,ef:d,Ui:b}};
az=function(a){var b=$y(a);if(!b)return new _.dy("G_USERSTATE_");a=["G_USERSTATE_",_.Zx&&b.ef?"S":"H",b.Ui].join("");var c=_.my[a];c||(c={aJ:63072E3},_.He(_.py(b),c),c=new _.ay(a,c),_.my[a]=c,b=c.read(),typeof b!=="undefined"&&b!==null&&(document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};bz=function(a){var b=az(a).read();a=_.Fe();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
cz=function(a,b,c){var d=bz(b),e=d[a];d[a]="0";var f=[];_.Bm(d,function(k,l){f.push(l+"="+k)});var h=f.join(":");b=az(b);h?b.write(h):b.clear();d[a]!==e&&c&&c()};dz=function(a,b){b=bz(b);return b[a]=="0"||b[a]=="X"};nz=function(a){a=$y(a.g_user_cookie_policy);if(!a||a.ef&&!_.Zx)a=null;else{var b=["G_AUTHUSER_",_.Zx&&a.ef?"S":"H",a.Ui].join(""),c=_.ly[b];c||(c=new _.iy(b,_.py(a)),_.ly[b]=c);a=c}_.$e("googleapis.config/sessionIndex",null);a.clear()};oz=function(a){return Oy(function(b){return a[b]})};
pz=0;qz=!1;rz=[];sz={};tz={};uz=null;vz=function(a){var b=_.Hy;return function(c){if(this.f==b&&this.t==_.$f.Sn(this.f)&&this.origin==_.$f.ho(this.f))return a.apply(this,arguments)}};wz=function(a){if(a&&!decodeURIComponent(a).startsWith("m;/_/scs/"))throw Error("ya");};xz=function(a){var b=_.cf.Pg,c=b(a).jsh;if(c!=null)return wz(c),a;if(b=String(b().jsh||_.Pe.h||""))wz(b),c=(a+"#").indexOf("#"),a=a.substr(0,c)+(a.substr(0,c).indexOf("?")!==-1?"&":"?")+"jsh="+encodeURIComponent(b)+a.substr(c);return a};
yz=function(){return!!_.Ze("oauth-flow/usegapi")};zz=function(a,b){yz()?uz.unregister(a):_.$f.unregister(a+":"+b)};Az=function(a,b,c){yz()?uz.register(a,c,_.Pm):_.$f.register(a+":"+b,vz(c))};Bz=function(){Qy.parentNode.removeChild(Qy)};
Cz=function(a){var b=Qy;_.Cy(b,[{property:"-webkit-transform",duration:1,timing:"ease",delay:0}]);_.Cy(b,[{property:"transform",duration:1,timing:"ease",delay:0}]);_.Fy(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};Dz=function(){var a=Ry+88;Cz(a);Ry=a};Ez=function(){var a=Ry-88;Cz(a);Ry=a};
Fz=function(a){var b=a?Dz:Ez,c=a?Ez:Dz;a=a?"-":"";Ry=parseInt(a+88,10);Qy.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";Qy.style.transform="translate3d(0px,"+a+88+"px,0px)";Qy.style.display="";Qy.style.visibility="visible";b();_.Fy(c,4E3);_.Fy(Bz,5E3)};
Gz=function(a){var b=_.Ze("oauth-flow/toast/position");b!=="top"&&(b="bottom");var c=document.createElement("div");Qy=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.Rr(c,"visibility","hidden");_.Rr(c,b,"-40px");_.Rr(c,"height","128px");var d=c;if(_.mr()){d=document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";d.appendChild(e);d=e}e=
b=="top"?"-":"";Ry=parseInt(e+88,10);Qy.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";Qy.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.Mm.openChild({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){b==="top"?Fz(!0):Fz(!1)}})};
Hz=function(a){var b=_.ro(),c=b&&b.scope;b=a&&a.scope;b=typeof b==="string"?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];_.zm.call(b,e)==-1&&b.push(e)}b.length>0&&(a.scope=b.join(" "))}return a};
Iz=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(Math.floor((new Date).getTime()/1E3)));var d=parseInt(b.expires_in,10)||86400;b.error&&(d=_.Ze("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(Math.floor((new Date).getTime()/1E3)+d));
b._aa||b.error||Xy(c)!=null||!oz(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};Jz=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+)\.?\.?\.?$/,"$1");a=_.Qf(_.Ny(a,!0));if(a===!1)throw Error("za");return a};Kz=function(a){return(a=Jz(a))?a.sub:null};
Lz=function(a){a&&rz.push(a);a=_.Hy;var b=document.getElementById(a),c=(new Date).getTime();if(b){if(pz&&c-pz<6E4)return;var d=_.$f.Sn(a);d&&(zz("oauth2relayReady",d),zz("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.Iy();a=_.Hy}pz=c;var e=String(2147483647*(0,_.Ng)()|0);b=_.Ze("oauth-flow/proxyUrl")||_.Ze("oauth-flow/relayUrl");yz()?uz=_.Mm.openChild({where:_.cf.nT(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",encodeURIComponent(_.Fg.getOrigin(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.cf.nT(),d=_.cf.EQ({name:a,id:a}),d.src=xz(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,d.setAttribute("aria-hidden","true"),c.appendChild(d),_.$f.Mw(a));Az("oauth2relayReady",e,function(){zz("oauth2relayReady",e);var f=rz;if(f!==null){rz=
null;for(var h=f.length,k=0;k<h;++k)f[k]()}});Az("oauth2callback",e,function(f){var h=_.cf.Pg;h=h(f);var k=h.state;f=k;f=f.replace(/\|.*$/,"");f={}.hasOwnProperty.call(tz,f)?tz[f]:null;h.state=f;if(h.state!=null){f=sz[k];delete sz[k];k=f&&f.key||"token";var l=h=Iz(f&&f.params,h);var m=(m=Kz(l))?dz(m,l.cookie_policy):!1;!m&&l&&(" "+(l.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")>=0&&_.Ze("isLoggedIn")&&(l&&l._aa)==="1"&&(l._aa="0",qz||(qz=!0,Gz(l)));_.Zy(k,h);h=Yy(k);if(f){k=
f.popup;l=f.after_redirect;if(k&&"keep_open"!=l)try{k.close()}catch(n){}f.callback&&(f.callback(h),f.callback=null)}}})};_.Mz=function(a){rz!==null?Lz(a):a&&a()};Oz=function(a,b){var c=Nz,d=Kz(a);d&&(nz(a),cz(d,b,function(){if(c){var e={error:"user_signed_out"};e.client_id=a.client_id;e.g_user_cookie_policy=a.g_user_cookie_policy;e.scope=a.scope;e.response_type=a.response_type;e.session_state=a.session_state;e=Iz(null,e);c(e)}}))};
Nz=function(a){a||(a=Yy(void 0,!0));a&&typeof a==="object"||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.Pe.drw=null);_.Zy(a);if(b=a.authuser)_.Ze("googleapis.config/sessionIndex"),_.$e("googleapis.config/sessionIndex",b);_.Jy.ep(_.Ky,a);return a};Pz=["client_id","cookie_policy","response_type"];Qz="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect access_type hl state".split(" ");
Rz=function(a){var b=_.Tj(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=parseInt(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=parseInt(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.aw(b);return b};
Sz=function(a){if(a.include_granted_scopes===void 0){var b=_.Ze("include_granted_scopes");a.include_granted_scopes=!!b}};Tz=function(a){window.console&&(typeof window.console.warn==="function"?window.console.warn(a):typeof window.console.log==="function"&&window.console.log(a))};
Uz=function(a){var b=a||{},c={};_.Cb(Qz,function(d){b[d]!=null&&(c[d]=b[d])});a=_.Ze("googleapis/overrideClientId");a!=null&&(c.client_id=a);Sz(c);typeof b.scope==="string"?c.scope=b.scope:Array.isArray(b.scope)&&(c.scope=b.scope.join(" "));b["openid.realm"]!=null&&(c.openid_realm=b["openid.realm"]);b.cookie_policy!=null?c.cookie_policy=b.cookie_policy:b.cookiepolicy!=null&&(c.cookie_policy=b.cookiepolicy);c.login_hint==null&&b.user_id!=null&&(c.login_hint=b.user_id);try{_.Uw(c.cookie_policy)}catch(d){c.cookie_policy&&
Tz("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}b.hd!=null&&(c.hosted_domain=b.hd);c.prompt==null&&(b.immediate==1||b.immediate=="true"?c.prompt="none":b.approval_prompt=="force"&&(c.prompt="consent"));c.prompt=="none"&&(c.session_selection="first_valid");c.prompt=="none"&&c.access_type=="offline"&&delete c.access_type;typeof c.authuser==="undefined"&&(a=_.ei(),a!=null&&(c.authuser=a));a=b.redirect_uri||_.Ze("oauth-flow/redirectUri");
a!=null&&a!="postmessage"&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c};
Vz=function(a,b){var c=Uz(a),d=new _.lk(function(e,f){_.Ax(c,function(h){var k=h||{};_.Cb(Pz,function(l){k[l]==null&&(k[l]=c[l])});!c.include_granted_scopes&&a&&a.scope&&(k.scope=a.scope);a&&a.state!=null&&(k.state=a.state);k.error?(c.prompt=="none"&&k.error=="user_logged_out"&&(k.error="immediate_failed_user_logged_out"),f(k)):(h=Rz(k),h.authuser!=null&&_.$e("googleapis.config/sessionIndex",h.authuser),e(h))})});b&&d.then(b,b);return d};var Wz,Yz;Wz=null;_.Zz=function(a,b){if(a.approvalprompt!=="force"){a=_.Xz(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)Wz?(a.client_id!==Wz.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(Wz=a,b=!1);b||Yz(a)}};
_.Xz=function(a){var b=a.redirecturi||"postmessage",c=_.Ec((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c=a.accesstype=="offline"?!0:(c=a.redirecturi)&&c!="postmessage";c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=_.Ec(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&a.cookiepolicy!=="none"&&(b.cookie_policy=a.cookiepolicy);typeof a.includegrantedscopes!="undefined"&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.Ze("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.Ze("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};Yz=function(a){_.dp("waaf0","signin","0");Vz(a,function(b){_.dp("waaf1","signin","0");Nz(b)})};
_.$z=function(a){a=_.Xz(a);_.$e("oauth-flow/authWindowWidth",445);_.$e("oauth-flow/authWindowHeight",615);Yz(a)};_.aA=function(a){_.Jy.unsubscribe(_.Ky,a);_.Jy.subscribe(_.Ky,a)};var hA,kA;_.cA=function(a){return a.cookiepolicy?!0:(_.bA("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.bA=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.gA=function(a,b){var c=_.ro();_.He(a,c);c=Hz(c);if(_.cA(c)){var d=_.dA();_.eA(c);b?_.Oe(b,"click",function(){_.fA(c,d)}):_.fA(c,d)}};
_.dA=function(){var a=new hA;_.aA(function(b){a.bJ&&b&&(b.access_token&&_.$e("isPlusUser",!0),b["g-oauth-window"]&&(a.bJ=!1,_.Vf.warn("OTA app install is no longer supported.")))});return a};hA=function(){this.bJ=!1};_.eA=function(a){a=_.iA(a);_.jA(a.callback);_.Mz(function(){_.Zz(a)})};_.iA=function(a){kA(a);a.redirecturi&&delete a.redirecturi;Py(function(b){return a[b]})||(a.authuser=0);return a};kA=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")};
_.jA=function(a){if(typeof a==="string")if(window[a])a=window[a];else{_.bA('Callback function named "'+a+'" not found');return}a&&_.aA(a)};_.fA=function(a,b){b.bJ=!0;a=_.iA(a);_.$z(a)};_.r("gapi.auth.authorize",Vz);_.r("gapi.auth.checkSessionState",function(a,b){var c=_.Fe();c.client_id=a.client_id;c.session_state=a.session_state;_.Mz(function(){yz()?uz.send("check_session_state",c,function(d){b.call(null,d[0])},_.Pm):_.$f.call(_.Hy,"check_session_state",vz(function(d){b.call(null,d)}),c.session_state,c.client_id)})});_.r("gapi.auth.getAuthHeaderValueForFirstParty",function(a,b){_.xi(_.wi(),51).rb();return _.oi(a,b)});_.r("gapi.auth.getToken",Yy);
_.r("gapi.auth.getVersionInfo",function(a,b){_.Mz(function(){var c=_.oi()||"",d=null,e=null;c&&(e=c.split(" "),e.length==2&&(d=e[1]));d?yz()?uz.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(f){a(f[0])},_.Pm):_.$f.call(_.Hy,"get_versioninfo",vz(function(f){a(f)}),d,b):a()})});_.r("gapi.auth.init",_.Mz);_.r("gapi.auth.setToken",_.Zy);_.r("gapi.auth.signIn",function(a){_.gA(a)});_.r("gapi.auth.signOut",function(){var a=Yy();a&&Oz(a,a.cookie_policy)});
_.r("gapi.auth.unsafeUnpackIdToken",Jz);_.r("gapi.auth._pimf",_.Zz);_.r("gapi.auth._oart",Gz);_.r("gapi.auth._guss",function(a){return az(a).read()});
var lA=_.ro();lA.clientid&&lA.scope&&lA.callback&&!_.Ze("disableRealtimeCallback")&&_.eA(lA);
var Fx=function(){};var Hx;Hx=function(){};_.cb(Hx,Fx);Hx.prototype.Vy=function(){return new XMLHttpRequest};_.Gx=new Hx;
_.Ig=window.googleapis&&window.googleapis.server||{};
var Og=function(a){return{execute:function(b){var c={method:a.httpMethod||"GET",root:a.root,path:a.url,params:a.urlParams,headers:a.headers,body:a.body},d=window.gapi,e=function(){var f=d.config.get("client/apiKey"),h=d.config.get("client/version");try{var k=d.config.get("googleapis.config/developerKey"),l=d.config.get("client/apiKey",k);d.config.update("client/apiKey",l);d.config.update("client/version","1.0.0-alpha");var m=d.client;m.request.call(m,c).then(b,b)}finally{d.config.update("client/apiKey",
f),d.config.update("client/version",h)}};d.client?e():d.load.call(d,"client",e)}}},Pg=function(a,b){return function(c){var d={};c=c.body;var e=_.Qf(c),f={};if(e&&e.length)for(var h=e.length,k=0;k<h;++k){var l=e[k];f[l.id]=l}h=b.length;for(k=0;k<h;++k)l=b[k].id,d[l]=e&&e.length?f[l]:e;a(d,c)}},Qg=function(a){a.transport={name:"googleapis",execute:function(b,c){for(var d=[],e=b.length,f=0;f<e;++f){var h=b[f],k=h.method,l=String(k).split(".")[0];l=_.Ze("googleapis.config/versions/"+k)||_.Ze("googleapis.config/versions/"+
l)||"v1";d.push({jsonrpc:"2.0",id:h.id,method:k,apiVersion:String(l),params:h.params})}b=Og({httpMethod:"POST",root:a.transport.root,url:"/rpc?pp=0",headers:{"Content-Type":"application/json"},body:d});b.execute.call(b,Pg(c,d))},root:void 0}},Rg=function(a){var b=this.method,c=this.transport;c.execute.call(c,[{method:b,id:b,params:this.rpc}],function(d){d=d[b];d.error||(d=d.data||d.result);a(d)})},Tg=function(){for(var a=Sg,b=a.split("."),c=function(k){k=k||{};k.groupId=k.groupId||"@self";k.userId=
k.userId||"@viewer";k={method:a,rpc:k||{}};Qg(k);k.execute=Rg;return k},d=_.Xa,e=b.length,f=0;f<e;++f){var h=d[b[f]]||{};f+1==e&&(h=c);d=d[b[f]]=h}if(b.length>1&&b[0]!="googleapis")for(b[0]="googleapis",b[b.length-1]=="delete"&&(b[b.length-1]="remove"),d=_.Xa,e=b.length,f=0;f<e;++f)h=d[b[f]]||{},f+1==e&&(h=c),d=d[b[f]]=h},Sg;for(Sg in _.Ze("googleapis.config/methods"))Tg();_.r("googleapis.newHttpRequest",function(a){return Og(a)});_.r("googleapis.setUrlParameter",function(a,b){if(a!=="trace")throw Error("u");_.$e("client/trace",b)});
_.Lh=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Mh=function(a){return a==null?"":String(a)};_.Nh=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Oh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Ph=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Qh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Qh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Rh=function(a){var b=[],c;for(c in a)_.Qh(c,a[c],b);return b.join("&")};
_.Sh=function(a,b){b=_.Rh(b);return _.Ph(a,b)};
var Ki,Li;_.Ji=function(a){var b={SAPISIDHASH:!0,SAPISID3PHASH:!0,SAPISID1PHASH:!0,APISIDHASH:!0};return a&&(a.OriginToken||a.Authorization&&b[String(a.Authorization).split(" ")[0]])?!0:!1};Ki={NU:_.Ji,Fca:_.mi,NT:function(){var a=null;_.mi()&&(a=window.__PVT,a==null&&(a=(new _.fi(document)).get("BEAT")));return a},a$:_.oi};
Li=function(a,b){a=_.cf.EQ({id:a,name:a});a.style.width="1px";a.style.height="1px";a.style.position="absolute";a.style.top="-100px";a.style.display="none";if(window.navigator){var c=window.navigator.userAgent||"";var d=window.navigator.product||"";c=c.indexOf("Opera")!=0&&c.indexOf("WebKit")==-1&&d=="Gecko"&&c.indexOf("rv:1.")>0}else c=!1;a.src=c?"about:blank":b;a.tabIndex=-1;typeof a.setAttribute==="function"?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true";document.body.appendChild(a);
c&&(a.src=b);return a};Ki={NU:_.Ji,Fca:_.mi,NT:function(){var a=null;_.mi()&&(a=window.__PVT,a==null&&(a=(new _.fi(document)).get("BEAT")));return a},a$:_.oi};var Ni,Mi;Ni=function(){return!!Mi("auth/useFirstPartyAuthV2")};Mi=function(a){return _.Ze("googleapis.config/"+a)};
_.Oi=function(a,b,c){a=a===void 0?{}:a;b=b===void 0?window.location.href:b;c=c===void 0?"auto":c;if(c=="none")return a;var d=a.Authorization,e=a.OriginToken;if(!d&&!e){(e=_.qi())&&e.access_token&&(c=="oauth2"||c=="auto")&&(d=String(e.token_type||"Bearer")+" "+e.access_token);if(e=!d)e=(!!Mi("auth/useFirstPartyAuth")||c=="1p")&&c!="oauth2";if(e&&_.mi()){if(Ni()){d=Mi("primaryEmail");c=Mi("appDomain");e=Mi("fogId");var f=[];d&&f.push({key:"e",value:d});c&&f.push({key:"a",value:c});e&&f.push({key:"u",
value:e});d=_.oi(f)}else d=_.oi();d&&(c=a["X-Goog-AuthUser"],b=_.ei(b),b=c||b,_.Cc(_.Mh(b))&&(!Ni()||Ni()&&_.Cc(_.Mh(Mi("primaryEmail")))&&_.Cc(_.Mh(Mi("appDomain")))&&_.Cc(_.Mh(Mi("fogId"))))&&(b="0"),_.Cc(_.Mh(b))||(a["X-Goog-AuthUser"]=b))}d?a.Authorization=d:Mi("auth/useOriginToken")!==!1&&(e=Ki.NT())&&(a.OriginToken=e)}return a};_.Pi=function(){function a(n,p,q,t,v){var u=f("proxy");if(t||!u){u=f("root");var x=f("root-1p")||u;u=u||"https://content.googleapis.com";x=x||"https://clients6.google.com";var A=f("xd3")||"/static/proxy.html";u=(t||String(p?x:u))+A}u=String(u);q&&(u+=(u.indexOf("?")>=0?"&":"?")+"usegapi=1");(p=_.cf.Pg().jsh||_.Pe.h)&&(u+=(u.indexOf("?")>=0?"&":"?")+"jsh="+encodeURIComponent(p));u+="#parent="+encodeURIComponent(v!=null?String(v):_.Fg.getOrigin(document.location.href));return u+("&rpctoken="+n)}function b(n,
p,q,t,v){var u=d(q,t,v);k[u]||(q=Li(u,p),_.$f.register("ready:"+n,function(){_.$f.unregister("ready:"+n);if(!l[u]){l[u]=!0;var x=m[u];m[u]=[];for(var A=0,C=x.length;A<C;++A){var D=x[A];e(D.lp,D.mfa,D.callback)}}}),_.$f.Mw(u,p),k[u]=q)}function c(n,p,q){var t=String(2147483647*_.Fi()|0),v=a(t,n,p,q);_.Xf(function(){b(t,v,n,p,q)})}function d(n,p,q){n=a("",n,p,q,"");q=h[n+p];if(!q){q=new _.Mg;q.Bx(n);q=q.Ti().toLowerCase();var t=_.Fi();q+=t;h[n+p]=q}return"apiproxy"+q}function e(n,p,q){var t=void 0,
v=!1;if(n!=="makeHttpRequests")throw'only "makeHttpRequests" RPCs are implemented';var u=function(P){if(P){if(typeof t!="undefined"&&typeof P.root!="undefined"&&t!=P.root)throw"all requests in a batch must have the same root URL";t=P.root||t;v=Ki.NU(P.headers)}};if(p)for(var x=0,A=p.length;x<A;++x){var C=p[x];C&&u(C.params)}u=!!f("useGapiForXd3");var D=d(v,u,t);k[D]||c(v,u,t);l[D]?_.$f.call(D,n,function(P){if(this.f==D&&this.t==_.$f.Sn(this.f)&&this.origin==_.$f.ho(this.f)){var F=_.Qf(P);q(F,P)}},
p):(m[D]||(m[D]=[]),m[D].push({lp:n,mfa:p,callback:q}))}function f(n){return _.Ze("googleapis.config/"+n)}var h={},k={},l={},m={};return{kqa:function(n,p,q){return _.Oi(n,p,q)},Zm:e}}();
var Ug={Bia:"Authorization",d3:"Content-ID",Zia:"Content-Transfer-Encoding",aja:"Content-Type",Fja:"Date",wma:"OriginToken",Tka:"hotrod-board-name",Uka:"hotrod-chrome-cpu-model",Vka:"hotrod-chrome-processors",Noa:"User-Agent",ipa:"WWW-Authenticate",kpa:"X-Ad-Manager-Impersonation",jpa:"X-Ad-Manager-Debug-Info",mpa:"X-ClientDetails",npa:"X-Cloudaicompanion-Trace-Id",opa:"X-Compass-Routing-Destination",rpa:"X-Goog-AuthUser",xpa:"X-Goog-Encode-Response-If-Executable",ppa:"X-Google-Consent",qpa:"X-Google-EOM",
zpa:"X-Goog-Meeting-ABR",Apa:"X-Goog-Meeting-Botguardid",Bpa:"X-Goog-Meeting-Bot-Info",Cpa:"X-Goog-Meeting-ClientInfo",Dpa:"X-Goog-Meeting-ClientVersion",Epa:"X-Goog-Meeting-Debugid",Fpa:"X-Goog-Meeting-Identifier",Gpa:"X-Goog-Meeting-Interop-Cohorts",Hpa:"X-Goog-Meeting-Interop-Type",Ipa:"X-Goog-Meeting-OidcIdToken",Jpa:"X-Goog-Meeting-RtcClient",Kpa:"X-Goog-Meeting-StartSource",Lpa:"X-Goog-Meeting-Token",Mpa:"X-Goog-Meeting-Viewer-Token",Npa:"X-Goog-PageId",Ppa:"X-Goog-Safety-Content-Type",Qpa:"X-Goog-Safety-Encoding",
tpa:"X-Goog-Drive-Client-Version",upa:"X-Goog-Drive-Resource-Keys",Rpa:"X-HTTP-Method-Override",Spa:"X-JavaScript-User-Agent",Tpa:"X-Origin",Upa:"X-Referer",Vpa:"X-Requested-With",Ypa:"X-Use-HTTP-Status-Code-Override",Wpa:"X-Server-Timeout",ypa:"X-Goog-First-Party-Reauth",Xpa:"X-Server-Token",spa:"x-goog-chat-space-id",Opa:"x-goog-pan-request-context",lpa:"X-AppInt-Credentials",wpa:"X-Goog-Earth-Gcp-Project",vpa:"X-Goog-Earth-Client-Metadata"},Vg="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding User-Agent Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-AppId X-Firebase-AppVersion X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-198889211-bin x-goog-ext-*********-bin x-goog-ext-223261916-bin x-goog-ext-*********-bin x-goog-ext-233818517-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-maps-session-id x-goog-maps-traffic-policy x-goog-gmp-client-signals x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Places-Ios-Sdk X-Android-Package X-Android-Cert X-Places-Android-Sdk X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-Youtube-Client-Version X-Youtube-Lava-Device-Context X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Label X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context X-AppInt-Credentials X-Goog-Earth-Gcp-Project X-Goog-Earth-Client-Metadata".split(" "),
Wg="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Cloudaicompanion-Trace-Id X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination x-goog-ext-*********-bin x-goog-ext-*********-bin".split(" ");var Xg,Yg,Zg,$g,bh,ch,dh,eh,fh,gh,hh,ih;Xg=null;Yg=null;Zg=null;$g=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.ah=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};bh={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ch={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
dh=function(a){if(!_.od(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();$g(d,e)&&(b[e]=d)}}for(var f in Ug)Object.prototype.hasOwnProperty.call(Ug,f)&&(a=Ug[f],c=a.toLowerCase(),$g(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};eh=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");fh=/[ \t]*(\r?\n[ \t]+)+/g;gh=/^[ \t]+|[ \t]+$/g;
hh=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=hh(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(fh," "),a=a.replace(gh,""),a.replace(eh,"")==""&&a))return a};ih=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.jh=function(a){if(typeof a!=="string"||!a||!a.match(ih))return null;a=a.toLowerCase();if(Zg==null){var b=[],c=_.Ze("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ze("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Wg);(c=_.Ze("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ze("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Vg);for(var d in Ug)Object.prototype.hasOwnProperty.call(Ug,d)&&b.push(Ug[d]);Zg=dh(b)}return Zg!=null&&Zg.hasOwnProperty(a)?Zg[a]:a};
_.kh=function(a,b){if(!_.jh(a)||!hh(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ch[a])return null;if(Xg==null){b=[];var c=_.Ze("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ze("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Vg);Xg=dh(b)}return Xg!=null&&Xg.hasOwnProperty(a)?Xg[a]:null};
_.lh=function(a,b){if(!_.jh(a)||!hh(b))return null;a=a.toLowerCase();if(bh[a])return null;if(Yg==null){b=[];var c=_.Ze("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Ze("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Wg);Yg=dh(b)}return Yg!=null&&Yg.hasOwnProperty(a)?a:null};
_.mh=function(a,b){if(_.jh(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&$g(d,b)){var e=hh(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.nh=function(a,b,c,d){var e=_.jh(b);if(e){c&&(c=hh(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&$g(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.oh=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.jh(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=hh(f))if(k=_.lh(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.mh(c,k),h!==void 0&&(f=h+", "+f),_.nh(c,k,f,!0)}}}return c};
/\uffff/.test("\uffff");
var Jx;_.Ix=function(a){var b=0,c;for(c in a)b++;return b};Jx=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)_.Qh(a[b],a[b+1],c);return c.join("&")};_.Kx=function(a,b){var c=arguments.length==2?Jx(arguments[1],0):Jx(arguments,1);return _.Ph(a,c)};_.Lx=function(a,b){_.Cj(a,"/")&&(a=a.slice(0,-1));_.Bc(b,"/")&&(b=b.slice(1));return a+"/"+b};_.Mx=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};var Ox,Px,Qx;_.Nx=function(a){_.Ej.call(this);this.headers=new Map;this.z2=a||null;this.Uf=!1;this.Va=null;this.nB="";this.rr=0;this.Do=this.dI=this.NA=this.cG=!1;this.Ls=0;this.Pc=null;this.Um="";this.xh=!1;this.RE=this.FN=null};_.cb(_.Nx,_.Ej);_.Nx.prototype.Ab=null;Ox=/^https?$/i;Px=["POST","PUT"];Qx=[];_.Rx=function(a,b,c,d,e,f,h){var k=new _.Nx;Qx.push(k);b&&k.ta("complete",b);k.zr("ready",k.j8);f&&k.sD(f);h&&(k.xh=h);k.send(a,c,d,e)};_.g=_.Nx.prototype;
_.g.j8=function(){this.dispose();_.Xi(Qx,this)};_.g.sD=function(a){this.Ls=Math.max(0,a)};_.g.setTrustToken=function(a){this.FN=a};_.g.setAttributionReporting=function(a){this.RE=a};
_.g.send=function(a,b,c,d){if(this.Va)throw Error("ta`"+this.nB+"`"+a);b=b?b.toUpperCase():"GET";this.nB=a;this.rr=0;this.cG=!1;this.Uf=!0;this.Va=this.z2?this.z2.Vy():_.Gx.Vy();this.Va.onreadystatechange=(0,_.ck)((0,_.Ab)(this.VX,this));try{this.dI=!0,this.Va.open(b,String(a),!0),this.dI=!1}catch(h){this.sz(5,h);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=_.za(d.keys());
for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("ua`"+String(d));d=Array.from(c.keys()).find(function(h){return"content-type"==h.toLowerCase()});e=_.Xa.FormData&&a instanceof _.Xa.FormData;!_.qb(Px,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.za(c);for(d=b.next();!d.done;d=b.next())c=_.za(d.value),d=c.next().value,c=c.next().value,this.Va.setRequestHeader(d,c);this.Um&&(this.Va.responseType=this.Um);"withCredentials"in this.Va&&
this.Va.withCredentials!==this.xh&&(this.Va.withCredentials=this.xh);if("setTrustToken"in this.Va&&this.FN)try{this.Va.setTrustToken(this.FN)}catch(h){}if("setAttributionReporting"in this.Va&&this.RE)try{this.Va.setAttributionReporting(this.RE)}catch(h){}try{this.Pc&&(clearTimeout(this.Pc),this.Pc=null),this.Ls>0&&(this.Pc=setTimeout(this.Ii.bind(this),this.Ls)),this.NA=!0,this.Va.send(a),this.NA=!1}catch(h){this.sz(5,h)}};
_.g.Ii=function(){typeof _.Ua!="undefined"&&this.Va&&(this.rr=8,this.dispatchEvent("timeout"),this.abort(8))};_.g.sz=function(a){this.Uf=!1;this.Va&&(this.Do=!0,this.Va.abort(),this.Do=!1);this.rr=a;Sx(this);Tx(this)};var Sx=function(a){a.cG||(a.cG=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};_.Nx.prototype.abort=function(a){this.Va&&this.Uf&&(this.Uf=!1,this.Do=!0,this.Va.abort(),this.Do=!1,this.rr=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Tx(this))};
_.Nx.prototype.ua=function(){this.Va&&(this.Uf&&(this.Uf=!1,this.Do=!0,this.Va.abort(),this.Do=!1),Tx(this,!0));_.Nx.N.ua.call(this)};_.Nx.prototype.VX=function(){this.isDisposed()||(this.dI||this.NA||this.Do?Ux(this):this.GJ())};_.Nx.prototype.GJ=function(){Ux(this)};
var Ux=function(a){if(a.Uf&&typeof _.Ua!="undefined")if(a.NA&&_.Vx(a)==4)setTimeout(a.VX.bind(a),0);else if(a.dispatchEvent("readystatechange"),_.Vx(a)==4){a.Uf=!1;try{a.qr()?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.rr=6,a.getStatus(),Sx(a))}finally{Tx(a)}}},Tx=function(a,b){if(a.Va){a.Pc&&(clearTimeout(a.Pc),a.Pc=null);var c=a.Va;a.Va=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}};_.Nx.prototype.isActive=function(){return!!this.Va};
_.Nx.prototype.qr=function(){var a=this.getStatus(),b;if(!(b=_.Mx(a))){if(a=a===0)a=String(this.nB).match(_.Oh)[1]||null,!a&&_.Xa.self&&_.Xa.self.location&&(a=_.Xa.self.location.protocol.slice(0,-1)),a=!Ox.test(a?a.toLowerCase():"");b=a}return b};_.Vx=function(a){return a.Va?a.Va.readyState:0};_.Nx.prototype.getStatus=function(){try{return _.Vx(this)>2?this.Va.status:-1}catch(a){return-1}};_.Wx=function(a){try{return a.Va?a.Va.responseText:""}catch(b){return""}};
_.Xx=function(a){try{if(!a.Va)return null;if("response"in a.Va)return a.Va.response;switch(a.Um){case "":case "text":return a.Va.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.Va)return a.Va.mozResponseArrayBuffer}return null}catch(b){return null}};_.Nx.prototype.getResponseHeader=function(a){if(this.Va&&_.Vx(this)==4)return a=this.Va.getResponseHeader(a),a===null?void 0:a};
_.Nx.prototype.getAllResponseHeaders=function(){return this.Va&&_.Vx(this)>=2?this.Va.getAllResponseHeaders()||"":""};_.cj(function(a){_.Nx.prototype.GJ=a(_.Nx.prototype.GJ)});
var Qt,Vt;_.Mt=function(a,b){var c=_.od(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(a==null)return;a=a[d[c]]}return a};
_.Nt=function(a){if(!a||typeof a!=="object")return a;if(typeof a.clone==="function")return a.clone();if(typeof Map!=="undefined"&&a instanceof Map)return new Map(a);if(typeof Set!=="undefined"&&a instanceof Set)return new Set(a);if(a instanceof Date)return new Date(a.getTime());var b=Array.isArray(a)?[]:typeof ArrayBuffer!=="function"||typeof ArrayBuffer.isView!=="function"||!ArrayBuffer.isView(a)||a instanceof DataView?{}:new a.constructor(a.length),c;for(c in a)b[c]=_.Nt(a[c]);return b};
_.Ot=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.pd()).toString(36)};_.Pt=function(a,b,c){return _.ke(document,arguments)};Qt=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
_.Rt=function(a,b,c){for(var d=0,e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)return d;d+=e+1}return-1};_.St=/#|$/;_.Tt=function(a){if(a.Ye&&typeof a.Ye=="function")return a.Ye();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(_.od(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.sb(a)};
_.Ut=function(a){if(a.ig&&typeof a.ig=="function")return a.ig();if(!a.Ye||typeof a.Ye!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(_.od(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.Lh(a)}}};
Vt=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(_.od(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=_.Ut(a),e=_.Tt(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};var hu,cu,lu,du,fu,eu,iu,gu,mu;
_.Wt=function(a,b){this.Xd=this.wh=this.Di="";this.yg=null;this.wG=this.Om="";this.Rg=!1;var c;a instanceof _.Wt?(this.Rg=b!==void 0?b:a.Rg,_.Xt(this,a.Di),_.Yt(this,a.wh),_.Zt(this,a.Lg()),_.$t(this,a.yg),this.setPath(a.getPath()),_.au(this,a.Qd.clone()),this.Sk(a.Tz())):a&&(c=String(a).match(_.Oh))?(this.Rg=!!b,_.Xt(this,c[1]||"",!0),_.Yt(this,c[2]||"",!0),_.Zt(this,c[3]||"",!0),_.$t(this,c[4]),this.setPath(c[5]||"",!0),_.au(this,c[6]||"",!0),this.Sk(c[7]||"",!0)):(this.Rg=!!b,this.Qd=new _.bu(null,
this.Rg))};_.Wt.prototype.toString=function(){var a=[],b=this.Di;b&&a.push(cu(b,du,!0),":");var c=this.Lg();if(c||b=="file")a.push("//"),(b=this.wh)&&a.push(cu(b,du,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.yg,c!=null&&a.push(":",String(c));if(c=this.getPath())this.Xd&&c.charAt(0)!="/"&&a.push("/"),a.push(cu(c,c.charAt(0)=="/"?eu:fu,!0));(c=this.Qd.toString())&&a.push("?",c);(c=this.Tz())&&a.push("#",cu(c,gu));return a.join("")};
_.Wt.prototype.resolve=function(a){var b=this.clone(),c=!!a.Di;c?_.Xt(b,a.Di):c=!!a.wh;c?_.Yt(b,a.wh):c=!!a.Xd;c?_.Zt(b,a.Lg()):c=a.yg!=null;var d=a.getPath();if(c)_.$t(b,a.yg);else if(c=!!a.Om){if(d.charAt(0)!="/")if(this.Xd&&!this.Om)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(_.Dc(e,"./")||_.Dc(e,"/.")){d=_.Bc(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];k=="."?d&&h==e.length&&f.push(""):k==".."?((f.length>
1||f.length==1&&f[0]!="")&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.Xq();c?_.au(b,a.Qd.clone()):c=!!a.wG;c&&b.Sk(a.Tz());return b};_.Wt.prototype.clone=function(){return new _.Wt(this)};_.Xt=function(a,b,c){a.Di=c?hu(b,!0):b;a.Di&&(a.Di=a.Di.replace(/:$/,""));return a};_.Yt=function(a,b,c){a.wh=c?hu(b):b;return a};_.Wt.prototype.Lg=function(){return this.Xd};_.Zt=function(a,b,c){a.Xd=c?hu(b,!0):b;return a};
_.$t=function(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("L`"+b);a.yg=b}else a.yg=null;return a};_.Wt.prototype.getPath=function(){return this.Om};_.Wt.prototype.setPath=function(a,b){this.Om=b?hu(a,!0):a;return this};_.Wt.prototype.Xq=function(){return this.Qd.toString()!==""};_.au=function(a,b,c){b instanceof _.bu?(a.Qd=b,a.Qd.OL(a.Rg)):(c||(b=cu(b,iu)),a.Qd=new _.bu(b,a.Rg));return a};_.Wt.prototype.hb=function(a,b){return _.au(this,a,b)};_.Wt.prototype.getQuery=function(){return this.Qd.toString()};
_.ju=function(a,b,c){a.Qd.set(b,c);return a};_.g=_.Wt.prototype;_.g.Ng=function(a){return this.Qd.get(a)};_.g.Tz=function(){return this.wG};_.g.Sk=function(a,b){this.wG=b?hu(a):a;return this};_.g.removeParameter=function(a){this.Qd.remove(a);return this};_.g.OL=function(a){this.Rg=a;this.Qd&&this.Qd.OL(a)};_.ku=function(a,b){return a instanceof _.Wt?a.clone():new _.Wt(a,b)};hu=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
cu=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,lu),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null};lu=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};du=/[#\/\?@]/g;fu=/[#\?:]/g;eu=/[#\?]/g;iu=/[#\?@]/g;gu=/#/g;_.bu=function(a,b){this.Ae=this.Kc=null;this.eg=a||null;this.Rg=!!b};mu=function(a){a.Kc||(a.Kc=new Map,a.Ae=0,a.eg&&Qt(a.eg,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};_.g=_.bu.prototype;
_.g.Yb=function(){mu(this);return this.Ae};_.g.add=function(a,b){mu(this);this.eg=null;a=nu(this,a);var c=this.Kc.get(a);c||this.Kc.set(a,c=[]);c.push(b);this.Ae+=1;return this};_.g.remove=function(a){mu(this);a=nu(this,a);return this.Kc.has(a)?(this.eg=null,this.Ae-=this.Kc.get(a).length,this.Kc.delete(a)):!1};_.g.clear=function(){this.Kc=this.eg=null;this.Ae=0};_.g.isEmpty=function(){mu(this);return this.Ae==0};_.g.Hl=function(a){mu(this);a=nu(this,a);return this.Kc.has(a)};
_.g.forEach=function(a,b){mu(this);this.Kc.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.g.ig=function(){mu(this);for(var a=Array.from(this.Kc.values()),b=Array.from(this.Kc.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.g.Ye=function(a){mu(this);var b=[];if(typeof a==="string")this.Hl(a)&&(b=b.concat(this.Kc.get(nu(this,a))));else{a=Array.from(this.Kc.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
_.g.set=function(a,b){mu(this);this.eg=null;a=nu(this,a);this.Hl(a)&&(this.Ae-=this.Kc.get(a).length);this.Kc.set(a,[b]);this.Ae+=1;return this};_.g.get=function(a,b){if(!a)return b;a=this.Ye(a);return a.length>0?String(a[0]):b};_.g.setValues=function(a,b){this.remove(a);b.length>0&&(this.eg=null,this.Kc.set(nu(this,a),_.Yb(b)),this.Ae+=b.length)};
_.g.toString=function(){if(this.eg)return this.eg;if(!this.Kc)return"";for(var a=[],b=Array.from(this.Kc.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Ye(d);for(var f=0;f<d.length;f++){var h=e;d[f]!==""&&(h+="="+encodeURIComponent(String(d[f])));a.push(h)}}return this.eg=a.join("&")};_.g.clone=function(){var a=new _.bu;a.eg=this.eg;this.Kc&&(a.Kc=new Map(this.Kc),a.Ae=this.Ae);return a};var nu=function(a,b){b=String(b);a.Rg&&(b=b.toLowerCase());return b};
_.bu.prototype.OL=function(a){a&&!this.Rg&&(mu(this),this.eg=null,this.Kc.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.Rg=a};_.bu.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Vt(arguments[b],function(c,d){this.add(d,c)},this)};
var nA=function(a){if(!a||typeof a!=="function")throw new mA("Must provide a function.");this.zg=null;this.t9=a},oA=!1,AA,BA,CA,DA,EA,FA,GA,HA,IA,JA,KA,LA,MA,NA,OA;oA=!1;
var pA=function(a){return new _.lk(function(b){var c=a.length,d=[];if(c)for(var e=function(k,l,m){c--;d[k]=l?{Ez:!0,value:m}:{Ez:!1,reason:m};c==0&&b(d)},f,h=0;h<a.length;h++)f=a[h],_.sk(f,_.bb(e,h,!0),_.bb(e,h,!1));else b(d)})},qA,rA,sA,tA={LP:function(a){qA=a;try{delete tA.LP}catch(b){}},MP:function(a){rA=a;try{delete tA.MP}catch(b){}},NP:function(a){sA=a;try{delete tA.NP}catch(b){}}},uA=function(a){return _.Mx(a.status)},vA=function(){var a=!0,b=_.Gx.Vy();b&&b.withCredentials!==void 0||(a=!1);
return a},wA=function(a,b){if(b==null)return b;b=String(b);b.match(/^\/\/.*/)&&(b=(window.location.protocol=="http:"?"http:":"https:")+b);b.match(/^\/([^\/].*)?$/)&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=window.location.protocol+"//"+window.location.host+b);var c=b.match(/^(https?:)(\/\/)?(\/([^\/].*)?)?$/i);c&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=c[1]+"//"+window.location.host+(c[3]||""));b=b.replace(/^(https?:\/\/[^\/?#@]*)\/$/i,
"$1");b=b.replace(/^(http:\/\/[-_a-z0-9.]+):0*80([\/?#].*)?$/i,"$1$2");b=b.replace(/^(https:\/\/[-_a-z0-9.]+):0*443([\/?#].*)?$/i,"$1$2");b.match(/^https?:\/\/[-_a-z0-9.]*[-_a-z][-_a-z0-9.]*$/i)&&(b=b.toLowerCase());c=_.Ze("client/rewrite");_.ub(c)&&Object.prototype.hasOwnProperty.call(c,b)?b=String(c[b]||b):(b=b.replace(/^(https?):\/\/www\.googleapis\.com$/,"$1://content.googleapis.com"),b=b.replace(/^(https?):\/\/www-(googleapis-[-_a-z0-9]+\.[-_a-z0-9]+\.google\.com)$/,"$1://content-$2"),b.match(/^https?:\/\/content(-[-_a-z0-9.]+)?\.googleapis\.com$/)||
(b=b.replace(/^(https?):\/\/([-_a-z0-9]+(\.[-_a-z0-9]+)?\.googleapis\.com)$/,"$1://content-$2")));a&&(a=_.Ze("client/firstPartyRewrite"),_.ub(a)&&Object.prototype.hasOwnProperty.call(a,b)?b=String(a[b]||b):(b=b.replace(/^(https?):\/\/content\.googleapis\.com$/,"$1://clients6.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.([-a-z0-9]+)\.googleapis\.com$/,"$1://$2-googleapis.$3.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.googleapis\.com$/,"$1://$2.clients6.google.com"),
b=b.replace(/^(https?):\/\/([-a-z0-9]+)-www-googleapis\.([-a-z0-9]+).google.com$/,"$1://content-googleapis-$2.$3.google.com")));return b},mA=function(a){_.jb.call(this,a)};_.y(mA,_.jb);mA.prototype.name="gapix.client.GapiClientError";nA.prototype.then=function(a,b,c){this.zg||(this.zg=this.t9());return this.zg.then(a,b,c)};nA.prototype.jD=function(a){this.zg||(this.zg=a)};
var xA=function(a){var b={},c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=_.mh(a,c);d&&(c=_.lh(c,d))&&_.nh(b,c,d,!0)}return b},yA={error:{code:-1,message:"A network error occurred and the request could not be completed."}},zA=function(a,b,c,d){_.Nx.call(this);this.Hd=a;this.fJ=b;this.Kd=c;a={};if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(b=_.mh(d,e),b!==void 0&&(e=_.kh(e,b))&&_.nh(a,e,b));d={};for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&(d[unescape(encodeURIComponent(f))]=
unescape(encodeURIComponent(a[f])));this.gv=d;this.zg=null};_.y(zA,_.Nx);
zA.prototype.then=function(a){this.zg||(this.zg=(new _.lk(function(b,c){this.ta("error",(0,_.Ab)(function(){c(AA(this))},this));this.ta("success",(0,_.Ab)(function(){b(AA(this))},this));this.send(this.Hd,this.fJ,this.Kd,this.gv)},this)).then(function(b){b.headers=xA(b.headers);return b},function(b){return b.status?(b.headers=xA(b.headers),_.qk(b)):_.qk({result:yA,body:'{"error":{"code":-1,"message":"A network error occurred and the request could not be completed."}}',headers:null,status:null,statusText:null})}));
return this.zg.then.apply(this.zg,arguments)};AA=function(a){var b=a.getStatus(),c=_.Wx(a);var d=b==204?!1:a.Um==""?_.Qf(c):_.Xx(a);var e=a.getAllResponseHeaders();e=_.oh(e,!1);try{var f=_.Vx(a)>2?a.Va.statusText:""}catch(h){f=""}return{result:d,body:c,headers:e,status:b,statusText:f}};BA=/;\s*charset\s*=\s*("utf-?8"|utf-?8)\s*(;|$)/i;CA=/^(text\/[^\s;\/""]+|application\/(json(\+[^\s;\/""]*)?|([^\s;\/""]*\+)?xml))\s*(;|$)/i;DA=/;\s*charset\s*=/i;EA=/(([\r\n]{0,2}[A-Za-z0-9+\/]){4,4}){0,1024}([\r\n]{0,2}[A-Za-z0-9+\/][\r\n]{0,2}[AQgw]([\r\n]{0,2}=){2,2}|([\r\n]{0,2}[A-Za-z0-9+\/]){2,2}[\r\n]{0,2}[AEIMQUYcgkosw048][\r\n]{0,2}=|([\r\n]{0,2}[A-Za-z0-9+\/]){4,4})[\r\n]{0,2}/g;
FA=function(a){var b=[];a=a.replace(EA,function(c){b.push(_.Ny(c));return""});if(a.length)throw Error("va");return b.join("")};GA=function(a){var b=a.headers;if(b&&_.mh(b,"X-Goog-Safety-Encoding")==="base64"){var c=FA(a.body),d=_.mh(b,"X-Goog-Safety-Content-Type");b["Content-Type"]=d;if(d.match(BA)||d.match(CA)&&!d.match(DA))c=_.Eh(c),c=_.Zv(c);_.nh(b,"X-Goog-Safety-Encoding");_.nh(b,"X-Goog-Safety-Content-Type");a.body=c}};
HA=function(a,b,c){c||((c=_.Ze("googleapis.config/proxy"))&&(c=String(c).replace(/\/static\/proxy\.html$/,"")||"/"),c=String(c||""));c||(c=_.Ze("googleapis.config/root"),b&&(c=_.Ze("googleapis.config/root-1p")||c),c=String(c||""));c=String(wA(b,c)||c);return a=_.Lx(c,a)};
IA=function(a,b){var c=a.params||_.Fe();c.url=c.path;var d=c.root;d=HA("/",_.Ji(c.headers),d);d.match(/^(.*[^\/])?\/$/)&&(d=d.substr(0,d.length-1));c.root=d;a.params=c;_.Pi.Zm("makeHttpRequests",[a],function(e,f){e&&e.gapiRequest?(e.gapiRequest.data?GA(e.gapiRequest.data):GA(e),b(e,_.Rf(e))):b(e,f)})};
JA=function(a){var b=_.Mt(a,"params","headers");b&&typeof b==="object"||(b={});a={};for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=_.mh(b,c);d&&(_.kh(c,d),_.nh(a,c,d))}c=(window.location.href.match(_.Oh)[1]||null)=="chrome-extension";a=_.Ji(a);return!(c&&a)&&vA()};
KA=function(a){return new _.lk(function(b,c){var d=function(e){e&&e.gapiRequest?e=e.gapiRequest.data||e:c(e);e={result:e.status!=204&&_.Qf(e.body),body:e.body,headers:e.headers||null,status:e.status||null,statusText:e.statusText||null};uA(e)?b(e):c(e)};try{IA(a,d)}catch(e){c(e)}})};LA=function(a){var b=!_.Ze("client/cors")||!!_.Ze("client/xd4"),c={};_.Bm(a,function(d,e){(d=_.kh(e,d))||b||(d=_.jh(e));d&&(e=_.mh(a,d))&&_.nh(c,d,e)});return c};
MA=function(a){var b=a.params||_.Fe();a=_.Tj(b.headers||{});var c=b.httpMethod||"GET",d=String(b.url||""),e=encodeURIComponent("$unique");if(!(c==="POST"||_.Rt(d,"$unique",d.search(_.St))>=0||_.Rt(d,e,d.search(_.St))>=0)){var f=[];for(h in a)Object.prototype.hasOwnProperty.call(a,h)&&f.push(h.toLowerCase());f.sort();f.push(_.Hg(location.href));var h=f.join(":");f=_.ii();f.update(h);h=f.Ti().toLowerCase().substr(0,7);h=String(parseInt(h,16)%1E3+1E3).substr(1);d=_.Kx(d,e,"gc"+h)}e=b.body||null;h=b.responseType||
null;b=_.Ji(a)||b.authType=="1p";f=!!_.Ze("googleapis.config/auth/useUberProxyAuth")||!!_.Ze("client/withCredentials");_.nh(a,"X-Referer");a=LA(a);var k=new zA(d,c,e,a);k.xh=b||f;h&&(k.Um=h);return new _.lk(function(l,m){k.then(function(n){GA(n);l(n)},function(n){m(n)})})};NA=function(a,b){var c=function(d){d=_.Tj(d);delete d.result;d={gapiRequest:{data:d}};b&&b(d,_.Rf(d))};MA(a).then(c,c)};
OA=function(a,b){(_.Ze("client/cors")||_.Ze("client/xd4"))&&JA(a)?(_.xi(_.wi(),12).rb(),NA(a,b)):(_.xi(_.wi(),11).rb(),IA(a,b))};_.PA={};var QA=function(a){this.qw=a;this.Uf=!1;this.promise={then:(0,_.Ab)(function(b,c,d){this.Uf||(this.Uf=!0);this.ow&&!this.lw?this.qw.resolve(this.ow):this.lw&&!this.ow&&this.qw.reject(this.lw);return this.qw.promise.then(b,c,d)},this)}};QA.prototype.resolve=function(a){this.Uf?this.qw.resolve(a):this.ow||this.lw||(this.ow=a)};QA.prototype.reject=function(a){this.Uf?this.qw.reject(a):this.ow||this.lw||(this.lw=a)};var RA=function(a){a=_.Nt(a.error);return{code:a.code,data:a.errors,message:a.message}},SA=function(a){throw Error("Aa`"+a);};var TA=function(a){nA.call(this,TA.prototype.bp);if(!a||typeof a!="object"&&typeof a!="string")throw new mA("Missing required parameters");if(typeof a==="string"){var b={};b.path=a}else b=a;if(!b.path)throw new mA('Missing required parameter: "path"');this.hh={};this.hh.path=b.path;this.hh.method=b.method||"GET";this.hh.params=b.params||{};this.hh.headers=b.headers||{};this.hh.body=b.body;this.hh.root=b.root;this.hh.responseType=b.responseType;this.hh.apiId=b.apiId;this.xn=b.authType||"auto";this.Oca=
!!b.isXd4;this.WV=!1;this.Kj(this.xn);this.BZ=!1};_.y(TA,nA);TA.prototype.Df=function(){return this.hh};TA.prototype.Kj=function(a){this.xn=a;this.WV=this.xn==="1p"};TA.prototype.Hq=function(){return this.WV};
TA.prototype.Fj=function(){if(!this.BZ){this.BZ=!0;var a=this.hh,b=a.headers=a.headers||{},c=[],d=[];for(h in b)if(Object.prototype.hasOwnProperty.call(b,h)){c.push(h);var e=h,f=_.mh(b,e);f&&(e=_.kh(e,f)||_.jh(e))&&d.push([e,f])}var h=0;for(e=c.length;h<e;++h)delete b[c[h]];c=0;for(h=d.length;c<h;++c)_.nh(b,d[c][0],d[c][1]);if(this.Oca)d=this.xn=="1p";else{d=b;c=String(_.Ze("client/version","1.1.0"));h=String(_.Ze("client/name","google-api-javascript-client"));h=UA[h]===!0?h:"google-api-javascript-client";
e=String(_.Ze("client/appName",""));f=[];e&&(f.push(e),f.push(" "));f.push(h);c&&(f.push("/"),f.push(c));_.nh(d,"X-JavaScript-User-Agent",f.join(""));_.nh(b,"X-Requested-With","XMLHttpRequest");d=_.mh(b,"Content-Type");a.body&&!d&&_.nh(b,"Content-Type","application/json");_.Ze("client/allowExecutableResponse")||_.nh(b,"X-Goog-Encode-Response-If-Executable","base64");(d=_.mh(b,"Content-Type"))&&d.toLowerCase()=="application/json"&&!a.params.alt&&(a.params.alt="json");(d=a.body||null)&&_.ub(d)&&(a.body=
_.Rf(d));a.key=a.id;b=_.Oi(b,void 0,this.xn);d=_.Ji(b);if((c=b)&&window.navigator){h=[];for(e=0;e<VA.length;e++)(f=window.navigator[VA[e]])&&h.push(encodeURIComponent(VA[e])+"="+encodeURIComponent(f));_.nh(c,"X-ClientDetails",h.join("&"))}(c=_.Ze("client/apiKey"))&&a.params.key===void 0&&(a.params.key=c);(c=_.Ze("client/trace"))&&!a.params.trace&&(a.params.trace=c)}this.xn=="auto"&&(d?this.Kj("1p"):(b=_.mh(b,"Authorization"))&&String(b).match(/^(Bearer|MAC)[ \t]/i)?this.Kj("oauth2"):this.Kj("none"));
if((b=String(a.path||"").match(/^(https?:\/\/[^\/?#]+)([\/?#].*)?$/i))&&!a.root)if(a.root=String(b[1]),a.path=String(b[2]||"/"),a.path.match(/^\/_ah\/api(\/.*)?$/))a.root+="/_ah/api",a.path=a.path.substr(8);else{b=_.Ze("googleapis.config/root");d&&(b=_.Ze("googleapis.config/root-1p")||b);b=String(b||"");c=a.root+a.path;if(h=b&&c.substr(0,b.length)===b)h=_.ku(b),e=_.ku(c),h=(!h.Xd&&!e.Xd||h.Lg()==e.Lg())&&(h.yg==null&&e.yg==null||h.yg==e.yg);h&&(a.path=c.substr(b.length),a.root=b)}b=a.params;c=_.ah(a.path);
h=String(_.Ze("googleapis.config/xd3")||"");h.length>=18&&h.substring(h.length-18)=="/static/proxy.html"&&(h=h.substring(0,h.length-18));h||(h="/");e=_.ah(h);if(h!=e)throw Error("x");h.charAt(h.length-1)!="/"&&(h+="/");c=_.Lx(h,c);_.Cj(c,"/")&&(c=c.substring(0,c.length-1));h=_.Fe();for(var k in b)Object.prototype.hasOwnProperty.call(b,k)&&(e=encodeURIComponent(k),h[e]=b[k]);c=_.Sh(c,h);a.path=c;a.root=wA(!!d,a.root);a.url=HA(a.path,!!d,a.root)}};
var WA=function(a){a.Fj();var b=a.hh;return{key:"gapiRequest",params:{id:b.id,key:b.key,url:b.url,path:b.path,httpMethod:b.method,body:b.body||"",headers:b.headers||{},urlParams:{},root:b.root,authType:a.xn}}};_.g=TA.prototype;_.g.execute=function(a){var b=WA(this);OA(b,function(c,d){var e=c;c.gapiRequest&&(e=c.gapiRequest);e&&e.data&&(e=e.data);c=e;c=c instanceof Array?c[0]:c;if(c.status!=204&&c.body)try{var f=_.Qf(c.body)}catch(h){}a&&a(f,d)})};
_.g.bp=function(){var a=WA(this);(_.Ze("client/cors")||_.Ze("client/xd4"))&&JA(a)?(_.xi(_.wi(),15).rb(),a=MA(a)):(_.xi(_.wi(),14).rb(),a=KA(a));return a};_.g.hj=function(){return this.bp()};_.g.Ce=function(){return this.hh.root};_.g.Lv=function(){console.log("makeJsonRpc is not supported for this request.");return{}};_.g.getFormat=function(){return 0};var VA=["appVersion","platform","userAgent"],UA={"google-api-gwt-client":!0,"google-api-javascript-client":!0};TA.prototype.execute=TA.prototype.execute;
TA.prototype.then=TA.prototype.then;TA.prototype.getPromise=TA.prototype.hj;var XA=function(a){if(!a||typeof a!="object")throw new mA("Missing rpc parameters");if(!a.method)throw new mA("Missing rpc method");this.zC=a};_.g=XA.prototype;_.g.Ce=function(){var a=this.zC.transport;return a?a.root||null:null};_.g.execute=function(a){var b=rA();b.add(this,{id:"gapiRpc",callback:this.Jv(a)});b.execute()};
_.g.Lv=function(a){var b=this.zC.method,c=String,d;(d=this.zC.apiVersion)||(d=String(b).split(".")[0],d=_.Ze("googleapis.config/versions/"+b)||_.Ze("googleapis.config/versions/"+d)||"v1",d=String(d));a={jsonrpc:"2.0",id:a,method:b,apiVersion:c(d)};(b=this.zC.rpcParams)&&(a.params=b);return a};
_.g.Jv=function(a){return function(b,c){if(b)if(b.error){var d=b.error;d.error==null&&(d.error=_.Tj(b.error))}else d=b.result||b.data,_.ub(d)&&d.result==null&&(d.result=_.Tj(b.result||b.data));else d=!1;a(d,c)}};_.g.then=function(){throw SA('The "then" method is not available on this object.');};_.g.jD=function(){};_.g.Df=function(){};_.g.Fj=function(){};_.g.Kj=function(){};_.g.Hq=function(){};_.g.hj=function(){};XA.prototype.execute=XA.prototype.execute;var ZA=function(a,b){this.Xe=b||0;this.Xe==2?(b=null,a!=null&&_.ub(a)&&(b={},b.method=a.method,b.rpcParams=a.rpcParams,b.transport=a.transport,b.root=a.root,b.apiVersion=a.apiVersion,b.authType=a.authType),this.Sb=new XA(b)):(this.Xe==0&&(b=a&&a.callback)&&(a.callback=YA(b)),b=null,a!=null&&(_.ub(a)?(b={},b.path=a.path,b.method=a.method,b.params=a.params,b.headers=a.headers,b.body=a.body,b.root=a.root,b.responseType=a.responseType,b.authType=a.authType,b.apiId=a.apiId):typeof a==="string"&&(b=a)),
this.Sb=new TA(b))},YA=function(a){return function(b){if(b!=null&&_.ub(b)&&b.error){var c=RA(b);b=_.Rf([{id:"gapiRpc",error:c}]);c.error=_.Nt(c)}else b==null&&(b={}),c=_.Nt(b),c.result=_.Nt(b),b=_.Rf([{id:"gapiRpc",result:b}]);a(c,b)}};_.g=ZA.prototype;_.g.getFormat=function(){return this.Xe};_.g.execute=function(a){this.Sb.execute(a&&this.Xe==1?YA(a):a)};_.g.then=function(a,b,c){return this.Sb.then(a,b,c)};_.g.jD=function(a){this.Sb.jD(a)};_.g.Df=function(){return this.Sb.Df()};_.g.Fj=function(){this.Sb.Fj()};
_.g.Ce=function(){return this.Sb.Ce()};_.g.Lv=function(a){if(this.Sb.Lv)return this.Sb.Lv(a)};_.g.Kj=function(a){this.Sb.Kj(a)};_.g.Hq=function(){return!!this.Sb.Hq()};_.g.hj=function(){return this.Sb.hj()};ZA.prototype.execute=ZA.prototype.execute;ZA.prototype.then=ZA.prototype.then;ZA.prototype.getPromise=ZA.prototype.hj;var $A=/<response-(.*)>/,aB=/^application\/http(;.+$|$)/,bB=["clients6.google.com","content.googleapis.com","www.googleapis.com"],cB=function(a,b){a=_.mh(a,b);if(!a)throw new mA("Unable to retrieve header.");return a},dB=function(a){var b=void 0;a=_.za(a);for(var c=a.next();!c.done;c=a.next()){c=c.value.Df().apiId;if(typeof c!=="string")return"batch";if(b===void 0)b=c;else if(b!=c)return"batch"}b=_.Ze("client/batchPath/"+b)||"batch/"+b.split(":").join("/");return String(b)},eB=function(a){a=a.map(function(b){return b.request});
return dB(a)},fB=function(a,b){var c=[];a=a.Df();var d=function(f,h){_.Bm(f,function(k,l){h.push(l+": "+k)})},e={"Content-Type":"application/http","Content-Transfer-Encoding":"binary"};e["Content-ID"]="<"+b+">";d(e,c);c.push("");c.push(a.method+" "+a.path);d(a.headers,c);c.push("");a.body&&c.push(a.body);return c.join("\r\n")},iB=function(a,b){a=gB(a,b);var c={};_.Zb(a,function(d,e){c[e]=hB(d,e)});return c},hB=function(a,b){return{result:a.result||a.body,rawResult:_.Rf({id:b,result:a.result||a.body}),
id:b}},gB=function(a,b){a=_.Ec(a);_.Cj(a,"--")&&(a=a.substring(0,a.length-2));a=a.split(b);b=_.Fe();for(var c=0;c<a.length;c++)if(a[c]){var d;if(d=a[c]){_.Cj(d,"\r\n")&&(d=d.substring(0,d.length-2));if(d){d=d.split("\r\n");for(var e=0,f={headers:{},body:""};e<d.length&&d[e]=="";)e++;for(f.outerHeaders=jB(d,e);e<d.length&&d[e]!="";)e++;e++;var h=d[e++].split(" ");f.status=Number(h[1]);f.statusText=h.slice(2).join(" ");for(f.headers=jB(d,e);e<d.length&&d[e]!="";)e++;e++;f.body=d.slice(e).join("\r\n");
GA(f);d=f}else d=null;e=_.Fe();f=cB(d.outerHeaders,"Content-Type");if(aB.exec(f)==null)throw new mA("Unexpected Content-Type <"+f+">");f=cB(d.outerHeaders,"Content-ID");f=$A.exec(f);if(!f)throw new mA("Unable to recognize Content-Id.");e.id=decodeURIComponent(f[1].split("@")[0].replace(/^.*[+]/,""));e.response={status:d.status,statusText:d.statusText,headers:d.headers};d.status!=204&&(e.response.body=d.body,e.response.result=_.Qf(d.body));d=e}else d=null;d&&d.id&&(b[d.id]=d.response)}return b},jB=
function(a,b){for(var c=[];b<a.length&&a[b];b++)c.push(a[b]);return _.oh(c.join("\r\n"),!1)},kB=function(a,b,c){a=a||b;if(!a||_.ku(a).Di!=="https")if(a=c?_.Ze("googleapis.config/root-1p"):_.Ze("googleapis.config/root"),!a)return!1;a=wA(c,String(a))||a;return bB.includes(_.ku(a).Lg())};var lB=function(a){nA.call(this,lB.prototype.bp);this.mk={};this.uy={};this.Sm=[];this.Sd=a;this.oda=!!a;this.PU=this.DA=!1};_.y(lB,nA);var mB=function(a,b){a=_.za(Object.values(a.mk));for(var c=a.next();!c.done;c=a.next())if(c.value.map(function(d){return d.id}).includes(b))return!0;return!1};lB.prototype.Zp=function(a){(function(b){setTimeout(function(){throw b;})})(a)};
lB.prototype.add=function(a,b){var c=b||_.Fe();b=_.Fe();if(!a)throw new mA("Batch entry "+(_.Ge(c,"id")?'"'+c.id+'" ':"")+"is missing a request method");a.Fj();b.request=a;var d=_.vk();d=new QA(d);b.sC=d;a.jD(b.sC.promise);d=a.Df().headers;_.Ji(d)&&(this.DA=!0);(d=String((d||{}).Authorization||"")||null)&&d.match(/^Bearer|MAC[ \t]/i)&&(this.PU=!0);d=a.Df().root;if(!this.oda){if(d&&this.Sd&&d!=this.Sd)throw new mA('The "root" provided in this request is not consistent with that of existing requests in the batch.');
this.Sd=d||this.Sd}if(_.Ge(c,"id")){d=c.id;if(mB(this,d))throw new mA('Batch ID "'+d+'" already in use, please use another.');b.id=d}else{do b.id=String(Math.round(2147483647*_.Fi()));while(mB(this,b.id))}b.callback=c.callback;c="batch";kB(this.Sd,a.Df().path,this.DA)&&(c=eB([b]));this.mk[c]=this.mk[c]||[];this.mk[c].push(b);this.uy[b.id]=b;return b.id};
var nB=function(a){var b=[],c=kB(a.Sd,void 0,a.DA);Object.entries(a.mk).length>1&&_.Vf.warn("Heterogeneous batch requests are deprecated. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");for(var d=_.za(Object.entries(a.mk)),e=d.next();!e.done;e=d.next()){e=_.za(e.value);var f=e.next().value;e=e.next().value;for(var h=!0,k=_.za(e),l=k.next();!l.done;l=k.next())l=l.value,l.request.Fj(),f==="batch"&&c&&(h=!1,l.Gca=!0,l.request.Df.root=a.Sd,b.push(l.request),
a.Sm.push([l]));if(h){var m=e;f=a.Sd;h=a.DA;k=a.PU;l="batch"+String(Math.round(2147483647*_.Fi()))+String(Math.round(2147483647*_.Fi()));var n="--"+l;l="multipart/mixed; boundary="+l;for(var p={path:eB(m),method:"POST"},q=[],t=0;t<m.length;t++)q.push(fB(m[t].request,[n.substr(n.indexOf("--")+2),"+",encodeURIComponent(m[t].id).split("(").join("%28").split(")").join("%29").split(".").join("%2E"),"@googleapis.com"].join("")));p.body=[n,q.join("\r\n"+n+"\r\n"),n+"--"].join("\r\n")+"\r\n";p.root=f||null;
_.Ze("client/xd4")&&vA()?(p.isXd4=!0,p.params={$ct:l},p.headers={},_.nh(p.headers,"Content-Type","text/plain; charset=UTF-8"),h?p.authType="1p":k&&(p.authType="oauth2"),f=new TA(p)):(p.headers={},_.nh(p.headers,"Content-Type",l),f=sA(p));b.push(f);a.Sm.push(e)}}return b};
lB.prototype.execute=function(a){if(!(Object.keys(this.mk).length<1)){var b=this.Jv(a);a=nB(this);var c=[],d=a.map(function(e){return new _.lk(function(f){try{e.execute(function(h,k){return f({HP:h,Sea:k})})}catch(h){c.push(h),f({HP:{Ez:!1,reason:h}})}})});if(c.length>0&&c.length===a.length)throw c[0];_.tk(d).then(function(e){var f=e.map(function(h){return h.Sea});e=e.map(function(h){return h.HP});b(e,f)})}};
lB.prototype.bp=function(){var a=this;if(Object.keys(this.mk).length<1)return _.pk({});var b=nB(this).map(function(c){return new _.lk(function(d,e){return c.hj().then(d,e)})});return pA(b).then(function(c){c=c.map(function(d){return d.Ez?d.value:d});return oB(a,c,!0)})};
lB.prototype.uY=function(a,b,c,d){var e={};if(c){e=b?gB:iB;b=cB(a.headers,"Content-Type").split("boundary=")[1];if(!b)throw new mA("Boundary not indicated in response.");e=e(a.body,"--"+b)}else b?(a.result=_.Qf(a.body),e[d]=a):e[d]=hB(a,d);a={};e=_.za(Object.entries(e));for(b=e.next();!b.done;b=e.next())if(c=_.za(b.value),b=c.next().value,c=c.next().value,a[b]=c,!this.uy[b])throw new mA("Could not find batch entry for id "+b+".");return a};
var oB=function(a,b,c,d,e){for(var f=!1,h={},k,l=0,m=0;m<b.length;m++){var n=b[m];if(n&&Object.keys(n).includes("fulfilled")&&n.Ez===!1){l++;b[m]=n.reason;n=pB([b[m]]);for(var p=_.za(a.Sm[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=n}else{if(a.Sm[m].length<1)throw new mA("Error processing batch responses.");try{var t=!(a.Sm[m].length===1&&a.Sm[m][0].Gca),v=a.Sm[m][0].id;if(!c){p=n;q=t;var u=d[m],x=p;if(u&&(!x||!q)){var A=_.Qf(u);A&&(x=A.gapiRequest?A.gapiRequest.data:A,!q&&p&&(x.body=p))}if(!x)throw new mA("The batch response is missing.");
n=x}p=void 0;if(q=n){var C=q.headers;if(C){var D=_.Fe();for(p in C)if(Object.prototype.hasOwnProperty.call(C,p)){var P=_.mh(C,p);_.nh(D,p,P,!0)}q.headers=D}}if(t&&cB(n.headers,"Content-Type").indexOf("multipart/mixed")!=0)throw new mA("The response's Content-Type is not multipart/mixed.");k=k||_.Nt(n);var F=uA(n);F&&!uA(k)&&(k.status=n.status,k.statusText=n.statusText);if(F||c||!t)f=!0,h=Object.assign(h,a.uY(n,c,t,v))}catch(R){for(l++,b[m]=R,n=pB([R]),p=_.za(a.Sm[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=
n}}}if(l===b.length){d=pB(b);h=_.Rf(d);k=0;a=Array.from(Object.values(a.mk)).flat();f=_.za(a);for(l=f.next();!l.done;l=f.next())if(l=l.value,c)l.sC.reject(d);else if(l.callback)try{k++,l.callback(d,h)}catch(R){lB.prototype.Zp(R)}if(e)try{e(d,h)}catch(R){lB.prototype.Zp(R)}else if(k!==a.length)throw b.length===1?b[0]:d;}else{if(f)for(f=_.za(Object.entries(h)),l=f.next();!l.done;l=f.next())if(l=_.za(l.value),m=l.next().value,l=l.next().value,c)m=a.uy[m],l&&uA(l)?m.sC.resolve(l):m.sC.reject(l);else if(m=
a.uy[m],m.callback){if(l&&l.rawResult)try{delete l.rawResult}catch(R){}try{m.callback(l||!1,_.Rf(l))}catch(R){lB.prototype.Zp(R)}}k.result=h||{};k.body=b.length===1?k.body:"";if(e)try{e(h||null,d.length===1?d[0]:null)}catch(R){lB.prototype.Zp(R)}return k}},pB=function(a){var b={error:{code:0,message:"The batch request could not be fulfilled.  "}};a=_.za(a);for(var c=a.next();!c.done;c=a.next())(c=c.value)&&c.message||c instanceof Error&&c.message?b.error.message+=(c.message||c instanceof Error&&c.message)+
"  ":c&&c.error&&c.error.message&&(b.error.message+=c.error.message+"  ",b.error.code=c.error.code||b.error.code||0);b.error.message=b.error.message.trim();return{result:b,body:_.Rf(b),headers:null,status:null,statusText:null}};lB.prototype.Jv=function(a){var b=this;return function(c,d){b.WE(c,d,a)}};lB.prototype.WE=function(a,b,c){oB(this,a,!1,b,c)};lB.prototype.add=lB.prototype.add;lB.prototype.execute=lB.prototype.execute;lB.prototype.then=lB.prototype.then;var qB=function(){this.Pl=[];this.Sd=this.mf=null};
qB.prototype.add=function(a,b){b=b||{};var c={},d=Object.prototype.hasOwnProperty;if(a)c.lp=a;else throw new mA("Batch entry "+(d.call(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");if(d.call(b,"id")){a=b.id;for(d=0;d<this.Pl.length;d++)if(this.Pl[d].id==a)throw new mA('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(2147483647*_.Fi()|0);while(d.call(this.Pl,c.id))}c.callback=b.callback;this.Pl.push(c);return c.id};
var rB=function(a){return function(b){var c=b.body;if(b=b.result){for(var d={},e=b.length,f=0;f<e;++f)d[b[f].id]=b[f];a(d,c)}else a(b,c)}};
qB.prototype.execute=function(a){this.mf=[];for(var b,c,d=0;d<this.Pl.length;d++)b=this.Pl[d],c=b.lp,this.mf.push(c.Lv(b.id)),this.Sd=c.Ce()||this.Sd;c=this.Jv(a);a={requests:this.mf,root:this.Sd};b={};d=a.headers||{};for(var e in d){var f=e;if(Object.prototype.hasOwnProperty.call(d,f)){var h=_.mh(d,f);h&&(f=_.kh(f,h)||_.jh(f))&&_.nh(b,f,h)}}_.nh(b,"Content-Type","application/json");e=rB(c);sA({method:"POST",root:a.root||void 0,path:"/rpc",params:a.urlParams,headers:b,body:a.requests||[]}).then(e,
e)};qB.prototype.Jv=function(a){var b=this;return function(c,d){b.WE(c,d,a)}};qB.prototype.WE=function(a,b,c){a||(a={});for(var d=0;d<this.Pl.length;d++){var e=this.Pl[d];e.callback&&e.callback(a[e.id]||!1,b)}c&&c(a,b)};tA.MP(function(){return new qB});qB.prototype.add=qB.prototype.add;qB.prototype.execute=qB.prototype.execute;var sB=function(a,b){this.nea=a;this.Xe=b||null;this.xf=null};sB.prototype.kI=function(a){this.Xe=a;this.xf=this.Xe==2?new qB:new lB(this.nea)};sB.prototype.add=function(a,b){if(!a)throw a=b||_.Fe(),new mA("Batch entry "+(_.Ge(a,"id")?'"'+a.id+'" ':"")+"is missing a request method");this.Xe===null&&this.kI(a.getFormat());this.Xe!==a.getFormat()&&SA("Unable to add item to batch.");var c=b&&b.callback;this.Xe==1&&c&&(b.callback=function(d){d=tB(d);var e=_.Rf([d]);c(d,e)});return this.xf.add(a,b)};
sB.prototype.execute=function(a){var b=a&&this.Xe==1?function(c){var d=[];_.Bm(c,function(f,h){f=tB(f);c[h]=f;d.push(f)});var e=_.Rf(d);a(c,e)}:a;this.xf&&this.xf.execute(b)};var tB=function(a){var b=a?_.Mt(a,"result"):null;_.ub(b)&&b.error!=null&&(b=RA(b),a={id:a.id,error:b});return a};sB.prototype.then=function(a,b,c){this.Xe==2&&SA('The "then" method is not available on this object.');return this.xf.then(a,b,c)};sB.prototype.add=sB.prototype.add;sB.prototype.execute=sB.prototype.execute;
sB.prototype.then=sB.prototype.then;var uB=function(a){nA.call(this,uB.prototype.bp);this.Sb=a;this.cQ=!1};_.y(uB,nA);var vB=function(a){a.Sb.Fj();var b=a.Sb,c=b.Df();return!(kB(c.root,c.path,a.Sb.Hq())?dB([b])!=="batch":1)};_.g=uB.prototype;
_.g.execute=function(a){var b=this;this.cQ=!0;if(vB(this))this.Sb.execute(a);else{_.xi(_.wi(),13).rb();var c=function(d){if(typeof a==="function"){var e={gapiRequest:{data:{status:d&&d.status,statusText:d&&d.statusText,headers:d&&d.headers,body:d&&d.body}}};if(b.getFormat()===1){a=YA(a);var f={}}var h=d?d.result:!1;d&&d.status==204&&(h=f,delete e.gapiRequest.data.body);a(h,_.Rf(e))}};this.hj().then(c,c)}};
_.g.bp=function(){if(vB(this))return this.Sb.hj();this.cQ||_.xi(_.wi(),16).rb();return new _.lk(function(a,b){var c=qA(),d=c.add(this.Sb,{id:"gapiRequest"});c.then(function(e){var f=e.result;if(f&&(f=f[d])){Object.prototype.hasOwnProperty.call(f,"result")||(f.result=!1);Object.prototype.hasOwnProperty.call(f,"body")||(f.body="");uA(f)?a(f):b(f);return}b(e)},b)},this)};_.g.Df=function(){if(this.Sb.Df)return this.Sb.Df()};_.g.Fj=function(){this.Sb.Fj&&this.Sb.Fj()};_.g.Ce=function(){if(this.Sb.Ce)return this.Sb.Ce()};
_.g.Kj=function(a){this.Sb.Kj&&this.Sb.Kj(a)};_.g.Hq=function(){return this.Sb.Hq()};_.g.getFormat=function(){return this.Sb.getFormat?this.Sb.getFormat():0};_.g.hj=function(){return this.bp()};uB.prototype.execute=uB.prototype.execute;uB.prototype.then=uB.prototype.then;uB.prototype.getPromise=uB.prototype.hj;var wB="/rest?fields="+encodeURIComponent("kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id")+"&pp=0",xB=function(a,b){return"/discovery/v1/apis/"+(encodeURIComponent(a)+"/"+encodeURIComponent(b)+wB)},zB=function(a,b,c,d){if(_.ub(a)){var e=a;var f=a.name;a=a.version||"v1"}else f=a,a=b;if(!f||!a)throw new mA("Missing required parameters.");var h=c||function(){},k=_.ub(d)?d:{};c=function(l){var m=l&&l.result;if(!m||m.error||!m.name||!l||l.error||l.message||l.message)h(m&&
m.error?m:l&&(l.error||l.message||l.message)?l:new mA("API discovery response missing required fields."));else{l=k.root;l=m.rootUrl!=null?String(m.rootUrl):l;l=typeof l==="string"?l.replace(/([^\/])\/$/,"$1"):void 0;k.root=l;m.name&&m.version&&!m.id&&(m.id=[m.name,m.version].join(":"));m.id&&(k.apiId=m.id,l="client/batchPath/"+m.id,m.batchPath&&!_.Ze(l)&&_.$e(l,m.batchPath));var n=m.servicePath,p=m.parameters,q=function(v){_.Bm(v,function(u){if(!(u&&u.id&&u.path&&u.httpMethod))throw new mA("Missing required parameters");
var x=u.id.split("."),A=window.gapi.client,C;for(C=0;C<x.length-1;C++){var D=x[C];A[D]=A[D]||{};A=A[D]}var P,F;k&&(k.hasOwnProperty("root")&&(P=k.root),k.hasOwnProperty("apiId")&&(F=k.apiId));D=window.gapi.client[x[0]];D.cO||(D.cO={servicePath:n||"",parameters:p,apiId:F});x=x[C];A[x]||(A[x]=_.bb(yB,{path:typeof u.path==="string"?u.path:null,httpMethod:typeof u.httpMethod==="string"?u.httpMethod:null,parameters:u.parameters,parameterName:(u.request||{}).parameterName||"",request:u.request,root:P},
D.cO))})},t=function(v){_.Bm(v,function(u){q(u.methods);t(u.resources)})};t(m.resources);q(m.methods);h.call()}};e?c({result:e}):f.indexOf("://")>0?sA({path:f,params:{pp:0,fields:("/"+f).indexOf("/discovery/v1/apis/")>=0?"kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id":'fields["kind"],fields["name"],fields["version"],fields["rootUrl"],fields["servicePath"],fields["resources"],fields["parameters"],fields["methods"],fields["batchPath"],fields["id"]'}}).then(c,c):sA({path:xB(f,
a),root:d&&d.root}).then(c,c)},yB=function(a,b,c,d,e){e=e===void 0?{}:e;var f=b.servicePath||"";_.Bc(f,"/")||(f="/"+f);var h=AB(a.path,[a.parameters,b.parameters],c||{});c=h.Dd;var k=h.Pha;f=_.Lx(f,h.path);h=k.root;delete k.root;var l=a.parameterName;!l&&_.Ix(k)==1&&k.hasOwnProperty("resource")&&(l="resource");if(l){var m=k[l];delete k[l]}m==null&&(m=d);m==null&&a.request&&(_.Ch(k)&&(k=void 0),m=k);e=e||{};l=a.httpMethod;l=="GET"&&m!==void 0&&String(m)!=""&&(_.nh(e,"X-HTTP-Method-Override",l),l="POST");
if((m==null||d!=null)&&k)for(var n in k)typeof k[n]==="string"&&(c[n]=k[n]);return sA({path:f,method:l,params:c,headers:e,body:m,root:h||a.root,apiId:b.apiId},1)},AB=function(a,b,c){c=_.Tj(c);var d={};_.Am(b,function(e){_.Bm(e,function(f,h){var k=f.required;if(f.location=="path")if(Object.prototype.hasOwnProperty.call(c,h))_.Dc(a,"{"+h+"}")?(f=encodeURIComponent(String(c[h])),a=a.replace("{"+h+"}",f)):_.Dc(a,"{+"+h+"}")&&(f=encodeURI(String(c[h])),a=a.replace("{+"+h+"}",f)),delete c[h];else{if(k)throw new mA("Required path parameter "+
h+" is missing.");}else f.location=="query"&&Object.prototype.hasOwnProperty.call(c,h)&&(d[h]=c[h],delete c[h])})});if(b=c.trace)d.trace=b,delete c.trace;return{path:a,Dd:d,Pha:c}};var BB=function(a,b,c,d){var e=b||"v1",f=_.ub(d)?d:{root:d};if(c)zB(a,e,function(h){if(h)if(h.error)c(h);else{var k="API discovery was unsuccessful.";if(h.message||h.message)k=h.message||h.message;c({error:k,code:0})}else c()},f);else return new _.lk(function(h,k){var l=function(m){m?k(m):h()};try{zB(a,e,l,f)}catch(m){k(m)}})},CB=new RegExp(/^((([Hh][Tt][Tt][Pp][Ss]?:)?\/\/[^\/?#]*)?\/)?/.source+/(_ah\/api\/)?(batch|rpc)(\/|\?|#|$)/.source),DB=function(a,b){if(!a)throw new mA("Missing required parameters");
var c=typeof a==="object"?a:{path:a};a=c.callback;delete c.callback;b=new ZA(c,b);if(c=!!_.Ze("client/xd4")&&vA()){var d=b.Df();c=d.path;(d=d.root)&&d.charAt(d.length-1)!=="/"&&(d+="/");d&&c&&c.substr(0,d.length)===d&&(c=c.substr(d.length));c=!c.match(CB)}c&&(b=new uB(b));return a?(b.execute(a),null):b};tA.NP(function(a){return DB.apply(null,arguments)});
var EB=function(a,b){if(!a)throw new mA("Missing required parameters");for(var c=a.split("."),d=window.gapi.client,e=0;e<c.length-1;e++){var f=c[e];d[f]=d[f]||{};d=d[f]}c=c[c.length-1];if(!d[c]){var h=b||{};d[c]=function(k){var l=typeof h=="string"?h:h.root;k&&k.root&&(l=k.root);return new ZA({method:a,apiVersion:h.apiVersion,rpcParams:k,transport:{name:"googleapis",root:l}},2)}}},FB=function(a){return new sB(a)};tA.LP(function(a){return FB.apply(null,arguments)});
var GB=function(a){if(_.PA.JSONRPC_ERROR_MOD)throw new mA(a+" is discontinued. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");_.Vf.log(a+" is deprecated. See https://developers.google.com/api-client-library/javascript/reference/referencedocs")};_.r("gapi.client.init",function(a){a.apiKey&&_.$e("client/apiKey",a.apiKey);var b=_.Fb(a.discoveryDocs||[],function(d){return BB(d)});if((a.clientId||a.client_id)&&a.scope){var c=new _.lk(function(d,e){var f=function(){_.Xa.gapi.auth2.init.call(_.Xa.gapi.auth2,a).then(function(){d()},e)};oA?f():_.Xa.gapi.load("auth2",{callback:function(){f()},onerror:function(h){e(h||Error("Ba"))}})});b.push(c)}else(a.clientId||a.client_id||a.scope)&&_.Vf.log("client_id and scope must both be provided to initialize OAuth.");
return _.tk(b).then(function(){})});_.r("gapi.client.load",BB);_.r("gapi.client.newBatch",FB);_.r("gapi.client.newRpcBatch",function(){GB("gapi.client.newRpcBatch");return FB()});_.r("gapi.client.newHttpBatch",function(a){GB("gapi.client.newHttpBatch");return new sB(a,0)});_.r("gapi.client.register",function(a,b){GB("gapi.client.register");var c;b&&(c={apiVersion:b.apiVersion,root:b.root});EB(a,c)});_.r("gapi.client.request",DB);
_.r("gapi.client.rpcRequest",function(a,b,c){GB("gapi.client.rpcRequest");if(!a)throw new mA('Missing required parameter "method".');return new ZA({method:a,apiVersion:b,rpcParams:c,transport:{name:"googleapis",root:c&&c.root||""}},2)});_.r("gapi.client.setApiKey",function(a){_.$e("client/apiKey",a);_.$e("googleapis.config/developerKey",a)});_.r("gapi.client.setApiVersions",function(a){GB("gapi.client.setApiVersions");_.$e("googleapis.config/versions",a)});_.r("gapi.client.getToken",function(a){return _.qi(a)});
_.r("gapi.client.setToken",function(a,b){a?_.aw(a,b):_.bw(b)});_.r("gapi.client.AuthType",{Cia:"auto",NONE:"none",pma:"oauth2",wka:"1p"});_.r("gapi.client.AuthType.AUTO","auto");_.r("gapi.client.AuthType.NONE","none");_.r("gapi.client.AuthType.OAUTH2","oauth2");_.r("gapi.client.AuthType.FIRST_PARTY","1p");
});
// Google Inc.
